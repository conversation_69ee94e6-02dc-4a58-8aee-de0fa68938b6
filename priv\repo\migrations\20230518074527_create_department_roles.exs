defmodule App.Repo.Migrations.CreateSystemRoles do
  use Ecto.Migration

  def change do
    create table(:department_roles) do
      add :name, :string, size: 50, null: false
      add :description, :string, size: 255, null: false
      add :system_gen, :boolean, default: false, null: false
      add :editable, :boolean, default: true, null: false
      add :user_interface, :boolean, default: true, null: false
      add :status, :integer, default: 1, null: false
      add :deleted_at, :naive_datetime
      add :created_by, references(:tbl_users, on_delete: :nothing)
      add :updated_by, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create unique_index(:department_roles, [:name])
    create index(:department_roles, [:created_by])
    create index(:department_roles, [:updated_by])
  end
end
