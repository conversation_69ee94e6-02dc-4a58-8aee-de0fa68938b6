defmodule AppWeb.LicenceStepMappingLive.FormComponent do
  use AppWeb, :live_component
  alias App.Registration
  # alias App.ApplicationSteps.LicenceStepMapping
  # alias App.Service.Table.Registration.Page.Functions, as: PageFunctions

  # Render form
  @impl true
  @spec render(any()) :: Phoenix.LiveView.Rendered.t()
  def render(assigns) do
    ~H"""
    <div>
      <.simple_form
        for={@form}
        id="page-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-3 gap-4">
          <.input
            field={@form[:number]}
            type="text"
            placeholder="Enter a number"
            required
            label={raw(~c"Name <span class='text-rose-500'>*</span>")}
          />

          <.input
            field={@form[:name]}
            type="text"
            placeholder="Enter a name"
            required
            label={raw(~c"Name <span class='text-rose-500'>*</span>")}
          />

          <.input
            field={@form[:license_name]}
            type="text"
            placeholder="Enter a license name"
            required
            label={raw(~c"Name <span class='text-rose-500'>*</span>")}
          />
        </div>
        <:actions>
          <div class="align-left">
            <.button type="button" phx-click="close_model">Close</.button>

            <.button type="submit" phx-disable-with="submitting...">Submit</.button>
          </div>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{action: action} = assigns, socket) do
    c_mount(action, assigns, socket)
  end

  defp c_mount( :edit, %{application_steps: application_steps, current_user: current_user} = assigns, socket ) do
   changeset = Registration.change_application_steps(application_steps)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:license_name, @license_name)
     |> assign(:name, @name)
     |> assign(:number, @number)
     |> assign(:current_user, current_user)
     |> assign_form(changeset)}
  end

  defp c_mount( :new, %{application_steps: application_steps, current_user: current_user} = assigns, socket ) do
    changeset = Registration.change_application_steps(application_steps)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:license_name, @license_name)
     |> assign(:name, @name)
     |> assign(:number, @number)
     |> assign(:current_user, current_user)
     |> assign_form(changeset)}
  end

  @impl true
  def handle_event("validate", %{"application_steps" => params}, socket) do
    changeset =
      socket.assigns.application_steps
      |> Registration.change_application_steps(params)
      |> Map.put(:action, :validate)

    socket
    |> assign_form(changeset)
    |> noreply()
  end

  def handle_event("save", %{"application_steps" => params}, socket) do
    save_application_steps(socket, socket.assigns.action, params)
    |> noreply()
  end

  defp save_application_steps(socket, :edit, params) do
    case Registration.update_application_steps(socket.assigns.application_steps, params) do
      {:ok, application_steps} ->
        notify_parent({:saved, application_steps, "Application Step Updated Successfully"})
        socket

      {:error, %Ecto.Changeset{} = changeset} ->
        assign_form(socket, changeset)
    end
  end

  defp save_application_steps(socket, :new, params) do
    case Registration.create_application_steps_data(socket, params) do
      {:ok, %{create_application_steps: application_steps}} ->
        notify_parent({:saved, application_steps, "Application Step Created Successfully"})
        socket

      {:error, _name, %Ecto.Changeset{} = changeset, _map} ->
        assign_form(socket, changeset)
    end
  end

  defp notify_parent(msg) do
    send(self(), {__MODULE__, msg})
  end
end
