export const soundControl = async (url = "/media/sound/messagebox.mp3", loop = false, volume = 1.0) => {
    try {
        const audio = new Audio(url);
        audio.volume = Math.min(Math.max(volume, 0), 1);
        audio.loop = loop;
        await audio.play();
        return audio;
    } catch (error) {
        console.error('Error playing sound:', error);
        return null;
    }
}

let audioElement = null;

export const BackGroundSound = {
    mounted() {
        try {
            audioElement = new Audio(this.el.dataset.sound);
            audioElement.volume = 0.5; // Default background volume
            audioElement.addEventListener('error', (e) => console.error('Audio error:', e));
            audioElement.load();
            audioElement.play().catch(e => console.error('Playback failed:', e));
        } catch (error) {
            console.error('Error initializing background sound:', error);
        }
    },
    
    updated() {
        if (this.el.dataset.end === "1" && audioElement) {
            audioElement.pause();
        }
    },
    
    destroyed() {
        if (audioElement) {
            audioElement.pause();
            audioElement.src = '';
            audioElement = null;
        }
    }
}
