defmodule App.LicensesFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `App.Licenses` context.
  """

  @doc """
  Generate a license.
  """
  def license_fixture(attrs \\ %{}) do
    {:ok, license} =
      attrs
      |> Enum.into(%{
        form_number: 42,
        name: "some name",
        status: 42
      })
      |> App.Licenses.create_license()

    license
  end
end
