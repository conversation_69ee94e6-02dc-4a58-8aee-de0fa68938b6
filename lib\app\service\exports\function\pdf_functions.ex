defmodule App.Service.Export.PdfFunctions do
  @moduledoc false
  alias AppWeb.Endpoint
  @extension ".pdf"
  alias App.Service.Export.{
    Functions,
    UserServicePdf,
    ContactPersonServicePdf,
    ClientProfileServicePdf,
    SenderServicePdf,
    PurchasesServicePdf,
    RatesServicePdf,
    PdfErrorServicePdf,
    PdfApiLogService,
    PdfSessionLogService,
    PdfSystemLogService,
    SelfRegistrationApplicationsServicePdf,
    PdfMonthlyStatisticalServicePdf,
    PaymentPlansServicePdf,
    PrepaidClientStatementServicePdf,
    PostpaidClientStatementServicePdf,
    ApiServicesServicePdf,
    PdfSmppServicesService,
    DepartmentRolesManagementServicePdf,
    AccessRolesPdf,
    AdminRoleAccessPdf,
    AdminAccessRoleNoneRoleUserAccessPdf,
    ServiceProviderReportsPdf,
    PdfSmsLogsService,
    ClientStatisticsReportsPdf,
    PdfSmsLogsAchieve,
    PdfTransactionReportsService,
    PdfAnnualStaticsticsService
  }

  def annual_report_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Annual Statistical Report in Pdf",
      "annual_statistical_report",
      @extension,
      &PdfAnnualStaticsticsService.index/2
    )
  end

  def transaction_reports_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Transaction Reports in Pdf",
      "transaction_reports",
      @extension,
      &PdfTransactionReportsService.index/2
    )
  end

  def sms_logs_archieve(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Sms Logs Archieve in Pdf",
      "sms_logs_archieve",
      @extension,
      &PdfSmsLogsAchieve.index/2
    )
  end

  def client_statistics_report(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Client Statistics in Pdf",
      "client_statistics",
      @extension,
      &ClientStatisticsReportsPdf.index/2
    )
  end

  def sms_logs_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Sms Logs Service  in Pdf",
      "Sms Logs Service",
      @extension,
      &PdfSmsLogsService.index/2
    )
  end

  def service_provider_report(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Service Provider Reports in Pdf",
      "service_provider_reports",
      @extension,
      &ServiceProviderReportsPdf.index/2
    )
  end

  def admin_access_role_none_role_user_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for Admin Access Role for None Role User in Pdf",
      "Admin Access Role None Role User",
      @extension,
      &AdminAccessRoleNoneRoleUserAccessPdf.index/1
    )
  end

  def admin_access_role_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for Admin Access Role Management in Pdf",
      "Admin Access Role Management",
      @extension,
      &AdminRoleAccessPdf.index/1
    )
  end

  def access_roles_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for Access Roles Management in Pdf",
      "Access Roles Management",
      @extension,
      &AccessRolesPdf.index/1
    )
  end

  def smpp_services_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for SMPP Services in Pdf",
      "SMPP Services",
      @extension,
      &PdfSmppServicesService.index/2
    )
  end

  def department_role_management_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for Department Roles Managment in Pdf",
      "Department Roles Management",
      @extension,
      &DepartmentRolesManagementServicePdf.index/1
    )
  end

  def api_services_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for Api Services in Pdf",
      "Api_Services",
      @extension,
      &ApiServicesServicePdf.index/2
    )
  end

  def postpaid_client_statement_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for Postpaid Client Statement in Pdf",
      "postpaid_client_statement",
      @extension,
      &PostpaidClientStatementServicePdf.index/2
    )
  end

  def prepaid_client_statement_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for Prepaid Client Statement in Pdf",
      "prepaid_client_statement",
      @extension,
      &PrepaidClientStatementServicePdf.index/2
    )
  end

  def payment_plans_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading payment Plans in Pdf",
      "Payment Plans",
      @extension,
      &PaymentPlansServicePdf.index/2
    )
  end

  def self_registration_applications_service(socket, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Self Registration Applications table in Pdf",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = SelfRegistrationApplicationsServicePdf.index(socket, payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Self Registration Applications table in PDF is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "self_registration_applications", date, ".pdf")
  end

  def monthly_report_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Periodic Statistical Report table in Pdf",
      "Periodic Statistical Report",
      @extension,
      &PdfMonthlyStatisticalServicePdf.index/2
    )
  end

  def system_logs_service(_socket, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading System Logs table in Pdf",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = PdfSystemLogService.index(payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for System Logs table in PDF is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "system_audit_logs_service", date, ".pdf")
  end

  def session_logs_service(_socket, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Session Logs table in Pdf",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = PdfSessionLogService.index(payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Session Logs table in PDF is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "session_logs_service", date, ".pdf")
  end

  def api_logs_service(_socket, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading API Logs Management table in Pdf",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = PdfApiLogService.index(payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for API Logs Management table in PDF is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "api_logs_service", date, ".pdf")
  end

  def error_service(assigns, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Messaging Errors table in Pdf",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = PdfErrorServicePdf.index(assigns, payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Messaging Errors table in PDF is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "error_service", date, ".pdf")
  end

  def rates_service(assigns, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Messaging Rates table in Pdf",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = RatesServicePdf.index(assigns, payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Messaging Rates table in PDF is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "rates_service", date, ".pdf")
  end

  def purchase_service(assigns, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Purchases table in Pdf",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = PurchasesServicePdf.index(assigns, payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Purchases table in PDF is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "purchases_service", date, ".pdf")
  end

  def sender_service(assigns, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Senders table in Pdf",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = SenderServicePdf.index(assigns, payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Senders table in PDF is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "sender_service", date, ".pdf")
  end

  def client_profile_service(assigns, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Client Profiles table in Pdf",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = ClientProfileServicePdf.index(payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Client Profiles table in PDF is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "client_profile_service", date, ".pdf")
  end

  def contact_person_service(_socket, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Contact Person table in Pdf",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = ContactPersonServicePdf.index(payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Contact Person table in PDF is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "contact_person", date, ".pdf")
  end

  def user_service(_socket, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Admin User table in Pdf",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = UserServicePdf.index(payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Admin User table in PDF is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "User", date, ".pdf")
  end
end
