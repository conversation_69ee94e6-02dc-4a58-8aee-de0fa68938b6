defmodule App.Jobs.JobCheckBalance do
  import Ecto.Query, warn: false

  alias App.{
    Accounts,
    Schema.LicenseAccounts,
    Repo,
    Licenses.LicenseMapping,
    Licenses.LicenseApprovalLogs,
    Notification.UserNotifications,
    Utilities,
    Licenses
  }

  alias App.CustomContext

  @topic "VERIFY_AMOUNT"

  def subscribe(id), do: CustomContext.subscribe(@topic <> "#{id}")

  def init do
    accounts = Accounts.get_unverified_accounts()

    accounts
    |> Enum.each(fn account ->
      case check_balance(account) do
        {:ok, _msg} ->
          with {:ok, _records} <- approve_application(account, account.application) do
            {:ok, "Amount successfully validated"}
          else
            {:error, msg} -> {:error, msg}
          end

        {:error, msg} ->
          decline_application(account, account.application, msg)

        :pending ->
          :pending
      end
    end)
  end

  defp check_balance(account) do
    if is_nil(account.balance) do
      :pending
    else
      case Decimal.compare(account.balance, 0) do
        :eq ->
          {:ok, "Amount Validated"}

        :gt ->
          {:error,
           "Dear User, you currently have a balance of #{NumberF.currency(account.balance, "K")}, kindly clear this balance before proceeding"}

        :lt ->
          {:error, "Negative balance!"}
      end
    end
  end

  def approve_application(account, record) do
    category = Licenses.get_license_category_by_id(record.license_id)

    approval_level_list = Utilities.get_approval_level_list_by_category(category)

    status =
      case Enum.find_index(approval_level_list, &(&1 == record.status)) do
        nil ->
          # return true for final approval
          record.status

        index ->
          Enum.at(approval_level_list, index + 1)
      end

    get_level = Utilities.get_current_approval_level!(status)

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "approval",
      LicenseMapping.insert_changeset(record, %{
        "status" => status
      })
    )
    |> Ecto.Multi.update_all(
      "associated",
      fn %{"approval" => license} ->
        from(
          a in LicenseMapping,
          where: a.associated_license_id == ^license.id,
          update: [
            set: ^[status: status]
          ]
        )
      end,
      []
    )
    |> Ecto.Multi.update(
      "account",
      LicenseAccounts.changeset(account, %{
        "verified" => true
      })
    )
    |> Ecto.Multi.insert(
      "create_logs",
      fn %{"approval" => license} ->
        LicenseApprovalLogs.changeset(
          %LicenseApprovalLogs{},
          %{
            "status" => record.status,
            "is_approved" => true,
            "license_mapping_id" => license.id,
            "user_id" => license.id
          }
        )
      end
    )
    |> Ecto.Multi.insert(
      "create_notification",
      fn %{"approval" => license} ->
        UserNotifications.changeset(
          %UserNotifications{},
          %{
            "message" => "A License Application has been sent for approval",
            "page_url" => "/license/applications?id=#{license.id}",
            "type" => "LICENSE APPROVAL",
            "role_id" => get_level.department_id
          }
        )
      end
    )
    |> Repo.transaction()
    |> CustomContext.notify_subs(
      "VERIFY_AMOUNT:#{account.id}",
      :account_verified,
      __MODULE__
    )
  end

  def decline_application(account, record, reason) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "approval",
      LicenseMapping.insert_changeset(record, %{
        "status" => "-1"
      })
    )
    |> Ecto.Multi.update_all(
      "associated",
      fn %{"approval" => license} ->
        from(
          a in LicenseMapping,
          where: a.associated_license_id == ^license.id,
          update: [
            set: ^[status: "-1"]
          ]
        )
      end,
      []
    )
    |> Ecto.Multi.update(
      "account",
      LicenseAccounts.changeset(account, %{
        "declined" => true
      })
    )
    |> Ecto.Multi.insert(
      "create_logs",
      fn %{"approval" => license} ->
        LicenseApprovalLogs.changeset(
          %LicenseApprovalLogs{},
          %{
            "status" => record.status,
            "is_approved" => false,
            "license_mapping_id" => license.id,
            "user_id" => record.user_id
          }
        )
      end
    )
    |> Ecto.Multi.insert(
      "create_notification",
      UserNotifications.changeset(
        %UserNotifications{},
        %{
          "message" => "A Licence Application has been declined because: #{reason}",
          "page_url" => "/registration/#{record.license_id}",
          "type" => "LICENCE DECLINED",
          "reason" => reason,
          "user_id" => record.user_id,
          "role_id" => 8
        }
      )
    )
    |> Repo.transaction()
    |> CustomContext.notify_subs(
      "VERIFY_AMOUNT:#{account.id}",
      :account_verified,
      __MODULE__
    )
  end
end
