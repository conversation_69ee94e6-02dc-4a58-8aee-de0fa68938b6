export const ActivityTracker = {
    mounted() {
        // Configuration
        const THROTTLE_DELAY = 5000; // 5 seconds between activity reports
        const ACTIVITY_EVENTS = ['mousemove', 'scroll', 'keydown', 'click', 'touchstart', 'resize'];

        // Handle user leaving/returning to the page
        const handleVisibilityChange = () => {
            if (document.visibilityState === 'visible') {
                pushActivity();
            }
        };

        // Create throttled activity reporter
        const pushActivity = (() => {
            let throttled = false;
            let lastActivity = Date.now();

            return () => {
                // Update last activity time regardless of throttling
                lastActivity = Date.now();

                if (throttled) return;

                // Send the event to the server
                this.pushEventTo(`#${this.el.id}`, "activity", {
                    timestamp: lastActivity
                });

                throttled = true;

                setTimeout(() => {
                    throttled = false;

                    // If there was activity during throttle period, send another event
                    if (Date.now() - lastActivity < THROTTLE_DELAY * 0.9) {
                        pushActivity();
                    }
                }, THROTTLE_DELAY);
            };
        })();

        // Register all event listeners
        ACTIVITY_EVENTS.forEach(eventName => {
            window.addEventListener(eventName, pushActivity, { passive: true });
        });

        // Handle page visibility changes
        document.addEventListener('visibilitychange', handleVisibilityChange);

        // Initial activity push
        pushActivity();

        // Cleanup function for when the component is destroyed
        this.onDestroy = () => {
            ACTIVITY_EVENTS.forEach(eventName => {
                window.removeEventListener(eventName, pushActivity);
            });
            document.removeEventListener('visibilitychange', handleVisibilityChange);
        };
    },

    destroyed() {
        // Clean up event listeners if onDestroy exists
        if (this.onDestroy) {
            this.onDestroy();
        }
    },

    reconnected() {
        // Push activity when reconnected to ensure server is updated
        this.pushEventTo(`#${this.el.id}`, "activity", {
            timestamp: Date.now(),
            reconnected: true
        });
    }
};