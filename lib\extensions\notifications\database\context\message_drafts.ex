defmodule Notification.MessageDrafts do
  @moduledoc """
  The Message Draft context.
  """
  import Ecto.Query, warn: false

  alias Notification.{
    Notification.MessageDraft,
    Notification
  }

  alias Logs.Audit
  alias App.Repo

  @spec get_message!(any()) :: any()
  def get_message!(id), do: Repo.get!(MessageDraft, id)

  def get_message_by_service!(service_type) do
    MessageDraft
    |> where([a], a.service_type == ^service_type)
    |> limit(1)
    |> Repo.one()
  end

  def update_message(%MessageDraft{} = message, attrs, socket) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(:message, MessageDraft.changeset(message, attrs))
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} Edited Message",
      "UPDATE",
      attrs,
      "Message Draft Configuration"
    )
    |> Repo.transaction()
  end

  def change_message(%MessageDraft{} = message, attrs \\ %{}) do
    MessageDraft.changeset(message, attrs)
  end

  def change_message_draft(attrs \\ %{}) do
    MessageDraft.changeset(%MessageDraft{}, attrs)
  end

  def change_message_new(attrs \\ %{}) do
    MessageDraft.changeset(%MessageDraft{}, attrs)
  end

  def index(service_type, params, status \\ "PENDING") do
    get_message_by_service!(service_type)
    |> format_message(params)
    |> save_new_message(nil, params["mobile_number"], status)
  end

  def index_email(service_type, params) do
    get_message_by_service!(service_type)
    |> format_message(params)
  end

  def index_multi(multi, service_type, params, status \\ "PENDING") do
    get_message_by_service!(service_type)
    |> format_message(params)
    |> save_new_message(multi, params["mobile_number"], status)
  end

  defp format_message(
         %MessageDraft{service_type: "LICENCE_REGISTRATION", message: message},
         attrs
       ) do
    IO.inspect(attrs, label: "attrs in format_message")
    IO.inspect(message, label: "message in message")

    message
    |> String.replace("{{client_name}}", attrs.client_name)
  end

  defp format_message(
         %MessageDraft{service_type: "LICENCE_STATUS_UPDATE", message: message},
         attrs
       ) do
    IO.inspect(attrs, label: "attrs in format_message")
    IO.inspect(message, label: "message in message")

    message
    |> String.replace("{{client_name}}", attrs.client_name)
    |> String.replace("{{status}}", attrs.status)
  end

  defp format_message(
         %MessageDraft{service_type: "PASSWORD_RESET", message: message},
         attrs
       ) do
    message
    |> String.replace("{{first_name}}", attrs["first_name"])
    |> String.replace("{{last_name}}", attrs["last_name"])
    |> String.replace("{{password}}", attrs["password"])
  end

  defp format_message(
         %MessageDraft{service_type: "EXPIRING_DATE_NOTIFICATION", message: message},
         attrs
       ) do
    message
    |> String.replace("{{client_name}}", attrs["client_name"])
    |> String.replace("{{condition}}", attrs["condition"])
    |> String.replace("{{expiring_date}}", attrs["expiring_date"])
  end

  defp format_message(
         %MessageDraft{service_type: "EXPIRING_DATE_CLIENT_NOTIFICATION", message: message},
         attrs
       ) do
    message
    |> String.replace("{{client_name}}", attrs["client_name"])
    |> String.replace("{{condition}}", attrs["condition"])
    |> String.replace("{{expiring_date}}", attrs["expiring_date"])
  end

  defp format_message(
         %MessageDraft{service_type: "SEND_CONDITION_REASON_TO_ADMIN", message: message},
         attrs
       ) do
    IO.inspect(attrs, label: "attrs in SEND_CONDITION_REASON_TO_ADMIN")

    message
    |> String.replace("{{client_name}}", attrs["client_name"])
    |> String.replace("{{condition}}", attrs["condition"])
    |> String.replace("{{expiring_date}}", attrs["expiring_date"])
    |> String.replace("{{comments}}", attrs["comments"])
    |> String.replace("{{user_name}}", attrs["user_name"])
  end

  defp format_message(
         %MessageDraft{service_type: "MERCHANT_CREATION", message: message},
         attrs
       ) do
    message
    |> String.replace("{{merchant_name}}", attrs["merchant_name"])
    |> String.replace("{{merchant_code}}", attrs["merchant_code"])
    |> String.replace("{{username}}", attrs["username"])
    |> String.replace("{{password}}", attrs["password"])
  end

  defp format_message(
         %MessageDraft{service_type: "FUND_MERCHANT_ACCOUNT", message: message},
         attrs
       ) do
    message
    |> String.replace("{{merchant_name}}", attrs["merchant_name"])
    |> String.replace("{{amount}}", attrs["amount"])
    |> String.replace("{{receipt_no}}", attrs["receipt_no"])
    |> String.replace("{{running_balance}}", attrs["running_balance"])
  end

  defp save_new_message(new_message, multi, mobile_number, status) do
    if !is_nil(multi) do
      Notification.save_message_multi(multi, new_message, mobile_number)
    else
      Notification.save_message_changeset(new_message, mobile_number, status)
      |> Repo.insert()
      |> case do
        {:ok, sms_log} -> {:ok, sms_log}
        {:error, reason} -> {:error, reason.errors}
      end
    end
  end
end
