defmodule App.GenderTest do
  use App.DataCase

  alias App.Gender

  describe "sex" do
    alias App.Gender.Sex

    import App.GenderFixtures

    @invalid_attrs %{Female: nil, Male: nil}

    test "list_sex/0 returns all sex" do
      sex = sex_fixture()
      assert Gender.list_sex() == [sex]
    end

    test "get_sex!/1 returns the sex with given id" do
      sex = sex_fixture()
      assert Gender.get_sex!(sex.id) == sex
    end

    test "create_sex/1 with valid data creates a sex" do
      valid_attrs = %{Female: "some Female", Male: "some Male"}

      assert {:ok, %Sex{} = sex} = Gender.create_sex(valid_attrs)
      assert sex.Female == "some Female"
      assert sex.Male == "some Male"
    end

    test "create_sex/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Gender.create_sex(@invalid_attrs)
    end

    test "update_sex/2 with valid data updates the sex" do
      sex = sex_fixture()
      update_attrs = %{Female: "some updated Female", Male: "some updated Male"}

      assert {:ok, %Sex{} = sex} = Gender.update_sex(sex, update_attrs)
      assert sex.Female == "some updated Female"
      assert sex.Male == "some updated Male"
    end

    test "update_sex/2 with invalid data returns error changeset" do
      sex = sex_fixture()
      assert {:error, %Ecto.Changeset{}} = Gender.update_sex(sex, @invalid_attrs)
      assert sex == Gender.get_sex!(sex.id)
    end

    test "delete_sex/1 deletes the sex" do
      sex = sex_fixture()
      assert {:ok, %Sex{}} = Gender.delete_sex(sex)
      assert_raise Ecto.NoResultsError, fn -> Gender.get_sex!(sex.id) end
    end

    test "change_sex/1 returns a sex changeset" do
      sex = sex_fixture()
      assert %Ecto.Changeset{} = Gender.change_sex(sex)
    end
  end

  describe "sex" do
    alias App.Gender.Sex

    import App.GenderFixtures

    @invalid_attrs %{Gender: nil}

    test "list_sex/0 returns all sex" do
      sex = sex_fixture()
      assert Gender.list_sex() == [sex]
    end

    test "get_sex!/1 returns the sex with given id" do
      sex = sex_fixture()
      assert Gender.get_sex!(sex.id) == sex
    end

    test "create_sex/1 with valid data creates a sex" do
      valid_attrs = %{Gender: "some Gender"}

      assert {:ok, %Sex{} = sex} = Gender.create_sex(valid_attrs)
      assert sex.Gender == "some Gender"
    end

    test "create_sex/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Gender.create_sex(@invalid_attrs)
    end

    test "update_sex/2 with valid data updates the sex" do
      sex = sex_fixture()
      update_attrs = %{Gender: "some updated Gender"}

      assert {:ok, %Sex{} = sex} = Gender.update_sex(sex, update_attrs)
      assert sex.Gender == "some updated Gender"
    end

    test "update_sex/2 with invalid data returns error changeset" do
      sex = sex_fixture()
      assert {:error, %Ecto.Changeset{}} = Gender.update_sex(sex, @invalid_attrs)
      assert sex == Gender.get_sex!(sex.id)
    end

    test "delete_sex/1 deletes the sex" do
      sex = sex_fixture()
      assert {:ok, %Sex{}} = Gender.delete_sex(sex)
      assert_raise Ecto.NoResultsError, fn -> Gender.get_sex!(sex.id) end
    end

    test "change_sex/1 returns a sex changeset" do
      sex = sex_fixture()
      assert %Ecto.Changeset{} = Gender.change_sex(sex)
    end
  end
end
