defmodule AppWeb.DocumentComponent do
  @moduledoc """
  Component for handling document uploads with previews.
  """
  use Phoenix.Component

  def document_upload(assigns) do
    ~H"""
    <div class="flex flex-col w-full space-y-3">
      <!-- File display area -->
      <div class="w-full rounded-lg p-4 bg-white border border-gray-200">
        <%= if Enum.empty?(@db_files) && Enum.empty?(@uploads.file.entries) do %>
          <div class="text-sm text-gray-500 text-center py-2">No files uploaded</div>
        <% else %>
          <div class="space-y-2">
            <!-- Database files -->
            <%= for file <- @db_files do %>
              <div class="group flex items-center justify-between bg-gray-50 rounded-md p-2 hover:bg-gray-100 transition-colors">
                <div
                  class="flex items-center space-x-3 w-full cursor-pointer"
                  phx-click="view_document"
                  phx-value-path={file.path}
                  phx-value-doc_name={file.filename}
                >
                  <!-- File icon -->
                  <div class="text-gray-500">
                    <%= if String.ends_with?(file.filename, [".jpg", ".jpeg", ".png"]) do %>
                      <svg
                        class="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                    <% else %>
                      <svg
                        class="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                        />
                      </svg>
                    <% end %>
                  </div>
                  <!-- File details -->
                  <div class="min-w-0 flex-1">
                    <p class="text-sm font-medium text-gray-900 truncate">
                      <%= String.slice(file.filename, 0, 28) <>
                        if String.length(file.filename) > 28, do: "...", else: "" %>
                    </p>

                    <p class="text-xs text-gray-500">
                      Level <%= file.level %> • <%= Calendar.strftime(
                        NaiveDateTime.add(file.inserted_at, 7200, :second),
                        "%d %B %Y %H:%M:%S"
                      ) %>
                    </p>
                  </div>
                </div>
              </div>
            <% end %>
            <!-- Upload entries -->
            <%= for entry <- @uploads.file.entries do %>
              <div class="flex items-center justify-between bg-gray-50 rounded-md p-2">
                <div class="flex items-center space-x-3 flex-1 min-w-0">
                  <!-- File icon -->
                  <div class="text-gray-500">
                    <%= if String.ends_with?(entry.client_name, [".jpg", ".jpeg", ".png"]) do %>
                      <svg
                        class="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                    <% else %>
                      <svg
                        class="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                        />
                      </svg>
                    <% end %>
                  </div>
                  <!-- File details -->
                  <div class="min-w-0 flex-1">
                    <p class="text-sm font-medium text-gray-900 truncate">
                      <%= String.slice(entry.client_name, 0, 28) <>
                        if String.length(entry.client_name) > 28, do: "...", else: "" %>
                    </p>

                    <p class="text-xs text-gray-500">
                      <%= NumberF.comma_separated(div(entry.client_size, 1000), 0) %> KB
                    </p>
                  </div>
                </div>
                <!-- Progress & cancel button -->
                <div class="flex items-center space-x-2">
                  <%= if entry.progress < 100 do %>
                    <div class="flex items-center space-x-2">
                      <div class="w-16 bg-gray-200 rounded-full h-1.5">
                        <div
                          class="bg-blue-600 h-1.5 rounded-full"
                          style={"width: #{entry.progress}%"}
                        />
                      </div>
                      <span class="text-xs text-gray-500"><%= entry.progress %>%</span>
                    </div>
                  <% end %>

                  <button
                    type="button"
                    phx-click="cancel-entry"
                    phx-value-ref={entry.ref}
                    class="p-1 rounded-full text-gray-400 hover:text-red-500 focus:outline-none"
                  >
                    <svg
                      class="h-4 w-4"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
                <!-- Upload errors -->
                <%= for err <- upload_errors(@uploads.file, entry) do %>
                  <div class="mt-1 text-xs text-red-600">
                    <%= error_to_string(err) %>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
      <!-- Upload drop area -->
      <%= if @uploads.file.entries == [] do %>
        <div
          phx-drop-target={@uploads.file.ref}
          class="relative border border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors"
        >
          <div class="flex flex-col items-center justify-center py-6">
            <svg
              class="w-8 h-8 text-gray-400 mb-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
              />
            </svg>

            <p class="text-sm text-gray-500 text-center">
              <span class="font-medium text-blue-600">Click to upload</span> or drag and drop
            </p>
          </div>

          <.live_file_input
            upload={@uploads.file}
            class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          />
        </div>
      <% else %>
        <div class="hidden">
          <.live_file_input upload={@uploads.file} />
        </div>
      <% end %>
    </div>
    """
  end

  # Convert error atoms to readable strings
  defp error_to_string(:too_large), do: "The file is too large (max 10MB)"
  defp error_to_string(:not_accepted), do: "Unacceptable file type (only PDF, JPG, JPEG, PNG)"
  defp error_to_string(:too_many_files), do: "You can only upload one file"
  defp error_to_string(error), do: "Error: #{inspect(error)}"
end
