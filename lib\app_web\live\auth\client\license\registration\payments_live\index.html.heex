<%= if @data_loader == false do %>
  <div class="min-h-screen bg-gray-50 py-8">
    <.live_component
      module={AppWeb.Registration.NavigationComponent}
      id="navigation"
      steps={@steps}
      current_position={@current_position}
    />
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 bg-white rounded-lg shadow-md mt-8">
      <div class="mb-2 border-b pb-2">
        <h1 class="text-2xl font-bold text-gray-900"><%= @chosen_licence.name %> Registration</h1>

        <p class="mt-2 text-sm text-gray-600">
          Please upload all required documents to proceed with your registration.
        </p>
      </div>

      <div class="space-y-8">
        <%= if @chosen_licence.associated_license_id do %>
          <!-- Representatives Section -->
          <div class="rounded-xl p-6">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h2 class="text-xl font-semibold text-gray-900">Representatives</h2>

                <p class="text-sm text-gray-600">
                  Attach required representatives to your license
                </p>
              </div>

              <div class="text-right">
                <div class="text-2xl font-bold text-blue-600">
                  <%= @associates_count %>/<%= @record.data["representative_number"] %>
                </div>

                <div class="text-xs text-gray-500">Attached</div>
              </div>
            </div>
            <!-- Progress Bar -->
            <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
              <div
                class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={"width: #{((@associates_count || 0) / (String.to_integer(@record.data["representative_number"] || 1)) * 100)}%"}
              >
              </div>
            </div>
            <!-- Cost Breakdown -->
            <div class="bg-gray-50 rounded-lg p-4 mb-4">
              <h3 class="font-medium text-gray-900 mb-3">License Cost Breakdown</h3>

              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">License Fee:</span>
                  <span class="font-medium">
                    <%= NumberF.currency(@chosen_licence.amount || 0, "") %>
                  </span>
                </div>

                <div class="flex justify-between">
                  <span class="text-gray-600">Compensation Levy:</span>
                  <span class="font-medium">
                    <%= NumberF.currency(@chosen_licence.other_fees || 0, "") %>
                  </span>
                </div>

                <%!-- <%= if @associates != [] do %>
                  <div class="flex justify-between">
                    <span class="text-gray-600">
                      Representatives (<%= @associates_count || 0 %> × <%= NumberF.currency(
                        List.first(@associates).license.amount,
                        ""
                      ) %>):
                    </span>
                    
                    <span class="font-medium">
                      <%= NumberF.currency(@totals.total_assoc_amount, "") %>
                    </span>
                  </div>
                <% end %> --%>
              </div>
            </div>
            <!-- Attached Representatives -->
            <%= if (@associates_count || 0) > 0 do %>
              <div class="bg-white rounded-lg p-4 border border-gray-200 mb-4">
                <h3 class="font-medium text-gray-900 mb-3 flex items-center">
                  <svg
                    class="h-4 w-4 text-green-500 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Attached Representatives
                </h3>

                <div class="space-y-2">
                  <%= for representative <- @associates do %>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div class="flex items-center space-x-3">
                        <div class="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                          <span class="text-green-600 font-medium text-xs">
                            <%= String.first(representative.user.first_name) %><%= String.first(
                              representative.user.last_name
                            ) %>
                          </span>
                        </div>

                        <div>
                          <h5 class="font-medium text-gray-900 text-sm">
                            <%= representative.record_name %>
                          </h5>

                          <p class="text-xs text-gray-600">
                            NRC: <%= representative.data["national_id"] %>
                          </p>
                        </div>
                      </div>

                      <div class="text-right">
                        <%!-- <button
                          phx-click="detach"
                          phx-value-id={representative.id}
                          class="text-xs text-red-600 hover:text-red-700"
                        >
                          Remove
                        </button> --%>
                      </div>

                      <div class="text-right">
                        <span class="text-gray-900">
                          <%= NumberF.currency(
                            representative.license.amount,
                            ""
                          ) %>
                        </span>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>

            <div class="border-t pt-2 flex justify-between font-semibold text-base">
              <span class="text-gray-900">Total Amount:</span>
              <span class="text-blue-600">
                <%= NumberF.currency(@totals.total_amount, "K") %>
              </span>
            </div>
            <!-- Search Section (only show if not all representatives are attached) -->
            <%= if (@associates_count || 0) < String.to_integer(@record.data["representative_number"]) do %>
              <div class="bg-white rounded-lg p-4 border border-gray-200 mt-4">
                <div class="mb-3">
                  <h3 class="text-base font-medium text-gray-900 flex items-center">
                    <svg
                      class="h-4 w-4 text-blue-500 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                    Search Representative
                  </h3>

                  <p class="text-xs text-gray-500">Enter NRC number to find and attach</p>
                </div>

                <.form for={@search_form} phx-submit="search" class="space-y-3">
                  <div class="flex gap-3">
                    <div class="flex-1">
                      <.input
                        field={@search_form[:nrc]}
                        type="text"
                        placeholder="123456/78/9"
                        phx-hook="nrcInputHook"
                        pattern="\d{6}/\d{2}/\d"
                        class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <.button
                      type="submit"
                      class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center text-sm"
                    >
                      <svg
                        class="h-4 w-4 mr-1"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        />
                      </svg>
                      Search
                    </.button>
                  </div>
                </.form>
                <!-- Loading State -->
                <div :if={@searching} class="text-center py-4">
                  <div class="inline-flex items-center text-gray-600 text-sm">
                    <svg class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      />
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      />
                    </svg>
                    Searching...
                  </div>
                </div>
                <!-- Search Results -->
                <div :if={@search_results && length(@search_results) > 0} class="mt-4 space-y-2">
                  <h4 class="font-medium text-gray-900 text-sm">
                    Found <%= length(@search_results) %> representative(s)
                  </h4>

                  <%= for representative <- @search_results do %>
                    <%= if representative.user.company_id do %>
                      <div class="text-center py-6">
                        <svg
                          class="h-8 w-8 text-gray-400 mx-auto mb-3"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                          />
                        </svg>

                        <h4 class="text-base font-medium text-gray-900 mb-2">
                          Representative already attached to a Company
                        </h4>
                      </div>
                    <% else %>
                      <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border">
                        <div class="flex items-center space-x-3">
                          <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                            <span class="text-blue-600 font-medium text-xs">
                              <%= String.first(representative.user.first_name) %><%= String.first(
                                representative.user.last_name
                              ) %>
                            </span>
                          </div>

                          <div>
                            <h5 class="font-medium text-gray-900 text-sm">
                              <%= representative.record_name %>
                            </h5>

                            <p class="text-xs text-gray-600">
                              NRC: <%= representative.data["national_id"] %>
                            </p>

                            <p :if={representative.user.email} class="text-xs text-gray-500">
                              Email: <%= representative.user.email %>
                            </p>

                            <p :if={representative.license.amount} class="text-xs text-blue-700">
                              License Amount: <%= NumberF.currency(
                                representative.license.amount || 0
                              ) %>
                            </p>
                          </div>
                        </div>

                        <div class="text-right">
                          <.button
                            phx-click="attach"
                            phx-value-id={representative.id}
                            class="px-3 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-xs"
                          >
                            Attach
                          </.button>
                        </div>
                      </div>
                    <% end %>
                  <% end %>
                </div>
                <!-- No Results -->
                <div
                  :if={@searched && @search_results && length(@search_results) == 0}
                  class="text-center py-6"
                >
                  <svg
                    class="h-8 w-8 text-gray-400 mx-auto mb-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>

                  <h4 class="text-base font-medium text-gray-900 mb-2">
                    No representatives found
                  </h4>

                  <p class="text-gray-500 text-sm">
                    No representative found with NRC "<span class="font-medium"><%= @search_query %></span>". Please verify and try again.
                  </p>
                </div>
              </div>

              <%!-- <% else %>
              <div class="rounded-lg p-6 text-center">
                <svg
                  class="h-8 w-8 text-blue-600 mx-auto mb-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                
                <h3 class="text-lg font-medium text-gray-800 mb-2">
                  All Representatives Attached
                </h3>
                
                <p class="text-gray-700">
                  You have successfully attached all required representatives.
                </p>
              </div> --%>
            <% end %>
          </div>
        <% else %>
          <!-- Cost Breakdown -->
          <div class="bg-gray-50 rounded-lg p-4 mb-4">
            <h3 class="font-medium text-gray-900 mb-3">License Cost Breakdown</h3>

            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600">License Fee:</span>
                <span class="font-medium">
                  <%= NumberF.currency(@chosen_licence.amount || 0) %>
                </span>
              </div>

              <div class="flex justify-between">
                <span class="text-gray-600">Compensation Levy:</span>
                <span class="font-medium">
                  <%= NumberF.currency(@chosen_licence.other_fees || 0) %>
                </span>
              </div>

              <div class="border-t pt-2 flex justify-between font-semibold text-base">
                <span class="text-gray-900">Total Amount:</span>
                <span class="text-blue-600">
                  <%= NumberF.currency(
                    Decimal.add(@chosen_licence.amount || 0, @chosen_licence.other_fees || 0)
                  ) %>
                </span>
              </div>
            </div>
          </div>
        <% end %>
        <!-- Documents Section -->
        <%= if @record.license.associated_license_id do %>
          <div class="rounded-xl p-6">
            <div class="flex items-center justify-between mb-6">
              <div>
                <h2 class="text-xl font-semibold text-gray-900">POP Upload</h2>

                <p class="text-sm text-gray-600 mt-1">Upload the required document(s)</p>
              </div>
            </div>
            <!-- Uploaded Documents -->
            <%= if @available_uploads == []  do %>
              <div class="bg-white rounded-lg border border-gray-200 p-6">
                <h3 class="font-medium text-gray-900 mb-4">Uploaded Documents</h3>

                <div class="space-y-3">
                  <%= for field <- @upload_fields1 do %>
                    <%= if @record.data && Map.has_key?(@record.data, field.field_name) && Map.get(@record.data, field.field_name) != nil do %>
                      <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                          <div class="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                            <svg
                              class="h-4 w-4 text-green-600"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                clip-rule="evenodd"
                              />
                            </svg>
                          </div>

                          <div>
                            <p class="font-medium text-gray-900"><%= field.field_label %></p>

                            <p class="text-sm text-gray-600 truncate max-w-xs">
                              <%= String.slice(
                                @record.data[field.field_name] |> Path.basename(),
                                0,
                                40
                              ) %>
                            </p>
                          </div>
                        </div>

                        <button
                          type="button"
                          phx-click="remove_file"
                          phx-value-field={field.field_name}
                          class="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full transition-colors"
                          title="Remove file"
                        >
                          <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path
                              fill-rule="evenodd"
                              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </button>
                      </div>
                    <% end %>
                  <% end %>
                </div>
              </div>
            <% end %>
            <!-- Upload Form -->
            <.simple_form
              for={@upload_form}
              id="upload-form"
              phx-submit="doc_save"
              phx-change="validate_doc"
            >
              <%= if !Enum.empty?(@available_uploads) && (@associates_count || 0) == String.to_integer(@record.data["representative_number"]) do %>
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                  <div class="space-y-6">
                    <%= for field <- @available_uploads do %>
                      <%= if !(@record.data && Map.has_key?(@record.data, field.field_name) && Map.get(@record.data, field.field_name) != nil) do %>
                        <div>
                          <label class="block text-sm font-medium text-gray-700 mb-2">
                            <%= field.field_label %> <span class="text-red-500">*</span>
                          </label>

                          <AppWeb.DocumentUploadComponent.document_upload_field uploads={
                            @uploads[String.to_atom(field.field_name)]
                          } /> <% field_atom = String.to_atom(field.field_name) %>
                          <%= if error = @upload_form.source.errors[field_atom] do %>
                            <p class="mt-2 text-sm text-red-600"><%= elem(error, 0) %></p>
                          <% end %>

                          <%= if Map.get(field, :field_description) do %>
                            <p class="text-xs text-gray-500 mt-1">
                              <%= field.field_description %>
                            </p>
                          <% end %>
                        </div>
                      <% end %>
                    <% end %>
                  </div>
                </div>
              <% end %>

              <%= if (@associates_count || 0) != String.to_integer(@record.data["representative_number"]) do %>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <p class="text-sm text-yellow-800">
                    Please attach the specified number of representatives.
                  </p>
                </div>
              <% end %>

              <div class="flex justify-end pt-6 border-t">
                <.button type="button" phx-click="back">
                  <b><i class="hero-chevron-double-left position-left"></i></b> Back
                </.button>

                <%= if @upload_form.source.valid? do %>
                  <.button
                    type="submit"
                    class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium"
                  >
                    Next<b><i class="hero-chevron-double-right position-right"></i></b>
                  </.button>
                <% end %>
              </div>
            </.simple_form>
          </div>
        <% end %>
        <%!-- Individuals --%>
        <%= if is_nil(@record.license.associated_license_id) do %>
          <div class="rounded-xl p-6">
            <div class="flex items-center justify-between mb-6">
              <div>
                <h2 class="text-xl font-semibold text-gray-900">POP Upload</h2>

                <p class="text-sm text-gray-600 mt-1">Upload the required document(s)</p>
              </div>
            </div>
            <!-- Uploaded Documents -->
            <%= if @available_uploads == [] do %>
              <div class="bg-white rounded-lg border border-gray-200 p-6">
                <h3 class="font-medium text-gray-900 mb-4">Uploaded Documents</h3>

                <div class="space-y-3">
                  <%= for field <- @upload_fields1 do %>
                    <%= if @record.data && Map.has_key?(@record.data, field.field_name) && Map.get(@record.data, field.field_name) != nil do %>
                      <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                          <div class="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                            <svg
                              class="h-4 w-4 text-green-600"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                clip-rule="evenodd"
                              />
                            </svg>
                          </div>

                          <div>
                            <p class="font-medium text-gray-900"><%= field.field_label %></p>

                            <p class="text-sm text-gray-600 truncate max-w-xs">
                              <%= String.slice(
                                @record.data[field.field_name] |> Path.basename(),
                                0,
                                40
                              ) %>
                            </p>
                          </div>
                        </div>

                        <button
                          type="button"
                          phx-click="remove_file"
                          phx-value-field={field.field_name}
                          class="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full transition-colors"
                          title="Remove file"
                        >
                          <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path
                              fill-rule="evenodd"
                              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </button>
                      </div>
                    <% end %>
                  <% end %>
                </div>
              </div>
            <% end %>
            <!-- Upload Form -->
            <.simple_form
              for={@upload_form}
              id="upload-form"
              phx-submit="doc_save"
              phx-change="validate_doc"
            >
              <%= if !Enum.empty?(@available_uploads) do %>
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                  <div class="space-y-6">
                    <%= for field <- @available_uploads do %>
                      <%= if !(@record.data && Map.has_key?(@record.data, field.field_name) && Map.get(@record.data, field.field_name) != nil) do %>
                        <div>
                          <label class="block text-sm font-medium text-gray-700 mb-2">
                            <%= field.field_label %> <span class="text-red-500">*</span>
                          </label>

                          <AppWeb.DocumentUploadComponent.document_upload_field uploads={
                            @uploads[String.to_atom(field.field_name)]
                          } /> <% field_atom = String.to_atom(field.field_name) %>
                          <%= if error = @upload_form.source.errors[field_atom] do %>
                            <p class="mt-2 text-sm text-red-600"><%= elem(error, 0) %></p>
                          <% end %>

                          <%= if Map.get(field, :field_description) do %>
                            <p class="text-xs text-gray-500 mt-1">
                              <%= field.field_description %>
                            </p>
                          <% end %>
                        </div>
                      <% end %>
                    <% end %>
                  </div>
                </div>

                <div class="flex justify-end pt-6 border-t">
                  <.button
                    type="button"
                    phx-click="back"
                    class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium"
                  >
                    <b><i class="hero-chevron-double-left position-right"></i></b> Back
                  </.button>

                  <.button
                    type="button"
                    phx-click="skip"
                    class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium"
                  >
                    Skip <b><i class="hero-chevron-right position-right"></i></b>
                  </.button>

                  <%= if @upload_form.source.valid?  do %>
                    <.button
                      type="submit"
                      class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium"
                    >
                      Next<b><i class="hero-chevron-double-right position-right"></i></b>
                    </.button>
                  <% end %>
                </div>
              <% end %>
            </.simple_form>
          </div>
        <% end %>
        <!-- Navigation -->
      </div>
    </div>
  </div>
<% end %>
<!-- Confirmation Modal -->
<Model.confirmation_model
  :if={@live_action == :confirm}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
