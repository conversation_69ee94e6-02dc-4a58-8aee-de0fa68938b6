defmodule App.Service.Statistics.Dashboard do
  alias App.Service.Statistics.Dashboard.{
    DailyChart,
    WeeklyChart,
    MonthlyChart,
    AnnuallyChart
  }

  def bar_chart_by_product(params) do
    case params["filter"] do
      # DailyChart.init()
      "daily" -> 0
      # WeeklyChart.init()
      "weekly" -> 0
      # MonthlyChart.init()
      "monthly" -> 0
      # AnnuallyChart.init()
      "annually" -> 0
    end
  end
end
