defmodule App.Service.Export do
  @moduledoc false
  alias App.Service.Export.ExcelAccessRolesService
  alias AppWeb.Endpoint

  alias App.Service.Export.{
    CsvFunctions,
    ExcelFunctions,
    PdfFunctions
  }

  def index(socket, payload) do
    IO.inspect(payload)

    case payload["file_type"] do
      "xlsx" ->
        export_xlsx(socket, Map.put(payload, "filter", adjust_filter(payload)))

      "csv" ->
        export_csv(socket, Map.put(payload, "filter", adjust_filter(payload)))

      "pdf" ->
        export_pdf(socket, Map.put(payload, "filter", adjust_filter(payload)))

      a ->
        Endpoint.broadcast(
          "sweet_alert:" <> to_string(payload["browser_id"]),
          "sweet_alert:" <> to_string(payload["browser_id"]),
          %{
            message: "Invalid export type [#{payload["file_type"]}]",
            expression: "toast",
            timer: 8000,
            icon: "error"
          }
        )
    end
  end

  defp adjust_filter(map) do
    case map do
      %{
        "filter" => %{
          "filter" => value
        }
      } ->
        value

      %{"filter" => value} ->
        value

      _ ->
        map
    end
  end

  defp export_xlsx(socket, payload) do
    case payload["service"] do
      "users_service" ->
        ExcelFunctions.user_service(socket, payload)

      "client_service" ->
        ExcelFunctions.client_profile_service(socket, payload)

      "contact_person" ->
        ExcelFunctions.contact_person_service(socket, payload)

      "client_profile_service" ->
        ExcelFunctions.client_profile_service(socket, payload)

      "sender_service" ->
        ExcelFunctions.sender_service(socket, payload)

      "purchases_service" ->
        ExcelFunctions.purchase_service(socket, payload)

      "rates_service" ->
        ExcelFunctions.rates_service(socket, payload)

      "error_service" ->
        ExcelFunctions.error_service(socket, payload)

      # "annual_provider_report_service" ->
      #   ExcelFunctions.annual_report_service(socket, payload)
      # "monthly_provider_report_service" ->
      #   ExcelFunctions.monthly_report_service(socket, payload)

      "api_logs_service" ->
        ExcelFunctions.api_logs_service(socket, payload)

      "session_logs_service" ->
        ExcelFunctions.session_logs_service(socket, payload)

      "system_audit_logs_service" ->
        ExcelFunctions.system_logs_service(socket, payload)

      "applications" ->
        ExcelFunctions.self_registration_applications_service(socket, payload)

      "plan_service" ->
        ExcelFunctions.payment_plans_service(socket, payload)

      "user_service" ->
        ExcelFunctions.prepaid_client_statement_service(socket, payload)

      "user_post_service" ->
        ExcelFunctions.postpaid_client_statement_service(socket, payload)

      "api_services" ->
        ExcelFunctions.api_services_service(socket, payload)

      "smpp_services" ->
        ExcelFunctions.smpp_services_service(socket, payload)

      "monthly_provider_report_service" ->
        ExcelFunctions.monthly_report_service(socket, payload)

      "department_service" ->
        ExcelFunctions.department_roles_management_service(socket, payload)

      "access_service" ->
        ExcelFunctions.access_roles_service(socket, payload)

      "view_role_users" ->
        ExcelFunctions.admin_access_role_service(socket, payload)

      "non_role_users" ->
        ExcelFunctions.admin_access_role_none_role_user_service(socket, payload)

      "provider_reports" ->
        ExcelFunctions.service_provider_report(socket, payload)

      "sms_logs_service" ->
        ExcelFunctions.sms_logs_service(socket, payload)

      "client_statistics" ->
        ExcelFunctions.client_statistics_report(socket, payload)

      "archieve_sms_logs_service" ->
        ExcelFunctions.sms_logs_archieve(socket, payload)

      "transactions" ->
        ExcelFunctions.transaction_reports_service(socket, payload)

      "annual_provider_report_service" ->
        ExcelFunctions.annual_report_service(socket, payload)
    end
  end

  defp export_csv(socket, payload) do
    case payload["service"] do
      "users_service" ->
        CsvFunctions.user_service(socket, payload)

      "client_service" ->
        CsvFunctions.client_profile_service(socket, payload)

      "contact_person" ->
        CsvFunctions.contact_person_service(socket, payload)

      "client_profile_service" ->
        CsvFunctions.client_profile_service(socket, payload)

      "sender_service" ->
        CsvFunctions.sender_service(socket, payload)

      "purchases_service" ->
        CsvFunctions.purchase_service(socket, payload)

      "rates_service" ->
        CsvFunctions.rates_service(socket, payload)

      "error_service" ->
        CsvFunctions.error_service(socket, payload)

      "annual_provider_report_service" ->
        CsvFunctions.annual_report_service(socket, payload)

      "monthly_provider_report_service" ->
        CsvFunctions.monthly_report_service(socket, payload)

      "api_logs_service" ->
        CsvFunctions.api_logs_service(socket, payload)

      "session_logs_service" ->
        CsvFunctions.session_logs_service(socket, payload)

      "system_audit_logs_service" ->
        CsvFunctions.system_logs_service(socket, payload)

      "applications" ->
        CsvFunctions.self_registration_applications_service(socket, payload)

      "sms_logs_service" ->
        CsvFunctions.sms_logs_service(socket, payload)

      "plan_service" ->
        CsvFunctions.payment_plans_services(socket, payload)

      "user_service" ->
        CsvFunctions.prepaid_client_statement_service(socket, payload)

      "user_post_service" ->
        CsvFunctions.postpaid_client_statement_service(socket, payload)

      "api_services" ->
        CsvFunctions.api_services_service(socket, payload)

      "smpp_services" ->
        CsvFunctions.smpp_services_service(socket, payload)

      "access_service" ->
        CsvFunctions.access_service(socket, payload)

      "department_service" ->
        CsvFunctions.department_roles_management_service(socket, payload)

      "view_role_users" ->
        CsvFunctions.admin_access_role_management_service(socket, payload)

      "non_role_users" ->
        CsvFunctions.admin_access_role_none_role_user_service(socket, payload)

      "provider_reports" ->
        CsvFunctions.service_provider_report(socket, payload)

      "client_statistics" ->
        CsvFunctions.client_statistics_report(socket, payload)

      "archieve_sms_logs_service" ->
        CsvFunctions.archieve_sms_logs_service(socket, payload)

      "transactions" ->
        CsvFunctions.transaction_reports_service(socket, payload)
    end
  end

  defp export_pdf(socket, payload) do
    case payload["service"] do
      "users_service" ->
        PdfFunctions.user_service(socket, payload)

      "client_service" ->
        PdfFunctions.client_profile_service(socket, payload)

      "contact_person" ->
        PdfFunctions.contact_person_service(socket, payload)

      "client_profile_service" ->
        PdfFunctions.client_profile_service(socket, payload)

      "sender_service" ->
        PdfFunctions.sender_service(socket, payload)

      "purchases_service" ->
        PdfFunctions.purchase_service(socket, payload)

      "rates_service" ->
        PdfFunctions.rates_service(socket, payload)

      "error_service" ->
        PdfFunctions.error_service(socket, payload)

      "annual_provider_report_service" ->
        PdfFunctions.annual_report_service(socket, payload)

      "api_logs_service" ->
        PdfFunctions.api_logs_service(socket, payload)

      "session_logs_service" ->
        PdfFunctions.session_logs_service(socket, payload)

      "system_audit_logs_service" ->
        PdfFunctions.system_logs_service(socket, payload)

      "applications" ->
        PdfFunctions.self_registration_applications_service(socket, payload)

      "plan_service" ->
        PdfFunctions.payment_plans_service(socket, payload)

      "user_service" ->
        PdfFunctions.prepaid_client_statement_service(socket, payload)

      "user_post_service" ->
        PdfFunctions.postpaid_client_statement_service(socket, payload)

      "api_services" ->
        PdfFunctions.api_services_service(socket, payload)

      "smpp_services" ->
        PdfFunctions.smpp_services_service(socket, payload)

      "monthly_provider_report_service" ->
        PdfFunctions.monthly_report_service(socket, payload)

      "department_service" ->
        PdfFunctions.department_role_management_service(socket, payload)

      "access_service" ->
        PdfFunctions.access_roles_service(socket, payload)

      "view_role_users" ->
        PdfFunctions.admin_access_role_service(socket, payload)

      "non_role_users" ->
        PdfFunctions.admin_access_role_none_role_user_service(socket, payload)

      "provider_reports" ->
        PdfFunctions.service_provider_report(socket, payload)

      "sms_logs_service" ->
        PdfFunctions.sms_logs_service(socket, payload)

      "client_statistics" ->
        PdfFunctions.client_statistics_report(socket, payload)

      "archieve_sms_logs_service" ->
        PdfFunctions.sms_logs_archieve(socket, payload)

      "transactions" ->
        PdfFunctions.transaction_reports_service(socket, payload)
    end
  end
end
