defmodule AppWeb.Auth.LicenseSummaryLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias App.Roles
  alias App.Licenses.LicenseData
  alias App.Licenses

  @impl true
  def mount(params, _s, socket) do
    selected = App.Licenses.get_licence_data_by_license_id_summary(params["id"])

    {:ok,
     socket
     # |> assign(:licenses_forms, forms)
     |> assign(:get_params_id, params["id"] |> String.to_integer())
     |> assign(:name, App.Licenses.get_license_name(params["id"]))
     |> assign(
       :checked,
       App.SummaryData.checking_license_field_id_by_license_id_summary(params["id"])
     )
     |> assign(:field_label, App.Licenses.query_field_label())
     |> assign(:selected_ids, selected)
     |> assign(:license_field, App.SummaryData.checking_off_toggle())
     |> assign(:updated_list, [])
     |> assign(:filter, [])
     |> assign(:field, nil)
     |> assign(:license_id, params["id"])
     |> assign(confirmation_model: false)
     |> assign(confirmation_model_title: "Are you sure?")
     |> assign(confirmation_model_text: "")
     |> assign(confirmation_model_agree: "")
     |> assign(confirmation_model_reject: "close_confirmation_model")
     |> assign(confirmation_model_params: "")
     |> assign(confirmation_model_icon: "exclamation_circle")}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply,
     socket
     |> assign(:params, params)
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "License")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      {:save, params} -> save(params, socket)
    end
  end

  @impl true
  def handle_event(target, params, socket), do: handle_event_switch(target, params, socket)

  def handle_event_switch(target, params, %{assigns: assigns} = socket) do
    case target do
      "search" ->
        search(params, socket)

      "save" ->
        save(params, socket)

      "validate" ->
        validate(params, socket)

      "check_id" ->
        id = String.to_integer(params["field"])

        selected = Enum.map(assigns.selected_ids, & &1.field)
        IO.inspect(selected, label: "selectedselectedselectedselected")

        toggled =
          App.Licenses.get_licence_data_by_license_id_summary(socket.assigns.get_params_id)
          |> Enum.map(& &1.field)

        update_list =
          case selected do
            [] ->
              [id]

            _ ->
              case Enum.member?(selected, id) do
                true ->
                  if length(selected) == 1 do
                    Enum.reject(toggled, &(&1 == List.first(selected)))
                  else
                    Enum.reject(selected, &(&1 == id))
                  end
              end
          end

        #        case App.Licenses.remove_form(
        #               socket,
        #               %{"license_id" => assigns.get_params_id, "license_field_id" => update_list},
        #               id
        #             ) do
        #          {:ok, _} ->
        #            socket =
        #              socket
        #              |> assign(:updated_list, update_list)
        #              |> assign(
        #                :selected_ids,
        #                App.Licenses.get_licence_data_by_license_id_summary(socket.assigns.get_params_id)
        #              )
        #              |> LiveFunctions.sweet_alert("License form field removed Successfully.", "success")
        #
        #            reassign(socket)
        #
        #          {:error, reason} ->
        #            IO.inspect(reason, label: "Remove form error")
        #            {:noreply, socket}
        #        end
        {:noreply,
         socket
         |> assign(:updated_list, update_list)
         |> assign(
           :selected_ids,
           App.Licenses.get_licence_data_by_license_id_summary(socket.assigns.get_params_id)
         )}
    end
  end

  def search(params, socket) do
    filter = String.downcase(params["filter"] || "")

    forms =
      App.Licenses.get_licence_data_by_license_id_summary(socket.assigns.get_params_id)
      |> Enum.filter(fn %{field_label: field_label} ->
        String.contains?(String.downcase(field_label), filter)
      end)

    {:noreply,
     socket
     |> assign(selected_ids: forms, filter: filter)}
  end

  def validate(params, socket) do
    {:noreply,
     socket
     |> assign(
       :license_manage,
       params
     )}
  end

  def save(params, %{assigns: assigns} = socket) do
    license_id = assigns.get_params_id
    keys = Map.keys(params) |> Enum.map(&String.to_integer/1)

    param = %{"license_id" => assigns.get_params_id, "license_field_id" => keys}

    case App.SummaryData.create_summarised_data(socket, param) do
      {:ok, _} ->
        {
          :noreply,
          LiveFunctions.sweet_alert(socket, "Summary Successfully Saved.", "success")
        }

        reassign(socket)

      _ ->
        {:noreply, LiveFunctions.sweet_alert(socket, "Form(s) already submitted .", "warning")}
    end
  end

  defp reassign(socket) do
    selected = App.Licenses.get_licence_data_by_license_id_summary(socket.assigns.get_params_id)

    {:noreply,
     socket
     |> assign(:updated_list, [])
     |> assign(:selected_ids, selected)
     |> assign(:filter, "")
     |> assign(:license_field, App.SummaryData.checking_off_toggle())
     |> push_navigate(to: "/license/summary/#{socket.assigns.get_params_id}")}
  end
end
