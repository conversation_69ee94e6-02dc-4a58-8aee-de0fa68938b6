defmodule App.Repo.Migrations.CreateAccessRoles do
  use Ecto.Migration

  def change do
    create table(:access_roles) do
      add :name, :string, size: 50, null: false
      add :description, :string, size: 150, null: false
      add :system_gen, :boolean, default: false, null: false
      add :editable, :boolean, default: true, null: false
      add :user_interface, :boolean, default: true, null: false
      add :status, :integer, default: 1

      add :deleted_at, :naive_datetime

      add :created_by, references(:tbl_users, on_delete: :nothing)
      add :updated_by, references(:tbl_users, on_delete: :nothing)
      add :department_role_id, references(:department_roles, on_delete: :nothing), null: false

      timestamps()
    end

    create unique_index(:access_roles, [:name, :department_role_id])
    create index(:access_roles, [:created_by])
    create index(:access_roles, [:updated_by])
    create index(:access_roles, [:department_role_id])
  end
end
