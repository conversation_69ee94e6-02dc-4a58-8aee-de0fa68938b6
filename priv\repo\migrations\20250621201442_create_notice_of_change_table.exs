defmodule App.Repo.Migrations.CreateNoticeOfChangeTable do
  use Ecto.Migration

  def change do
    create table(:notice_of_change) do
      add :data, :map, default: %{}
      add :user_id, references(:tbl_users, on_delete: :nothing), null: false
      add :company_id, references(:companies, on_delete: :nothing)
      add :license_id, references(:licenses, on_delete: :nothing)
      add :status, :integer, default: 0

      timestamps(type: :utc_datetime)
    end
  end
end
