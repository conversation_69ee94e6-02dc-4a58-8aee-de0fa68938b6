defmodule App.Service.Export.CsvDevice do
  @moduledoc false
  alias App.Service.Table.Maintenance.ServiceDevices
  alias App.Service.Export.Functions

  @headers [
    "DATE",
    "CODE",
    "API KEY",
    "STATUS"
  ]

  def index(payload) do
    ServiceDevices.export(payload)
    |> Stream.map(
      &[
        &1.inserted_at,
        &1.code,
        &1.api_key,
        Functions.table_numeric_status(&1.status)
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Device"],
              ["", "", "", "", ""],
              @headers,
              ["", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
