defmodule App.Service.Export.CsvSessionLogService do
  @moduledoc false
  alias App.Service.Logs.LogsSession
  alias App.Service.Export.Functions

  @headers [
    "DATE",
    "USERNAME",
    "PORTAL",
    "SESSION",
    "DESCRIPTION"
  ]

  def index(payload) do
    LogsSession.export(payload)
    |> Stream.map(
      &[
        &1.inserted_at,
        &1.user_id,
        &1.portal,
        Functions.table_boolean_status(&1.status),
        &1.description
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Session Logs"],
              ["", "", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
