<div class="px-4 sm:px-6 lg:px-8 mt-5">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">
        Application Steps
      </h1>
    </div>
  </div>
  <!-- FILTERS -->
  <%= if @showFilter do %>
    <div class="border overflow-hidden animate-slide-in-from-top transition-all ease-in-out duration-500 rounded-lg bg-white my-4 flex flex-col gap-4 items-start">
      <h1 class="px-4 py-2 bg-gray-50 border-b font-medium w-full">Filter approval report</h1>
      <FormJ.filter_form
        for={@form}
        class="w-full px-4"
        id="filter"
        phx-change="filter_change"
        phx-submit="search"
      >
        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 mb-4">
          <%!-- <FormJ.input_filter
            field={@form[:id]}
            type="text"
            label="Application"
            placeholder="Enter Record Name"
          /> --%>
          <FormJ.input_filter
            field={@form[:record_name]}
            type="text"
            label="Record Name"
            placeholder="Enter Record Name"
          />
          <FormJ.input_filter
            field={@form[:tpin]}
            type="text"
            label="TPIN"
            placeholder="Enter TPIN"
          />
          <FormJ.input_filter
            field={@form[:status]}
            type="select"
            label="Status"
            prompt="--Select Status--"
            options={[
              {"All", ""},
              {"PENDING APPROVAL", 0},
              {"DECLINED", -1}
            ]}
          />
        </div>

        <p class="text-gray-500 font-medium">Date Filters</p>
        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 pt-2">
          <FormJ.input_filter field={@form[:start_date]} type="date" label="From" />
          <FormJ.input_filter field={@form[:end_date]} type="date" label="To" />
        </div>
        <div class="flex gap-4 items-center justify-start mt-4 p-4 w-full border-t font-medium">
          <.button type="reset" class="cursor-pointer hover:text-brand-1 py-2">Reset</.button>
          <.button type="submit" class="cursor-pointer hover:text-brand-1 py-2">Search</.button>
        </div>
      </FormJ.filter_form>
    </div>
  <% end %>
  <!-- END OF FILTERS -->
  <div class="mt-8 flow-root">
    <div class="-mx-4 -my-2 sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <Table.main_table id="adverts" rows={@data} params={@params} data_loader={@data_loader}>
          <:col :let={rj} label={table_link(@params, "Issue Date", :inserted_at)}>
            <%= cond do
              is_struct(rj.inserted_at, NaiveDateTime) ->
                Calendar.strftime(
                  NaiveDateTime.add(rj.inserted_at, 7200, :second),
                  "%d %B %Y %H:%M:%S"
                )
                is_binary(rj.inserted_at) ->
                
                rj.inserted_at
              true ->
                "N/A"
            end %>
          </:col>
          <:col :let={rj} label={table_link(@params, "Page ID", :id)}>
            <%= rj.id %>
          </:col>

            <:col :let={rj} label={table_link(@params, "Page Name", :name)}>
                <%= rj.name %>
            </:col>

             <:col :let={rj} label={table_link(@params, "URL", :url)}>
                <%= rj.url %>
            </:col>
          
              <:col :let={rj} label={table_link(@params, "Status", :status)}>
              <Table.application_steps status={rj.status} />
    
             </:col>
          <:action :let={rj}>
            <Option.bordered>
              <%= if Map.get(rj, :revoke, rj.status == 1) do %>
                <.link
                  phx-click="update_status"
                  phx-value-status="0"
                  phx-value-id={rj.id}
                  class="w-full text-left text-rose-700 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-rose-900"
                >
                  edit
                </.link>
                  <.link
                  phx-click="view_document"
                  phx-value-doc_name={rj.name}
                 phx-value-id={rj.id}
                  class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-white"
                >
                  view
                </.link>
              <% else %>
                <.link
                  phx-click="update_status"
                  phx-value-status="1"
                  phx-value-id={rj.id}
                  class="w-full text-left text-green-700 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-green-900"
                >
                  wait
                </.link>
              <% end %>
            </Option.bordered>
          </:action>
        </Table.main_table>
      </div>
    </div>
  </div>
</div>

<Model.small :if={@live_action in [:filter]} id="transaction-modal" show return_to="close_model">
  <.live_component
    module={AppWeb.Component.DateFilter}
    id={:filter}
    title={@page_title}
    @click.outside="open = false"
    params={@params}
  />
</Model.small>

<Model.small :if={@live_action == :view_reason} id="view-modal" show return_to="close_model">
  <.live_component
    module={AppWeb.ServicesLive.ReasonComponent}
    id="view_records_data"
    title={@page_title}
    application={@application}
  />
</Model.small>

<%!-- <Model.fullscreen
  :if={@live_action in [:view_document]}
  id="view_document-modal"
  title={@doc_name}
  show
  return_to="close_model"
>
  <iframe
    src={@document}
    id="document_iframe"
    title="document"
    style="width: 100%;"
    height="700"
    name="DOCUMENT"
  >
  </iframe>
</Model.fullscreen> --%>
<Model.fullscreen
  :if={@live_action == :view_document}
  id="view_licence-modal"
  title="Licence Form"
  show
  return_to="close_model"
>
  <iframe
    src={@licence_form}
    id="licence_iframe"
    title="Licence"
    style="width: 100%;"
    height="700"
    name="Licence"
  >
  </iframe>
</Model.fullscreen>

<Model.confirmation_with_des
  :if={@live_action == :confirm_with_des}
  show
  id="confirmation_with_des-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
