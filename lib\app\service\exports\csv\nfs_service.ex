defmodule App.Service.Export.CsvNfsService do
  @moduledoc false
  alias App.Service.Table.NFS

  @headers [
    "DATE",
    "PARTICIPANT NAME",
    "INSTITUTION CODE",
    "TYPE",
    "STATUS"
  ]

  def index(payload) do
    NFS.export(payload)
    |> Stream.map(
      &[
        &1.inserted_at,
        &1.name,
        &1.inst_code,
        &1.type,
        Functions.table_numeric_status(&1.status)
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["NFS"],
              ["", "", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
