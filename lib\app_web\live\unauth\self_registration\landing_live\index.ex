defmodule AppWeb.SelfRegistration.LandingLive.Index do
  @moduledoc false
  use AppWeb, :live_view

  @impl true
  def render(assigns) do
    ~H"""
    <.navbar
      menu_open={@menu_open}
      login_url={~p"/users/log_in"}
      registration_url={~p"/users/signup"}
      img_src="/images/pbs-logo.png"
      current_page="SELF_REG"
    />
    <!-- Hero Section -->
    <div class="relative isolate pt-8 mt-8 overflow-hidden bg-gradient-to-br from-blue-50 via-white to-indigo-50 min-h-screen">
      <!-- Background decorative elements -->
      <div class="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80">
        <div class="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-blue-400 to-indigo-600 opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]">
        </div>
      </div>
      <!-- Main Content -->
      <div class="mx-auto max-w-7xl px-6 pb-8 pt-2 sm:pb-32 lg:flex lg:px-2 lg:py-2">
        <div class="mx-auto max-w-2xl flex-shrink-0 lg:mx-0 lg:max-w-xl lg:pt-8">
          <!-- Animated heading -->
          <div class="animate-fade-in-up">
            <h1 class="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              Choose Your
              <span class="bg-brand-10 bg-clip-text text-transparent">
                Registration Type
              </span>
            </h1>

            <p class="mt-6 text-lg leading-8 text-gray-600">
              Whether you are an individual or a business, we have the perfect registration option for you.
            </p>
          </div>
          <!-- Registration Type Cards -->
          <div class="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2">
            <!-- Individual Registration Card -->
            <div class="group relative overflow-hidden rounded-2xl bg-white p-8 shadow-lg ring-1 ring-gray-200 hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer animate-fade-in-up animation-delay-200">
              <div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              </div>

              <div class="relative">
                <!-- Icon -->
                <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-blue-100 group-hover:bg-blue-200 transition-colors duration-300">
                  <svg
                    class="h-6 w-6 text-blue-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z"
                    />
                  </svg>
                </div>
                <!-- Content -->
                <h3 class="mt-4 text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                  Individual
                </h3>

                <p class="mt-2 text-sm text-gray-600">
                  Perfect for individuals.
                </p>
                <!-- Features -->
                <ul class="mt-4 space-y-2 text-sm text-gray-600">
                  <li class="flex items-center">
                    <svg class="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    Personal dashboard
                  </li>

                  <li class="flex items-center">
                    <svg class="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    Personalized profile
                  </li>

                  <li class="flex items-center">
                    <svg class="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    Individual analytics
                  </li>
                </ul>
                <!-- CTA Button -->
                <.link navigate={~p"/users/signup/individual"}>
                  <button class="mt-6 w-full rounded-lg bg-brand-10 px-4 py-2.5 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200 group-hover:scale-105">
                    Register as Individual
                  </button>
                </.link>
              </div>
            </div>
            <!-- Business Registration Card -->
            <div class="group relative overflow-hidden rounded-2xl bg-white p-8 shadow-lg ring-1 ring-gray-200 hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer animate-fade-in-up animation-delay-400">
              <div class="absolute inset-0 bg-gradient-to-br from-indigo-50 to-purple-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              </div>

              <div class="relative">
                <!-- Icon -->
                <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-indigo-100 group-hover:bg-indigo-200 transition-colors duration-300">
                  <svg
                    class="h-6 w-6 text-indigo-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18m2.25-18v18M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.75m-.75 3h.75m-.75 3h.75m-3.75-16.5h.75m-.75 3h.75m-.75 3h.75m3-6h.75M21 21v-7.5"
                    />
                  </svg>
                </div>
                <!-- Content -->
                <h3 class="mt-4 text-xl font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors duration-300">
                  Business
                </h3>

                <p class="mt-2 text-sm text-gray-600">
                  Ideal for companies
                </p>
                <!-- Features -->
                <ul class="mt-4 space-y-2 text-sm text-gray-600">
                  <li class="flex items-center">
                    <svg class="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    Business dashboard
                  </li>

                  <li class="flex items-center">
                    <svg class="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    Representative Management
                  </li>

                  <li class="flex items-center">
                    <svg class="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    Advanced analytics
                  </li>
                </ul>
                <!-- CTA Button -->
                <.link navigate={~p"/users/signup/business"}>
                  <button class="mt-6 w-full rounded-lg bg-brand-10 px-4 py-2.5 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200 group-hover:scale-105">
                    Register as Business
                  </button>
                </.link>
              </div>
            </div>
          </div>
          <!-- Additional Info -->
          <div class="mt-8 animate-fade-in-up animation-delay-600">
            <p class="text-sm text-gray-500">
              Already have an account?
              <a
                href={~p"/users/log_in"}
                class="font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200"
              >
                Sign in
              </a>
            </p>
          </div>
        </div>
        <!-- Right side illustration/features -->
        <div class="mx-auto mt-16 flex max-w-2xl sm:mt-24 lg:ml-20 lg:mr-0 lg:mt-0 lg:max-w-none lg:flex-none xl:ml-48">
          <div class="max-w-3xl flex-none sm:max-w-5xl lg:max-w-none animate-fade-in-right">
            <div class="relative">
              <!-- Floating cards animation -->
              <div class="relative mx-auto w-full max-w-lg lg:ml-12 xl:ml-20">
                <!-- Card 1 -->
                <div class="absolute top-10 left-8 w-64 h-40 bg-white rounded-xl shadow-lg p-6 animate-float animation-delay-0">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <svg
                        class="w-5 h-5 text-blue-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>

                    <div>
                      <h4 class="font-medium text-gray-900">Secure</h4>

                      <p class="text-sm text-gray-500">2FA Authenticated</p>
                    </div>
                  </div>
                </div>
                <!-- Card 2 -->
                <div class="absolute top-20 right-8 w-64 h-40 bg-white rounded-xl shadow-lg p-6 animate-float animation-delay-1000">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                      <svg
                        class="w-5 h-5 text-indigo-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M13 10V3L4 14h7v7l9-11h-7z"
                        />
                      </svg>
                    </div>

                    <div>
                      <h4 class="font-medium text-gray-900">Fast</h4>

                      <p class="text-sm text-gray-500">Quick setup process</p>
                    </div>
                  </div>
                </div>
                <!-- Card 3 -->
                <div class="absolute top-40 left-16 w-64 h-40 bg-white rounded-xl shadow-lg p-6 animate-float animation-delay-2000">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <svg
                        class="w-5 h-5 text-green-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                        />
                      </svg>
                    </div>

                    <div>
                      <h4 class="font-medium text-gray-900">Application Tracking</h4>

                      <p class="text-sm text-gray-500">Track your applications seamlessly</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Bottom decorative element -->
      <div class="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]">
        <div class="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-indigo-400 to-blue-600 opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]">
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    socket
    |> assign(page_title: "Self Registration")
    |> assign(menu_open: false)
    |> ok()
  end
end
