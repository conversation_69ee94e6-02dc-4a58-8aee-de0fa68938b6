defmodule App.Service.Export.ClientProfileServicePdf do
  alias App.Service.Table.ClientMaintenance

  alias App.Service.Export.{
    Functions
  }

  def index(payload) do
    results =
      ClientMaintenance.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "inserted_at" => data.inserted_at,
          "email" => data.email,
          "first_name" => data.first_name,
          "last_name" => data.last_name,
          "mobile" => data.mobile,
          "status" => Functions.table_numeric_status(data.status)
        }
      end)
      |> Enum.map(fn data ->
        """
         <tr>
            <td style="text-align: center;">{{ inserted_at }}</td>
            <td style="text-align: center;">{{ email }}</td>
            <td style="text-align: center;">{{ first_name }}</td>
            <td style="text-align: center;">{{ last_name }}</td>
            <td style="text-align: center;">{{ mobile }}</td>
            <td style="text-align: center;">{{ status }}</td>
         </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/client_profile.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Client Profile",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end
