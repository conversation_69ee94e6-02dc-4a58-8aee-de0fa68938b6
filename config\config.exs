# This file is responsible for configuring your application
# and its dependencies with the aid of the Config module.
#
# This configuration file is loaded before any dependency and
# is restricted to this project.

# General application configuration
import Config

config :app,
  ecto_repos: [App.Repo, Logs.LogRepo],
  generators: [
    timestamp_type: :utc_datetime
  ]

#   generators: [
#     timestamp_type: {:utc_datetime, "Africa/Lusaka"}
#   ]

config :app, Logs.LogRepo, migration_source: "priv/log_repo/migrations"

# Configures the endpoint
config :app,
       AppWeb.Endpoint,
       url: [
         host: "localhost"
       ],
       adapter: Bandit.PhoenixAdapter,
       render_errors: [
         formats: [
           html: AppWeb.ErrorHTML,
           json: AppWeb.ErrorJSON
         ],
         layout: false
       ],
       pubsub_server: App.PubSub,
       live_view: [
         signing_salt: "N3lwliD3"
       ],
       check_origin: [
         "//************",
         "//127.0.0.1",
         "//localhost",
         "//sec.sms.probasegroup.com",
         "//sms.probasegroup.com"
       ]

# Configures the mailer
#
# By default it uses the "Local" adapter which stores the emails
# locally. You can see the emails in your browser, at "/dev/mailbox".
#
# For production it's recommended to configure a different adapter
# at the `config/runtime.exs`.
# config :app, App.Mailer, adapter: Swoosh.Adapters.Local

# Configure esbuild (the version is required)
config :esbuild,
  version: "0.17.11",
  app: [
    args:
      ~w(js/app.js --bundle --target=es2017 --outdir=../priv/static/assets --external:/fonts/* --external:/images/*),
    cd: Path.expand("../assets", __DIR__),
    env: %{
      "NODE_PATH" => Path.expand("../deps", __DIR__)
    }
  ]

# Configure tailwind (the version is required)
config :tailwind,
  version: "3.4.0",
  app: [
    args: ~w(
        --config=tailwind.config.js
        --input=css/app.css
        --output=../priv/static/assets/app.css
     ),
    cd: Path.expand("../assets", __DIR__)
  ]

# Configures Elixir's Logger
config :logger,
       :console,
       format: "$time $metadata[$level] $message\n",
       metadata: [:request_id]

config :app,
       App.JobScheduler,
       overlap: false,
       jobs: [
         licence_approvals: [
           schedule: {:extended, "*/5"},
           task: {App.Jobs.LicenceApprovals, :init, []}
         ],
         condition_expire: [
           schedule: {:extended, "*/10"},
           task: {App.Jobs.JobConditionTracking, :init, []}
         ],
         send_reason_condition_expire: [
           schedule: {:extended, "*/10"},
           task: {App.Jobs.JobConditionSendReason, :init, []}
         ],
         amount_validation: [
           schedule: {:extended, "*/5"},
           task: {App.Jobs.JobCheckBalance, :init, []}
         ]
       ]

# config :logger,
#   backends: [:console, {App.ExceptionLogger, []}],
#   level: :info

# Use Jason for JSON parsing in Phoenix
config :phoenix, :json_library, Jason

# Import environment specific config. This must remain at the bottom
# of this file so it overrides the configuration defined above.
import_config "#{config_env()}.exs"
