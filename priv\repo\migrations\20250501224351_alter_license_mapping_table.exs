defmodule App.Repo.Migrations.AlterLicenseMappingTable do
  use Ecto.Migration

  def change do
    alter table(:user_license_mapping) do
      add :show_summary, :boolean, default: false
      add :revoked, :boolean, default: false
      add :condition_tracking, :boolean, default: false
      add :condition_status, :integer, default: 0
    end

    alter table(:approval_levels) do
      add :categories_id, references(:license_categories, on_delete: :nothing)
    end

    create unique_index(:approval_levels, [:categories_id, :approval_status])
  end
end
