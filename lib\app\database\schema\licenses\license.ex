defmodule App.Licenses.License do
  use Ecto.Schema
  import Ecto.Changeset

  schema "licenses" do
    field :form_number, :integer
    field :role_id, :integer
    field :name, :string
    field :type, :string, default: "INDIVIDUAL"
    field :color, :string
    field :icon, :string
    field :section, :integer
    field :security_act_no, :integer
    field :note, :string
    field :count_down_days, :integer
    field :reason, :string
    field :status, :string, default: "A"
    field :primary_key, :string
    field :require_license, :boolean, default: false
    field :file_path, :string
    field :amount, :decimal, default: 0.0
    field :other_fees, :decimal, default: 0.0
    belongs_to :associated_license, App.Licenses.License
    belongs_to :categories, App.Licenses.LicenseCategories
    belongs_to :certificate, App.Licenses.LicenseCertificates

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(license, attrs) do
    license
    |> cast(attrs, [
      :icon,
      :color,
      :reason,
      :section,
      :note,
      :security_act_no,
      :name,
      :role_id,
      :form_number,
      :status,
      :associated_license_id,
      :categories_id,
      :primary_key,
      :require_license,
      :file_path,
      :count_down_days,
      :amount,
      :other_fees,
      :certificate_id,
      :type
    ])
    |> validate_required([:name, :status, :security_act_no, :note, :section, :amount, :type])
  end
end
