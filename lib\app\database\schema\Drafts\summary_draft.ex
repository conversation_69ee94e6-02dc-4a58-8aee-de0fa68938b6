defmodule App.SummaryDrafts.SummaryDraft do
  use Ecto.Schema
  import Ecto.Changeset

  schema "summary_drafts" do
    field :template, :string
    field :template_data, :string
    field :name, :string
    field :service, :string
    field :status, :string
    belongs_to :license, App.Licenses.License
    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(representative_draft, attrs) do
    representative_draft
    |> cast(attrs, [:template, :template_data, :service, :status, :name, :license_id])
    |> validate_required([:template])
  end
end
