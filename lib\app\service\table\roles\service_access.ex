defmodule App.Services.Table.AccessRoles do
  @moduledoc false

  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false

  alias App.{
    Accounts.User,
    Util.CustomFunctions,
    Roles,
    Repo
  }

  @pagination [page_size: 10]

  def index(params) do
    Roles.get_access_roles_query(params["role"])
    |> compose_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  defp compose_query(query, search_params) do
    join(query, :left, [a], b in assoc(a, :created_by_user))
    |> join(:left, [a, b], c in assoc(a, :updated_by_user))
    |> handle_filter(search_params)
    |> compose_card_select()
  end

  def export(params) do
    Roles.get_access_roles_query()
    |> compose_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> Repo.all()
  end

  defp handle_filter(query, nil), do: query

  defp handle_filter(query, params) do
    Enum.reduce(
      params,
      query,
      fn
        {"isearch", value}, query when byte_size(value) > 0 ->
          isearch_filter(query, sanitize_term(value))

        {"name", value}, query when byte_size(value) > 0 ->
          where(query, [a], fragment("lower(?) LIKE lower(?)", a.name, ^value))

        {"status", value}, query when byte_size(value) > 0 ->
          where(query, [b], b.status == type(^value, :integer))

        {"start_date", value}, query when byte_size(value) > 0 ->
          where(
            query,
            [a],
            fragment(
              "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
              a.inserted_at,
              ^"#{value} 00:00:00"
            )
          )

        {"end_date", value}, query when byte_size(value) > 0 ->
          where(
            query,
            [a],
            fragment(
              "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
              a.inserted_at,
              ^"#{value} 23:59:59"
            )
          )

        {_, _}, query ->
          # Not a where parameter
          query
      end
    )
  end

  defp compose_card_select(query) do
    select(
      query,
      [a, b, c],
      %{
        name: a.name,
        inserted_at: a.inserted_at,
        description: a.description,
        permissions:
          fragment("select count(id) from access_role_permissions where access_role_id=?", a.id),
        created_by: b.email,
        updated_by: c.email,
        user: fragment("select count(id) from tbl_users where role_id=?", a.id),
        id: a.id,
        editable: a.editable,
        status: a.status,
        system_gen: a.system_gen
      }
    )
  end

  # defp compose_card_select(query) do
  #   select(
  #     query,
  #     [a, b, c],
  #     %{
  #       name: a.name,
  #       inserted_at: a.inserted_at,
  #       description: a.description,
  #       permissions: a.permissions,
  #       # Combine first and last name
  #       created_by: fragment("? || ' ' || ?", b.first_name, b.last_name),
  #       # Combine first and last name
  #       updated_by: fragment("? || ' ' || ?", c.first_name, c.last_name),
  #       users: fragment("select count(id) from tbl_users where role_id=?", a.id),
  #       id: a.id,
  #       editable: a.editable,
  #       status: a.status,
  #       system_gen: a.system_gen
  #     }
  #   )
  # # end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      #         fragment("lower(?) LIKE lower(?)", a.description, ^search_term) or
      fragment("lower(?) LIKE lower(?)", a.name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", b.email, ^search_term) or
        fragment("lower(?) LIKE lower(?)", c.email, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
