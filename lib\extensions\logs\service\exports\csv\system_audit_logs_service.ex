defmodule App.Service.Export.CsvSystemAuditLogs do
  @moduledoc false
  alias App.Service.Logs.System

  @headers [
    "DATE",
    "USER",
    "SERVICE",
    "ACTION",
    "IP ADDRESSES",
    "DESCRIPTION"
  ]

  def index(payload) do
    System.export(payload)
    |> Stream.map(
      &[
        Calendar.strftime(
          NaiveDateTime.add(&1.inserted_at, 7200, :second),
          "%d %B %Y %H:%M:%S"
        ),
        &1.user,
        &1.service,
        &1.action,
        &1.ip_address,
        &1.narration
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["System Audit Logs"],
              ["", "", "", "", ""],
              @headers,
              ["", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
