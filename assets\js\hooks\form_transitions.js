const FormTransitions = {
  mounted() {
    this.handleTransitions()
  },

  updated() {
    this.handleTransitions() 
  },

  handleTransitions() {
    // Add entrance animations
    this.el.classList.add('opacity-0')
    setTimeout(() => {
      this.el.classList.remove('opacity-0')
      this.el.classList.add('opacity-100')
    }, 50)

    // Add exit animations
    this.handleEvent("hide-form", () => {
      this.el.classList.add('animate-slide-out')
      setTimeout(() => {
        this.el.classList.add('hidden')
      }, 500)
    })
  }
}

export default FormTransitions
