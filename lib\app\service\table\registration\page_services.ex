# defmodule App.Table.Page.Service do
#   @moduledoc """
#   The Registration context.
#   """

#   import Ecto.Query, warn: false
#   alias App.Registration.Pages
#   alias App.Registration.ApplicationSteps
#   alias App.Repo


#   def index(assigns, params) do
#     compose_query(params, assigns)
#     |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
#   end

#   def export(assigns, params) do
#     compose_query(params, assigns)
#     |> Repo.all()
#   end

#   def compose_query(params, _assigns) do
#     LicenseMapping
#     |> join(:left, [a], b in assoc(a, :license))
#     |> where(
#       [a],
#       a.approval_status and a.approved
#     )
#     |> compose_filter_query(params["filter"])
#     |> order_by(^sort(params["order_by"]))
#     |> compose_select()
#   end

#   defp compose_filter_query(query, nil), do: query

#   defp compose_filter_query(query, filter) do
#     Enum.reduce(filter, query, fn
#       {"isearch", value}, query when byte_size(value) > 0 ->
#         isearch_filter(query, sanitize_term(value))

#       {"record_name", value}, query when byte_size(value) > 0 ->
#         where(
#           query,
#           [a],
#           fragment("lower(?) LIKE lower(?)", a.record_name, ^sanitize_term(value))
#         )

#       {"status", value}, query when byte_size(value) > 0 ->
#         where(query, [a], a.status == type(^value, :integer))

#       {"start_date", value}, query when byte_size(value) > 0 ->
#         where(
#           query,
#           [a],
#           fragment(
#             "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
#             a.inserted_at,
#             ^"#{value} 00:00:00"
#           )
#         )

#       {"end_date", value}, query when byte_size(value) > 0 ->
#         where(
#           query,
#           [a],
#           fragment(
#             "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
#             a.inserted_at,
#             ^"#{value} 23:59:59"
#           )
#         )

#       {_, _}, query ->
#         # Not a where parameter
#         query
#     end)
#   end

#   defp compose_select(query) do
#     select(query, [a, b], %{
#       id: a.id,
#       license_id: a.license_id,
#       data: a.data,
#       licence_name: b.name,
#       certificate_id: b.certificate_id,
#       record_name: a.record_name,
#       revoked: a.revoked,
#       status: a.status,
#       condition_tracking: a.condition_tracking,
#       approved: a.approved,
#       inserted_at: a.inserted_at,
#       updated_at: a.updated_at
#     })
#   end

#   defp isearch_filter(query, search_term) do
#     where(
#       query,
#       [a, b],
#       fragment("lower(?) LIKE lower(?)", a.record_name, ^search_term) or
#         fragment("lower(?) LIKE lower(?)", b.name, ^search_term)
#     )
#   end

#   defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
# end
