defmodule App.Service.Functions.AssociateUpload do
  use AppWeb, :file_function

  alias App.Companies

  def execute(socket, path, params) do
    Extract.validate_excel_and_csv(path)
    |> case do
      {:ok, col_count, file} ->
        cond do
          col_count == 0 ->
            {:error, :start, "No records found in the uploaded file."}

          col_count < 1 ->
            {:error, :start, "System expecting 1 columns but only got #{col_count}."}

          col_count > 1 ->
            {:error, :start, "System expecting 1 columns but got #{col_count}."}

          true ->
            file
            |> case do
              {:ok, txn} ->
                extraction_process(path, txn)

              {:error, message} ->
                {:error, :start, message}
            end
        end

      error ->
        error
    end
  end

  defp extraction_process(path, txn) do
    txn
    |> Extract.add_index()
    |> Enum.map(fn x ->
      nrc = String.trim(x["col1"])

      cond do
        nrc == "" ->
          message(0, "NRC cannot be blank", x)

        not Regex.match?(~r/^\d{6}\/\d{2}\/\d{1}$/, nrc) ->
          message(0, "NRC format is invalid! Allowed format: (******/**/*)", x)

        true ->
          case check_associate(nrc) do
            {:error, msg} ->
              message(0, msg, x)

            :ok ->
              message(1, "Valid record", x)
          end
      end
    end)
    |> Extract.finalize_process(
      path,
      # successful message, encase the process is a success
      "All Records in the file are valid, proceed to update them.",
      # failed message, encase the process failed
      "NRCs from the uploaded file are invalid. Please review and clean up the file, or proceed with the invalid records included."
    )
  end

  def process_file(socket, params, attrs) do
    entries = Jason.decode!(attrs["entries"])

    Companies.bulk_attach_applications(socket, params, entries)
    |> case do
      {:ok, txn} ->
        {:ok, "Successfully added #{Enum.count(entries)} messages", txn}

      {:error, message} ->
        {:error, message}
    end
  end

  def message(status, message, params) do
    %{
      status: status,
      col1: params["col1"],
      key: params["key"] + 1,
      message: message
    }
  end

  defp check_associate(nrc) do
    Companies.search_single_assoc!(nrc)
    |> case do
      nil ->
        {:error, "No associate found with NRC: #{nrc}"}

      associate ->
        cond do
          associate.user.company_id ->
            {:error, "Associate with NRC: #{nrc} is already attached to a company."}

          true ->
            :ok
        end
    end
  end
end
