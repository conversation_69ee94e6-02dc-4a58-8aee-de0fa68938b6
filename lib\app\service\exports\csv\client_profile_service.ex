defmodule App.Service.Export.CsvClientProfileService do
  @moduledoc false
  alias App.Service.Table.ClientMaintenance
  alias App.Service.Export.Functions

  @headers [
    "Created at",
    "Email",
    "First Name",
    "Last Name",
    "Mobile Number",
    "Status"
  ]

  def index(payload) do
    ClientMaintenance.export(payload)
    |> Stream.map(
      &[
        &1.inserted_at,
        &1.email,
        &1.first_name,
        &1.last_name,
        &1.mobile,
        Functions.table_numeric_status(&1.status)
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Client Profiles"],
              ["", "", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
