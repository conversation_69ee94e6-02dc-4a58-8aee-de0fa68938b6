<div class="px-4 sm:px-6 lg:px-8 mt-5">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">
        Service Provider Reports
      </h1>
    </div>
  </div>
  <!-- FILTERS -->
  <%= if @showFilter do %>
    <div class="border overflow-hidden animate-slide-in-from-top transition-all ease-in-out duration-500 rounded-lg bg-white my-4 flex flex-col gap-4 items-start">
      <h1 class="px-4 py-2 bg-gray-50 border-b font-medium w-full">
        Filter Payment Provider Reports
      </h1>

      <FormJ.filter_form
        for={@form}
        class="w-full px-4"
        id="filter"
        phx-change="filter_change"
        phx-submit="search"
      >
        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-2 pt-2">
          <FormJ.input_filter
            field={@form[:year]}
            type="select"
            options={@years}
            label="Year"
            prompt="---Select a year---"
            required
          />
          <FormJ.input_filter
            field={@form[:month]}
            type="select"
            options={@months}
            label="Month"
            prompt="---Select a month---"
            required
          />
        </div>

        <div class="flex gap-4 items-center justify-start mt-4 p-4 w-full border-t font-medium">
          <.button
            type="button"
            phx-click="reset_filter"
            class="cursor-pointer hover:text-brand-1 py-2"
          >
            Reset
          </.button>

          <.button type="submit" class="cursor-pointer hover:text-brand-1 py-2">Search</.button>
        </div>
      </FormJ.filter_form>
    </div>
  <% end %>
  <!-- END OF FILTERS -->
  <div class="mt-8 flow-root">
    <div class="-mx-4 -my-2 sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <Table.main_table id="adverts" rows={@data} params={@params} data_loader={@data_loader}>
          <:col :let={rj} label={table_link(@params, "SERVICE PROVIDER", :provider)}>
            <%= rj.courier %>
          </:col>
          <:col :let={rj} class="text-right" label={table_link(@params, "SENT", :sent)}>
            <%= NumberF.comma_separated(rj.sent, 0) %>
          </:col>

          <:col :let={rj} class="text-right" label={table_link(@params, "DELIVERED", :delivered)}>
            <%= NumberF.comma_separated(rj.delivered, 0) %>
          </:col>

          <:col :let={rj} class="text-right" label={table_link(@params, "FAILED", :failed)}>
            <%= NumberF.comma_separated(rj.failed, 0) %>
          </:col>
          <:col :let={rj} class="text-right" label={table_link(@params, "PERIOD", :period)}>
            <%= rj.period %>
          </:col>
        </Table.main_table>
      </div>
    </div>
  </div>
</div>
