defmodule AppWeb.UserSessionChannel do
  @moduledoc false
  use AppWeb, :channel
  alias AppWeb.Presence

  @impl true
  def join("user_session:lobby", _message, socket) do
    # send(self(), :after_join)
    {:ok, socket}
  end

  @impl true
  def join("user_session:" <> _user_id, payload, socket) do
    if authorized?(payload) do
      {:ok, socket}
    else
      {:error, %{reason: "unauthorized"}}
    end
  end

  def handle_info(:after_join, socket) do
    # Presence.track(socket, socket.assigns.user, %{
    #   online_at: System.system_time(:second)
    # })
    # push(socket, "presence_state", Presence.list(socket))
    {:ok, _} = {:noreply, socket}
  end

  # Channels can be used in a request/response fashion
  # by sending replies to requests from the client
  @impl true
  def handle_in("ping", payload, socket) do
    {:reply, {:ok, payload}, socket}
  end

  @impl true
  def handle_info({App.PresenceClient, {:join, %{user: _user_id} = _user_data}}, socket) do
    users_list = Presence.list(socket)

    Phoenix.PubSub.local_broadcast(
      App.PubSub,
      "presence_state",
      {:online_user_count, Enum.count(users_list)}
    )

    push(socket, "presence_state", users_list)
    {:noreply, socket}
  end

  @impl true
  def handle_info({App.PresenceClient, {:leave, %{user: _user_id} = _user_data}}, socket) do
    users_list = Presence.list(socket)

    Phoenix.PubSub.local_broadcast(
      App.PubSub,
      "presence_state",
      {:online_user_count, Enum.count(users_list)}
    )

    push(socket, "presence_state", users_list)
    {:noreply, socket}
  end

  # It is also common to receive messages from the client and
  # broadcast to everyone in the current topic (user:lobby).
  @impl true
  def handle_in("shout", payload, socket) do
    broadcast(socket, "shout", payload)
    {:noreply, socket}
  end

  # Add authorization logic here as required.
  defp authorized?(_payload) do
    true
  end
end
