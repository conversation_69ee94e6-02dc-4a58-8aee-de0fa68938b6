defmodule AppWeb.Dashboard.SystemTrack do
  use AppWeb, :live_view
  alias Logs.Health
  alias App.CustomContext

  def render(assigns) do
    ~H"""
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-sm font-semibold text-gray-700">Name</th>
          <th scope="col" class="px-6 py-3 text-left text-sm font-semibold text-gray-700">
            Endpoint
          </th>
          <th scope="col" class="px-6 py-3 text-left text-sm font-semibold text-gray-700">Status</th>
          <th scope="col" class="px-6 py-3 text-left text-sm font-semibold text-gray-700">
            Last Seen
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <%= for system <- @systems do %>
          <tr class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <%= system.name %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <%= system.endpoint %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class={[
                "px-2 py-1 text-xs font-medium rounded-full",
                case system.endpoint_status do
                  1 -> "bg-green-100 text-green-800"
                  2 -> "bg-red-100 text-red-800"
                  3 -> "bg-yellow-100 text-yellow-800"
                  _ -> "bg-gray-100 text-gray-800"
                end
              ]}>
                <%= case system.endpoint_status do
                  1 -> "Online"
                  2 -> "Offline"
                  3 -> "idle"
                  _ -> "Other"
                end %>
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <%= time_ago(system.last_seen) %>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
    """
  end

  def mount(_params, _session, socket) do
    CustomContext.subscribe("ping_endpoint")

    systems =
      Health.get_active_health_check()
      |> Enum.sort_by(&(&1.name > &1.name))

    {:ok, assign(socket, systems: systems)}
  end

  def handle_info({App.CustomContext, "ping", data}, socket) do
    systems =
      Enum.map(socket.assigns.systems, fn ep ->
        if ep.name === data.name and ep.endpoint === data.endpoint,
          do: data,
          else: ep
      end)

    {:noreply, assign(socket, systems: Enum.sort_by(systems, &(&1.name > &1.name)))}
  end

  defp time_ago(nil), do: "Never seen"

  defp time_ago(datetime) do
    now = Timex.now()
    diff_seconds = Timex.diff(now, datetime, :seconds)
    diff_minutes = div(diff_seconds, 60)
    diff_hours = div(diff_seconds, 3600)
    diff_days = div(diff_seconds, 86_400)
    diff_weeks = div(diff_seconds, 604_800)
    # Approx. 30 days
    diff_months = div(diff_seconds, 2_592_000)
    # 365 days
    diff_years = div(diff_seconds, 31_536_000)

    cond do
      diff_seconds < 60 -> "Just now"
      diff_minutes < 60 -> "#{diff_minutes} minute#{plural(diff_minutes)} ago"
      diff_hours < 24 -> "#{diff_hours} hour#{plural(diff_hours)} ago"
      diff_days < 7 -> "#{diff_days} day#{plural(diff_days)} ago"
      diff_weeks < 4 -> "#{diff_weeks} week#{plural(diff_weeks)} ago"
      diff_months < 12 -> "#{diff_months} month#{plural(diff_months)} ago"
      true -> "#{diff_years} year#{plural(diff_years)} ago"
    end
  end

  defp plural(1), do: ""
  defp plural(_), do: "s"
end
