defmodule AppWeb.Auth.Registration.SummaryLive.Index do
  @moduledoc false

  use AppWeb, :live_view
  alias Logs.Audit
  alias App.Licenses
  alias App.Licenses.DynamicSchema
  alias App.Validators.DynamicFormValidator
  alias AppWeb.Auth.RegistrationLive.Entry

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("license_registration", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Licence Registration", "Accessed Licence Registration Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      socket =
        socket
        |> assign(data_loader: true)
        |> assign(live_socket_id: session["live_socket_id"])

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Licence Registration", "Accessed Licence Registration Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_data, params})

    {
      :noreply,
      socket
      |> assign(:params, params)
      |> apply_action(socket.assigns.live_action, params)
    }
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      {:get_data, params} ->
        get_update(socket, params)

      {:process_registration, params} ->
        submit(socket, params)

      {AppWeb.LiveFunctions, message} ->
        send(self(), {:get_data, %{}})

        {
          :noreply,
          socket
          |> put_flash(:info, message)
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }
    end
  end

  @impl true
  def handle_event(target, attrs, %{assigns: assigns} = socket) do
    case target do
      "submit" ->
        send(self(), {:process_registration, assigns.data})

        {
          :noreply,
          socket
          |> assign(data_loader: true)
        }

      "back" ->
        {:noreply, pre_position(socket)}
    end
  end

  defp submit(socket, _data) do
    case Licenses.insert_or_update_registration(
           socket,
           socket.assigns.record,
           socket.assigns.params
         ) do
      {:ok, _record} ->
        {
          :noreply,
          push_navigate(
            LiveFunctions.sweet_alert(socket, "Application Submitted Successfully", "success"),
            to: ~p"/dashboard"
          )
        }

      {:error, message} ->
        LiveFunctions.sweet_alert(socket, message, "error")

        {
          :noreply,
          assign(socket, data_loader: false)
        }
    end
  end

  defp get_update(socket, params) do
    Entry.init(socket, params, "summary")
  end

  defp next_position(socket, record) do
    get_next_page =
      socket.assigns.steps
      |> Enum.find(fn step -> step.number > socket.assigns.current_position end)

    push_navigate(socket, to: "#{get_next_page.step.url}#{record.license_id}")
  end

  defp pre_position(socket) do
    get_prev_page =
      socket.assigns.steps
      |> Enum.filter(fn step -> step.number < socket.assigns.current_position end)
      |> Enum.max_by(& &1.number, fn -> nil end)

    Licenses.update_current_step(socket.assigns.record, get_prev_page.number)
    |> case do
      {:ok, record} ->
        push_navigate(socket, to: "#{get_prev_page.step.url}#{record.license_id}")

      {:error, _error} ->
        :error
    end
  end

  defp pre_position(socket), do: socket
end
