<div class="px-4 sm:px-6 lg:px-8">
  <div class="sm:flex sm:items-center mt-8">
    <div class="sm:flex-auto">
      <h1 class="text-xl font-semibold leading-6 text-gray-900">Summary Drafts</h1>
      <p class="mt-2 text-sm text-gray-700">
        Manage and edit license summary draft templates.
      </p>
    </div>

    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
      <button
        type="button"
        phx-click="refresh_table"
        class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 mr-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
        Refresh
      </button>
    </div>
  </div>

  <div class="flex flex-row justify-between bg-white mt-6 rounded-lg shadow">
    <Table.sidebar
      live_action={@live_action}
      table_data={@data}
      selected_item={@selected_item}
      params={@params}
      data_loader={@data_loader}
    />
    <div class="w-full flex flex-col justify-between h-[70vh] border-l border-gray-200">
      <Table.sidebar_show_rich_text
        :if={@live_action == :show}
        selected_info={@selected_info}
        edit_form={@edit_form}
        name="licence_drafts"
        selected_item={@selected_item}
        selected_item_name={@selected_item_name}
        changeset={@changeset}
      />
      <Table.empty_sidebar_data :if={@live_action == :index} />
      <Table.done_sidebar_data
        :if={@live_action == :done}
        message={assigns[:display_message_text]}
      />
    </div>
  </div>

  <.live_component
    module={PaginationComponent}
    id="PaginationComponent1"
    params={@params}
    pagination_data={@data}
  />
</div>

<Model.confirmation_model
  :if={@live_action == :confirm}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
