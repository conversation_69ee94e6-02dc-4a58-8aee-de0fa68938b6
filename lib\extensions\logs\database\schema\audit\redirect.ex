defmodule Logs.Audit.Redirect do
  use AppWeb, :schema

  schema "logs_redirect" do
    field :device_uuid, :string
    field :full_browser_name, :string
    field :ip_address, :string
    field :known_browser, :boolean, default: false
    field :reference, :string
    field :request, :string
    field :response, :string
    field :service, :string
    field :system_platform_name, :string

    timestamps()
  end

  @doc false
  def changeset(redirect, attrs) do
    redirect
    |> cast(attrs, [
      :device_uuid,
      :reference,
      :ip_address,
      :service,
      :request,
      :response,
      :full_browser_name,
      :system_platform_name,
      :known_browser
    ])
    |> validate_required([
      :device_uuid,
      :reference,
      :ip_address,
      :service,
      :request,
      :response,
      :full_browser_name,
      :system_platform_name,
      :known_browser
    ])
  end
end
