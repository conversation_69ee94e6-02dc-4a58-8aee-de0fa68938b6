defmodule AppWeb.Auth.RegistrationLive.Entry do
  use AppWeb, :live_view
  alias App.{Licenses, Registration}
  alias App.Licenses.DynamicSchema
  alias App.Validators.DynamicFormValidator

  def init(socket, params, service \\ "text_fields") do
    cond do
      service == "text_fields" ->
        text_fields(socket, params)

      service == "uploads" ->
        uploads(socket, params)

      service == "payments" ->
        paymemts(socket, params)

      service == "summary" ->
        summary(socket, params)
    end
  end

  defp text_fields(%{assigns: assigns} = socket, params) do
    license_fields = Licenses.get_license_data(params["license_id"])

    # Organize fields

    upload_fields = Enum.filter(license_fields, &(&1.field_type == "upload"))

    text_fields =
      Enum.filter(
        license_fields,
        &(&1.field_type in [
            "text",
            "date",
            "select",
            "number",
            "textarea",
            "checkbox",
            "radio",
            "checkbox_group"
          ])
      )
      |> Enum.sort_by(fn field ->
        case field.field_type do
          "text" -> 0
          "date" -> 1
          "textarea" -> 3
          _ -> 2
        end
      end)

    input_fields =
      text_fields
      |> Enum.map(fn field ->
        {String.to_atom(field.field_name), field.field_name}
      end)

    record =
      Licenses.get_license_mapping_by_license_id_and_user_id(
        params["license_id"],
        assigns.current_user.id
      )

    data =
      if is_nil(record),
        do: %{},
        else: record.data

    # Create initial changeset with the dynamic validator
    initial_changeset =
      DynamicFormValidator.validate_step(
        data,
        text_fields,
        step: 0
      )

    socket
    |> assign(data_loader: false)
    |> assign(min_date: Date.utc_today() |> Date.to_iso8601())
    |> assign(record: record)
    |> assign(steps: get_steps(params["license_id"]))
    |> assign(current_position: List.first(Registration.get_license_steps!(params["license_id"])))
    |> assign(chosen_licence: Licenses.get_license!(params["license_id"]))
    # Convert changeset to form here
    |> assign(:form, to_form(initial_changeset, as: "license_mapping"))
    |> assign(get_attention_field: App.LicenseReviews.get_attention_field(params["license_id"]))
    |> assign(license_id: params["license_id"])
    |> assign(license_fields: license_fields)
    |> assign(upload_fields1: upload_fields)
    |> assign(text_fields: text_fields)
    |> assign(input_fields: Keyword.keys(input_fields))
    |> noreply()
  end

  defp uploads(%{assigns: assigns} = socket, params) do
    license_fields = Licenses.get_license_data(params["license_id"])

    # Organize fields

    upload_fields =
      Enum.filter(license_fields, &(&1.field_type == "upload" && &1.field_name != "pop_upload"))

    record =
      Licenses.get_license_mapping_by_license_id_and_user_id(
        params["license_id"],
        assigns.current_user.id
      )

    data =
      if is_nil(record),
        do: %{},
        else: record.data

    upload_fields1 =
      upload_fields
      |> Enum.filter(fn field ->
        DynamicSchema.should_show_field?(field, data)
      end)

    available_upload =
      Enum.filter(upload_fields1, fn field ->
        !(record.data && Map.has_key?(record.data, field.field_name) &&
            Map.get(record.data, field.field_name) != nil)
      end)

    # Create upload changeset
    upload_changeset =
      DynamicFormValidator.validate_step(
        data,
        available_upload,
        step: 1
      )

    all_form_data =
      case record do
        nil -> %{}
        record -> record.data
      end

    # upload_fields
    # |> Enum.filter(fn field ->
    #   DynamicSchema.should_show_field?(field, data) &&
    #     !(record.data && Map.has_key?(record.data, field.field_name) &&
    #         Map.get(record.data, field.field_name) != nil)
    # end)
    # |> Enum.map(fn field ->
    #   {String.to_atom(field.field_name), ~w(.pdf .jpg .png .jpeg)}
    # end)

    uploads =
      upload_fields
      |> Enum.filter(fn field ->
        DynamicSchema.should_show_field?(field, data)
      end)
      |> Enum.map(fn field ->
        {String.to_atom(field.field_name), ~w(.pdf .jpg .png .jpeg)}
      end)

    socket
    |> assign(data_loader: false)
    |> assign(record: record)
    |> assign(steps: get_steps(params["license_id"]))
    |> assign(current_position: record.current_step)
    |> assign(chosen_licence: Licenses.get_license!(params["license_id"]))
    |> configure_uploads(uploads)
    |> assign(upload_fields: Keyword.keys(uploads))
    |> assign(all_form_data: all_form_data)
    |> assign(get_attention_field: App.LicenseReviews.get_attention_field(params["license_id"]))
    # Initialize as empty map instead of nil
    |> assign(:doc_details, %{})
    |> assign(license_id: params["license_id"])
    |> assign(license_fields: license_fields)
    |> assign(upload_fields1: upload_fields1)
    |> assign(:upload_form, to_form(upload_changeset, as: "upload"))
    |> assign(available_uploads: available_upload)
    |> noreply()
  end

  defp paymemts(%{assigns: assigns} = socket, params) do
    license_fields = Licenses.get_license_data(params["license_id"])

    # Organize fields

    upload_fields = Enum.filter(license_fields, &(&1.field_name == "pop_upload"))

    record =
      Licenses.get_license_mapping_by_license_id_and_user_id(
        params["license_id"],
        assigns.current_user.id
      )

    data =
      if is_nil(record),
        do: %{},
        else: record.data

    # Create upload changeset
    upload_changeset =
      DynamicFormValidator.validate_step(
        data,
        upload_fields,
        step: 1
      )

    all_form_data =
      case record do
        nil -> %{}
        record -> record.data || %{}
      end

    upload_fields1 =
      upload_fields
      |> Enum.filter(fn field ->
        DynamicSchema.should_show_field?(field, all_form_data)
      end)

    uploads =
      upload_fields
      |> Enum.filter(fn field ->
        DynamicSchema.should_show_field?(field, all_form_data) &&
          !(record.data && Map.has_key?(record.data, field.field_name) &&
              Map.get(record.data, field.field_name) != nil)
      end)
      |> Enum.map(fn field ->
        {String.to_atom(field.field_name), ~w(.pdf .jpg .png .jpeg)}
      end)

    associates = Licenses.get_associated_licenses!(record.id)

    associates_count = Licenses.count_associates_at_reg(record.id)
    chosen_licence = Licenses.get_license!(params["license_id"])

    total_assoc_amount =
      case List.first(associates) do
        nil -> Decimal.new(0)
        first_assoc -> Decimal.mult(associates_count, first_assoc.license.amount)
      end

    total_amount =
      Decimal.add(chosen_licence.amount, total_assoc_amount)
      |> Decimal.add(chosen_licence.other_fees || 0)

    socket
    |> assign(data_loader: false)
    |> assign(record: record)
    |> assign(steps: get_steps(params["license_id"]))
    |> assign(totals: %{total_amount: total_amount, total_assoc_amount: total_assoc_amount})
    |> assign(associates: associates)
    |> assign(current_position: record.current_step)
    |> assign(associates_count: associates_count)
    |> assign(chosen_licence: chosen_licence)
    |> configure_uploads(uploads)
    |> assign(upload_fields: Keyword.keys(uploads))
    |> assign(all_form_data: all_form_data)
    |> assign(get_attention_field: App.LicenseReviews.get_attention_field(params["license_id"]))
    # Initialize as empty map instead of nil
    |> assign(:doc_details, %{})
    |> assign(license_id: params["license_id"])
    |> assign(license_fields: license_fields)
    |> assign(upload_fields1: upload_fields1)
    |> assign(:upload_form, to_form(upload_changeset, as: "upload"))
    |> assign(
      available_uploads:
        Enum.filter(upload_fields1, fn field ->
          !(record.data && Map.has_key?(record.data, field.field_name) &&
              Map.get(record.data, field.field_name) != nil)
        end)
    )
    |> noreply()
  end

  defp summary(%{assigns: assigns} = socket, params) do
    license_fields = Licenses.get_license_data(params["license_id"])

    # Organize fields
    text_fields =
      Enum.filter(
        license_fields,
        &(&1.field_type in [
            "text",
            "date",
            "select",
            "number",
            "textarea",
            "checkbox",
            "radio",
            "checkbox_group"
          ])
      )
      |> Enum.sort_by(fn field ->
        case field.field_type do
          "text" -> 0
          "date" -> 1
          "textarea" -> 3
          _ -> 2
        end
      end)

    upload_fields = Enum.filter(license_fields, &(&1.field_type == "upload"))

    record =
      Licenses.get_license_mapping_by_license_id_and_user_id(
        params["license_id"],
        assigns.current_user.id
      )

    associates = Licenses.get_associated_licenses!(record.id)

    data =
      if is_nil(record),
        do: %{},
        else: record.data

    # Create initial changeset with the dynamic validator
    initial_changeset =
      DynamicFormValidator.validate_step(
        data,
        text_fields,
        step: 0
      )

    assign(socket, data: [])
    |> assign(data_loader: false)
    |> assign(chosen_licence: Licenses.get_license!(params["license_id"]))
    |> assign(record: record)
    |> assign(associates: associates)
    |> assign(steps: get_steps(params["license_id"]))
    |> assign(:form, to_form(initial_changeset, as: "license_mapping"))
    |> assign(params: params)
    |> assign(license_fields: license_fields)
    |> assign(upload_fields1: upload_fields)
    |> assign(current_position: record.current_step)
    |> noreply()
  end

  # Helper Functions Here -------------->>>

  # steps ---------------->

  defp get_steps(license_id) do
    Registration.get_reg_steps!(license_id)
  end

  # uploads ------>

  defp configure_uploads(socket, upload_types) do
    Enum.reduce(upload_types, socket, fn {field, _accepts}, acc ->
      allow_upload(acc, field,
        accept: ~w(.pdf .jpg .png .jpeg),
        max_entries: 1,
        max_file_size: 10_000_000
      )
    end)
  end

  def validate_doc(%{"_target" => [field]}, %{assigns: assigns} = socket) do
    upload_field = String.to_atom(field)

    attrs =
      assigns.upload_fields
      |> Enum.reduce(%{}, fn key, acc ->
        # check existing entries
        existing_entries =
          assigns.uploads[key]
          |> Map.get(:entries, [])
          |> Enum.map(& &1.client_name)
          |> Enum.at(0)

        # If the current key matches field validating, fetch the entries
        updated_entries =
          if key == upload_field do
            assigns.uploads[upload_field]
            |> Map.get(:entries, [])
            |> Enum.map(& &1.client_name)
            |> Enum.at(0)
          else
            existing_entries
          end

        Map.put(acc, key, updated_entries)
      end)

    # Use our dynamic validator for uploads
    changeset =
      DynamicFormValidator.validate_step(
        attrs,
        assigns.available_uploads,
        step: assigns.current_position
      )
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :upload_form, to_form(changeset, as: "upload"))}
  end

  def consume_upload(socket, upload_type, _attrs) do
    new_path = LiveFunctions.static_path("/uploads/licence_registration")

    consume_uploaded_entries(socket, upload_type, fn %{path: path}, entry ->
      file_name = "/#{Path.rootname(entry.client_name)}.#{LiveFunctions.ext(entry)}"
      dest = Path.join(new_path, file_name)

      if File.exists?(new_path <> "/") do
        File.cp(path, dest)
        {:ok, dest}
      else
        File.mkdir_p(new_path <> "/")
        File.cp_r(path, dest)
        {:ok, dest}
      end
    end)
  end

  def document_uploads(attrs, socket) do
    # Process each upload type and return the updated attributes
    updated_attrs =
      socket.assigns.upload_fields
      |> Enum.reduce(attrs, fn upload_type, acc ->
        acc |> Map.put("#{upload_type}", handle_upload(socket, upload_type, attrs))
      end)

    changeset =
      DynamicFormValidator.validate_step(
        updated_attrs,
        socket.assigns.available_uploads,
        step: socket.assigns.current_position
      )
      |> Map.put(:action, :validate)

    if changeset.valid? do
      Licenses.create_registration(
        socket,
        socket.assigns.record,
        Map.merge(
          updated_attrs,
          socket.assigns.all_form_data
          |> Jason.encode!()
          |> Jason.decode!()
        )
      )
    else
      # If validation fails, return the form with errors
      socket
      |> assign(:form, %{source: Map.put(changeset, :action, :validate)})
    end
  end

  defp handle_upload(socket, upload_type, attrs) do
    current_attachment = Map.get(socket.assigns, upload_type)

    if current_attachment && length(socket.assigns.uploads[upload_type].entries) > 0 do
      File.rm!(current_attachment)
      consume_upload(socket, upload_type, attrs)
    else
      # if length(socket.assigns.uploads[upload_type].entries) > 0 do
      List.first(consume_upload(socket, upload_type, attrs))
      # else
      #   List.first(current_attachment)
      # end
    end
  end
end
