defmodule App.Service.Table.Report.IssuedLicense do
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false
  alias App.Accounts.User
  alias App.Licensing.IssuedLicense
  alias App.Licenses.UserNotifications
  alias Logs.Audit
  alias App.Licenses.LicenseMapping
  alias App.Licensing
  alias App.Repo

  @pagination [page_size: 10]
  # nfs_participates

  def index(params) do
    compose_query(params)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def export(params) do
    compose_query(params)
    |> Repo.all()
  end

  def compose_query(params) do
    IssuedLicense
    |> join(:left, [a], b in assoc(a, :holder))
    |> join(:left, [a], c in assoc(a, :certificate))
    # |> join(:left, [a], d in assoc(a, :license))
    |> join(:left, [a], d in assoc(a, :application))
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"status", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.status, ^sanitize_term(value)))

      {"name", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.name, ^sanitize_term(value))
        )

      {"form_number", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.form_number, ^sanitize_term(value))
        )

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a, b, c, d], %{
      id: a.id,
      status: a.status,
      revoke: a.status == 1,
      email: b.email,
      full_name: fragment("CONCAT(?, ' ', ?)", b.first_name, b.last_name),
      template: c.template,
      license_name: d.record_name,
      aproved: d.approved,
      expiring_date: a.expiring_date,
      inserted_at: a.inserted_at
    })
  end
  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b,_, d],
      fragment("lower(?) LIKE lower(?)", type(d.record_name, :string), ^search_term) or
      fragment("lower(?) LIKE lower(?)", type(b.first_name, :string), ^search_term) or
      fragment("lower(?) LIKE lower(?)", type(b.email, :string), ^search_term) or
      fragment("lower(?) LIKE lower(?)", type(b.last_name, :string), ^search_term)


    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
