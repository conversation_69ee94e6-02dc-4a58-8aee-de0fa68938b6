defmodule App.Licenses.LicenseData do
  use Ecto.Schema
  import Ecto.Changeset

  schema "license_data" do
    field :status, :integer, default: 1
    belongs_to :license_field, App.Licenses.LicenseFields
    belongs_to :license, App.Licenses.License

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(license_data, attrs) do
    license_data
    |> cast(attrs, [:status, :license_field_id, :license_id])
    |> validate_required([:license_field_id, :license_id])
  end
end
