<div class="px-4 sm:px-6 lg:px-8 mt-5">
  <!-- Header  -->
  <div class="p-6 text-center rounded-md">
    <h1 class="text-3xl font-bold tracking-tight text-gray-900">
      Devices
    </h1>

    <p class="mt-2 text-gray-600">View and remove all logged in devices for this account</p>
  </div>

  <div class="mt-8 flow-root">
    <div class="-mx-4 -my-2 sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <Table.main_table
          id="adverts"
          rows={@data}
          extraction={false}
          params={@params}
          data_loader={@data_loader}
        >
          <:col :let={rj} label={table_link(@params, "Email", :user)}>
            <%= rj.user %>
          </:col>
          <:col :let={rj} label={table_link(@params, "Device", :name)}>
            <%= rj.name %>
          </:col>
          <:col :let={rj} label={table_link(@params, "Platform", :inst_code)}>
            <div class="has-tooltip">
              <span class="tooltip rounded shadow-lg p-1 bg-gray-100 text-brand-10 -mt-8">
                <%= rj.platform %>
              </span>
              <%= if rj.platform == "desktop" do %>
                <img src={~p"/images/svg/desktop.svg"} class="size-6" />
              <% end %>
              <%= if rj.platform in ["mobile", "android"] do %>
                <img src={~p"/images/svg/mobile.svg"} class="size-6" />
              <% end %>
              <%= if rj.platform == "tablet" do %>
                <img src={~p"/images/svg/tablet.svg"} class="size-6" />
              <% end %>
              <%= if rj.platform == "console" do %>
                <img src={~p"/images/svg/console.svg"} class="size-6" />
              <% end %>
              <%= if rj.platform == "unknown" do %>
                <img src={~p"/images/svg/unknown.svg"} class="size-6" />
              <% end %>
            </div>
          </:col>
          <:col
            :let={rj}
            label={table_link(@params, "Operating System", :type)}
            class="items-center justify-center"
          >
            <div class="has-tooltip">
              <span class="tooltip rounded shadow-lg p-1 bg-gray-100 text-brand-10 -mt-8">
                <%= rj.operating_system %>
              </span>
              <%= if check_compare(rj.operating_system, "iOS") do %>
                <img src={~p"/images/svg/ios.svg"} class="size-6" />
              <% end %>
              <%= if check_compare(rj.operating_system, "Android") do %>
                <img src={~p"/images/svg/android-logo.svg"} class="size-6" />
              <% end %>
              <%= if check_compare(rj.operating_system, "Linux") do %>
                <img src={~p"/images/svg/linux.svg"} class="size-6" />
              <% end %>
              <%= if check_compare(rj.operating_system, "MacOS") do %>
                <img src={~p"/images/svg/macos.svg"} class="size-6" />
              <% end %>
              <%= if check_compare(rj.operating_system, "Windows") do %>
                <img src={~p"/images/svg/windows.svg"} class="size-6" />
              <% end %>
              <%= if check_compare(rj.operating_system, "ChromeOS") do %>
                <img src={~p"/images/svg/chrome.svg"} class="size-6" />
              <% end %>
              <%= if check_compare(rj.operating_system, "BlackBerry") do %>
                <img src={~p"/images/svg/blackberry.svg"} class="size-6" />
              <% end %>
              <%= if check_compare(rj.operating_system, "Other") do %>
                <img src={~p"/images/svg/unknown.svg"} class="size-6" />
              <% end %>
            </div>
          </:col>
          <:col :let={james} label={table_link(@params, "Last Activity", :last_activity)}>
            <%= Calendar.strftime(
              NaiveDateTime.add(james.last_activity, 7200, :second),
              "%d %B %Y %H:%M:%S"
            ) %>
          </:col>
          <:col :let={t} label_class="item-center">
            <div class="flex items-center gap-2 justify-center">
              <.link
                phx-click="update_status"
                phx-value-status="0"
                phx-value-id={t.id}
                phx-value-name={t.name}
                title="Remove"
                class="border border-red-500 hover:bg-brand-10 hover:border-brand-10 rounded-full p-3"
              >
                <img src={~p"/images/svg/trash.svg"} class="w-4 h-4" />
              </.link>
            </div>
          </:col>
        </Table.main_table>
      </div>
    </div>
  </div>
</div>

<Model.small :if={@live_action in [:filter]} id="transaction-modal" show return_to="close_model">
  <.live_component
    module={AppWeb.Component.DateFilter}
    id={:filter}
    title={@page_title}
    @click.outside="open = false"
    params={@params}
  />
</Model.small>

<Model.small
  :if={@live_action in [:new, :edit]}
  id="transaction-modal"
  show
  return_to="close_model"
>
  <.live_component
    module={AppWeb.DeviceLive.FormComponent}
    id={@nfs.id || :new}
    title={@page_title}
    action={@live_action}
    nfs={@nfs}
    browser_info={@browser_info}
    current_user={@current_user}
  />
</Model.small>

<Model.confirmation_model
  :if={@confirmation_model}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
