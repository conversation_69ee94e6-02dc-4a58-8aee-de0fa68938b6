defmodule AppWeb.Auth.Schedule.SingleLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias Notification.{Sms, Notification.SmsLogs}
  alias App.{Settings, Notifications.ServerCalls}

  def mount(params, session, socket) do
    changeset = Sms.change_sms_logs(%SmsLogs{})

    {
      :ok,
      socket
      |> assign_form(changeset)
      |> assign(:chose_sender, Settings.get_senders_by_client_id!(socket.assigns.client.id))
      |> LivePageControl.order_by_composer()
      |> LivePageControl.i_search_composer()
      |> assign(params: params)
      |> assign(min_date: Date.utc_today() |> Date.to_iso8601())
      |> assign(loader: false)
      |> assign(confirmation_model: false)
      |> assign(confirmation_model_title: "Are you sure?")
      |> assign(confirmation_model_text: "")
      |> assign(confirmation_model_agree: "")
      |> assign(confirmation_model_reject: "close_confirmation_model")
      |> assign(confirmation_model_params: "")
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> assign(live_socket_id: session["live_socket_id"])
    }
  end

  def handle_event("validate", %{"sms_logs" => sms_logs_params}, socket) do
    changeset =
      Sms.change_sms_logs(%SmsLogs{}, sms_logs_params)
      |> Map.put(:action, :validate)

    {:noreply, assign_form(socket, changeset)}
  end

  def handle_event("save", %{"sms_logs" => sms_logs_params}, socket) do
    value =
      CustomFunctions.get_value_from_select_data2(
        sms_logs_params,
        socket.assigns.chose_sender,
        "sender_id"
      )["sender"]

    attrs =
      Map.merge(sms_logs_params, %{
        "sender" => value,
        "client_id" => socket.assigns.client.id
      })

    changeset = Sms.send_change_sms_logs(%SmsLogs{}, attrs)

    if changeset.valid? do
      send(self(), {:send, attrs})

      LiveFunctions.sweet_alert(socket, "Processing message schedule", "info")
      |> assign_form(Map.put(changeset, :action, :validate))
      |> assign(loader: true)
      |> noreply()
    else
      noreply(LiveFunctions.sweet_alert(socket, "Make sure all fields are filled", "error"))
    end
  end

  def handle_info({:send, attrs}, socket) do
    ServerCalls.api_server(
      "scheduled_single",
      Map.put(attrs, "user_id", socket.assigns.current_user.id)
    )
    |> case do
      {:ok, body} ->
        if body["status"] do
          changeset = Sms.send_change_sms_logs(%SmsLogs{}, %{})

          LiveFunctions.sweet_alert(socket, body["message"], "success")
          |> assign(loader: false)
          |> assign_form(Map.put(changeset, :action, :validate))
          |> noreply()
        else
          LiveFunctions.sweet_alert(socket, body["message"], "error")
          |> assign(loader: false)
          |> noreply()
        end

      {:error, message} ->
        LiveFunctions.sweet_alert(socket, message, "error")
        |> assign(loader: false)
        |> noreply()
    end
  end

  def assigns_form(socket, %Ecto.Changeset{} = changeset) do
    assign(socket, :form, to_form(changeset))
  end
end
