defmodule App.Service.Logs.LogsSession do
  @moduledoc false
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false

  alias Logs.Audit.Session
  alias Logs.LogRepo, as: Repo

  alias App.{Accounts}

  @pagination [page_size: 10]

  def index(params) do
    compose_query(params)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
    |> (fn %{entries: entries} = result ->
          Map.put(
            result,
            :entries,
            Enum.map(
              entries,
              &Map.merge(&1, %{
                user_id: Accounts.get_username_as_email_by_id(&1.user_id),
                type: ""
              })
            )
          )
        end).()
  end

  def export(params) do
    compose_query(params)
    |> Repo.all()
    |> Enum.map(&Map.merge(&1, %{user_id: Accounts.get_username_as_email_by_id(&1.user_id)}))
  end

  defp compose_query(params) do
    Session
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(String.trim(value)))

      {"user_id", value}, query when byte_size(value) > 0 ->
        where(query, [a, b], fragment("lower(?) LIKE lower(?)", b.user_id, ^value))

      {"portal", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.portal, ^value))

      {"status", value}, query when byte_size(value) > 0 ->
        where(query, [a], a.status == ^value)

      {"start_date", value}, query when byte_size(value) > 0 ->
        naive_datetime = NaiveDateTime.from_iso8601!("#{value} 00:00:00")
        where(query, [s, c, d], s.inserted_at >= ^naive_datetime)

      {"end_date", value}, query when byte_size(value) > 0 ->
        naive_datetime = NaiveDateTime.from_iso8601!("#{value} 23:59:59")
        where(query, [s, c, d], s.inserted_at <= ^naive_datetime)

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  def compose_select(query) do
    select(query, [a, b], %{
      user_id: a.user_id,
      inserted_at: a.inserted_at,
      log_out_at:
        fragment("TO_CHAR (? + interval '2 hours', 'DD MON YYYY, HH24:MI:SS')", a.log_out_at),
      log_in_at: a.log_in_at,
      portal: a.portal,
      session_id: a.session_id,
      status: a.status,
      description: a.description,
      id: a.id
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      fragment("lower(?) LIKE lower(?)", a.portal, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.session_id, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
