<%= if @data_loader == false do %>
  <.live_component
    module={AppWeb.Registration.NavigationComponent}
    id="navigation"
    steps={@steps}
    current_position={@current_position}
  />
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 bg-white rounded-lg shadow-md mt-8">
    <div class="mb-8 border-b pb-5">
      <h1 class="text-2xl font-bold text-gray-900"><%= @chosen_licence.name %> Registration</h1>

      <p class="mt-2 text-sm text-gray-600">
        Please review your information and documents before submission.
      </p>
    </div>
    <%!-- Summary of information --%>
    <div class="bg-gray-50 p-6 rounded-lg border border-gray-200 mb-8">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Summary of Information</h2>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <%= for field <- Enum.filter(@license_fields, &(&1.field_type != "upload")) do %>
          <%= if App.Licenses.DynamicSchema.should_show_field?(field, @form.source.params || %{}) do %>
            <div class="py-2 border-b border-gray-100">
              <div class="text-sm font-medium text-gray-500">
                <%= field.field_label %>
              </div>

              <div class="text-md text-gray-900">
                <%= case field.field_type do %>
                  <% "checkbox" -> %>
                    <%= if Phoenix.HTML.Form.input_value(
                             @form,
                             String.to_atom(field.field_name)
                           ),
                           do: "Yes",
                           else: "No" %>
                  <% "checkbox_group" -> %>
                    <%= values =
                      Phoenix.HTML.Form.input_value(@form, String.to_atom(field.field_name)) ||
                        []

                    Enum.join(values, ", ") %>
                  <% _ -> %>
                    <%= Phoenix.HTML.Form.input_value(@form, String.to_atom(field.field_name)) ||
                      "Not provided" %>
                <% end %>
              </div>
            </div>
          <% end %>
        <% end %>
      </div>

      <h3 class="text-md font-medium text-gray-900 mt-6 mb-2">Uploaded Documents</h3>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <%= for field <- Enum.filter(@license_fields, &(&1.field_type == "upload")) do %>
          <%= if is_nil(field.field_dependents) ||
          field.dependent_selection ==
            (if !Enum.empty?(field.field_dependents),
              do: @form.source.changes[String.to_atom(List.first(field.field_dependents))]) do %>
            <div class="py-2 border-b border-gray-100">
              <div class="text-sm font-medium text-gray-500">
                <%= field.field_label %>
              </div>

              <div class="text-md text-gray-900">
                <%= if @record.data &&
                  Map.has_key?(@record.data, field.field_name) &&
                  Map.get(@record.data, field.field_name) != nil do %>
                  <div class="flex items-start">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-green-500 mr-2 mt-0.5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      />
                    </svg>

                    <div>
                      <p class="text-sm font-medium text-gray-600 truncate">
                        : <%= String.slice(Path.basename(@record.data[field.field_name]), 0, 40) <>
                          if String.length(Path.basename(@record.data[field.field_name])) > 20,
                            do: "...",
                            else: "" %>
                      </p>
                    </div>
                  </div>
                <% else %>
                  <span class="text-orange-500">Not uploaded</span>
                <% end %>
              </div>
            </div>
          <% end %>
        <% end %>
      </div>

      <%= if @associates != [] do %>
        <h3 class="text-md font-medium text-gray-900 mt-6 mb-2">
          Representatives: (<%= NumberF.comma_separated(length(@associates), 0) %>)
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <%= for representative <- @associates do %>
            <div class="py-2 border-b border-gray-100">
              <div class="text-sm font-medium text-gray-500">
                Name
              </div>

              <div class="text-md text-gray-900">
                <%= representative.record_name %>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>

      <div class="mt-6 text-md text-gray-500">
        Please review the information above before submitting.
      </div>

      <div class="flex justify-end pt-6 border-t">
        <.button type="button" phx-click="back">
          <b><i class="hero-chevron-double-left position-left"></i></b> back
        </.button>

        <.button
          type="button"
          phx-disable-with="Submitting..."
          phx-click="submit"
          class="ml-3 inline-flex items-center px-6 py-2 text-sm font-medium text-white bg-brand-10 border border-transparent rounded-md shadow-sm hover:bg-brand-11 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-2"
        >
          <.icon name="hero-check-circle" class="h-5 w-5 mr-2" /> Submit
        </.button>
      </div>
    </div>
  </div>
<% end %>
