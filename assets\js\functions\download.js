export async function download_your_files(payload, fileName, extension) {
    let iframe = null;

    try {
        // Create and configure iframe
        iframe = document.createElement('iframe');
        iframe.id = 'download-frame';
        iframe.style.display = 'none';
        document.body.appendChild(iframe);

        // Define MIME types based on file extension
        const mimeTypes = {
            '.pdf': 'application/pdf',
            '.csv': 'text/csv',
            '.xls': 'application/vnd.ms-excel',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        };

        const mimeType = mimeTypes[extension] || 'application/octet-stream';
        const currentTime = new Date().toTimeString().slice(0, 8);
        const dataUrl = `data:${mimeType};base64,${encodeURIComponent(payload)}`;

        // Create download link inside the iframe
        const iframeDoc = iframe.contentWindow.document;
        const downloadLink = iframeDoc.createElement('a');
        downloadLink.href = dataUrl;
        downloadLink.download = `${fileName} ${currentTime}${extension}`;

        // Trigger download
        iframeDoc.body.appendChild(downloadLink);
        downloadLink.click();

        // Cleanup after short delay to ensure download starts
        setTimeout(() => cleanup(iframe), 1000);
    } catch (error) {
        console.error('Download failed:', error);
        cleanup(iframe);
    }
}

function cleanup(iframe) {
    if (iframe && iframe.parentNode) {
        iframe.parentNode.removeChild(iframe);
    }
}
