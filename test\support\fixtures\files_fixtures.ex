defmodule App.FilesFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `App.Files` context.
  """

  @doc """
  Generate a uploaded_file.
  """
  def uploaded_file_fixture(attrs \\ %{}) do
    {:ok, uploaded_file} =
      attrs
      |> Enum.into(%{
        approver_id: 42,
        content_type: "some content_type",
        filename: "some filename",
        path: "some path"
      })
      |> App.Files.create_uploaded_file()

    uploaded_file
  end
end
