defmodule App.Service.ServiceConditions.Functions do
  alias App.LicenseConditions

  def index(socket, attrs, function \\ "change_status") do
    record = LicenseConditions.get_condition!(attrs["id"])

    cond do
      function == "change_status" ->
        LicenseConditions.change_condition_status(socket, attrs, record)
    end
  end

  def create(socket, attrs) do
    LicenseConditions.create_condition(socket, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end

  def update(socket, record, attrs) do
    LicenseConditions.update_condition(socket, record, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end
end
