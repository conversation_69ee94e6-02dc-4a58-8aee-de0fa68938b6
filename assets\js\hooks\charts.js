import ApexCharts from 'apexcharts'

// Common chart options
const commonChartOptions = {
    chart: {
        height: 350
    },
    dataLabels: {
        enabled: true,
        dropShadow: {
            enabled: true,
            left: 2,
            top: 2,
            opacity: 0.5
        }
    },
    noData: {
        text: 'Loading',
        align: 'center',
        verticalAlign: 'middle',
        style: {
            fontSize: '14px'
        }
    }
};

export const BarChart = {
    mounted() {
        const options = {
            ...commonChartOptions,
            series: [
                {
                    name: 'Delivered',
                    data: [12, 19, 30, 50, 20, 30, 40],
                    type: 'bar',
                },
                {
                    name: 'Failed',
                    data: [3, 0, 0, 0, 6, 0, 8],
                    type: 'column',
                },
                {
                    name: 'Sent',
                    data: [5, 0, 4, 14, 16, 18, 40],
                    type: 'line',
                },
                {
                    name: 'Invalid',
                    data: [2, 1, 2, 0, 0, 0, 0],
                    type: 'line',
                }
            ],
            xaxis: {
                categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
                type: 'datetime',
                tickPlacement: 'on'
            },
            colors: ['#22c55e', '#ef4444', '#3b82f6', '#a855f7'],
            yaxis: {
                title: {
                    text: 'Points',
                }
            },
            markers: {
                size: 0
            },
            legend: {
                position: 'top',
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '55%',
                    endingShape: 'rounded'
                },
            },
            grid: {
                padding: {
                    left: 30, // or whatever value that works
                    right: 30 // or whatever value that works
                }
            },
            stroke: {
                width: [0, 2, 5],
                curve: 'smooth'
            },
            fill: {
                opacity: 1
            },
            tooltip: {
                shared: true,
                intersect: false,
                y: {
                    formatter: function (y) {
                        if (typeof y !== "undefined") {
                            return y.toFixed(0) + " points";
                        }
                        return y;

                    }
                }
            }
        };

        let chart = new ApexCharts(this.el, options);
        chart.render();

        // Improved error handling for updates
        this.handleEvent("bar_chart_update", ({series}) => {
            try {
                const { results } = series;
                if (!results || !results.datasets) {
                    console.error('Invalid chart data received');
                    return;
                }

                chart.updateOptions({
                    series: results.datasets,
                    colors: results.colors,
                    xaxis: {
                        categories: results.categories,
                        tickPlacement: 'on'
                    },
                    yaxis: {
                        title: { text: results.text }
                    }
                });
            } catch (error) {
                console.error('Error updating bar chart:', error);
            }
        });

        // Clean up on disconnect
        this.handleEvent("disconnected", () => {
            chart.destroy();
        });
    },

    destroyed() {
        if (this.chart) {
            this.chart.destroy();
            this.chart = null;
        }
    }
}

export const PieChart = {
    mounted() {
        const options = {
            ...commonChartOptions,
            series: ['9', '6', '3'],
            chart: {
                type: 'donut',
                height: 360
            },
            labels: ['Airtel', 'MTN', 'Zamtel'],
            plotOptions: {
                pie: {
                    dataLabels: {
                        offset: 0,
                        minAngleToShowLabel: 10
                    },
                    donut: {
                        labels: {
                            show: true,
                            total: {
                                show: true,
                                fontWeight: 600,
                            }
                        }
                    }
                }
            },
            dataLabels: {
                ...commonChartOptions.dataLabels,
                formatter: (val) => `${parseFloat(val).toFixed(2)}%`
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: { width: 200 },
                    legend: { position: 'bottom' }
                }
            }]
        };

        let chart = new ApexCharts(this.el, options);
        chart.render();

        // Improved error handling for updates
        this.handleEvent("pie_chart_update", ({series}) => {
            try {
                const { results } = series;
                if (!results) {
                    console.error('Invalid pie chart data received');
                    return;
                }

                chart.updateOptions({
                    chart: {
                        type: results.chart_type,
                        zoom: { enabled: true }
                    },
                    series: results.datasets,
                    labels: results.labels,
                    colors: results.colors,
                });
            } catch (error) {
                console.error('Error updating pie chart:', error);
            }
        });

        // Clean up on disconnect
        this.handleEvent("disconnected", () => {
            chart.destroy();
        });
    },

    destroyed() {
        if (this.chart) {
            this.chart.destroy();
            this.chart = null;
        }
    }
}