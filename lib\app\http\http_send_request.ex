defmodule App.Http.Send do
  @moduledoc false

  @options [
    timeout: 2_000_000,
    recv_timeout: 2_000_000,
    hackney: [:insecure]
  ]

  def get_request(url, attrs \\ %{}, headers \\ [{"Content-Type", "application/json"}]),
    do:
      if(Enum.empty?(attrs),
        do: HTTPoison.get(url, headers, @options),
        else:
          HTTPoison.get(
            "#{url}?#{URI.encode_query(attrs) |> String.replace("%2F", "/") |> String.replace("%25", "%")}",
            headers,
            @options
          )
      )

  def post_json_request(url, body \\ %{}, headers \\ []),
    do:
      HTTPoison.post(
        url,
        Jason.encode!(body),
        Enum.concat([{"Content-Type", "application/json"}], headers),
        @options
      )

  def post_urlencoded_request(
        url,
        body \\ %{},
        headers \\ [{"Content-Type", "x-www-form-urlencoded"}]
      ),
      do: HTTPoison.post(url, URI.encode_query(body), headers, @options)

  def post_any_request(url, body, headers \\ [], options \\ @options),
    do: HTTPoison.post(url, body, headers, options)
end
