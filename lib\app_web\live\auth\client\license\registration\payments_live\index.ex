defmodule AppWeb.Auth.Registration.PaymentsLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias App.{Companies, Licenses}

  alias AppWeb.Auth.RegistrationLive.Entry

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("client_applications-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Payments", "Accessed Payments Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> assign(params: params)
        |> assign(:search_form, to_form(%{"nrc" => ""}))
        |> assign(:search_results, [])
        |> assign(:searching, false)
        |> assign(:searched, false)
        |> assign(:search_query, "")
        |> assign(confirmation_model: false)
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(session_id: session["live_socket_id"])
        |> assign(live_socket_id: session["live_socket_id"])

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Payments", "Accessed Payments Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_data, params})

    {
      :noreply,
      socket
      |> assign(:params, params)
      |> apply_action(socket.assigns.live_action, params)
    }
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      {:get_data, params} ->
        get_update(socket, params)

      {:search, nrc} ->
        socket =
          socket
          |> assign(:search_results, Companies.search_associate!(nrc))
          |> assign(:searching, false)
          |> assign(:searched, true)

        {:noreply, socket}

      {AppWeb.LiveFunctions, message} ->
        {
          :noreply,
          socket
          |> put_flash(:info, message)
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, %{assigns: assigns} = socket) do
    case target do
      "close_model" ->
        {:noreply, assign(socket, :live_action, :index)}

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :live_action, :index)
        }

      "attach" ->
        {
          :noreply,
          assign(socket, :live_action, :confirm)
          |> assign(
            :confirmation_model_text,
            "Are you sure you want to attach this representative?"
          )
          |> assign(:confirmation_model_params, Map.merge(value, %{"action" => "cancel"}))
          |> assign(:confirmation_model_agree, "process_attach")
        }

      "process_attach" ->
        attach_application(socket)

      "remove_file" ->
        remove_file(socket, value)

      "search" ->
        search(value, socket)

      "skip" ->
        socket
        |> next_position(assigns.record)
        |> noreply()

      "validate_doc" ->
        Entry.validate_doc(value, socket)

      "doc_save" ->
        Entry.document_uploads(value["license_mapping"] || %{}, socket)
        |> case do
          {:ok, %{"license_registration" => record}} ->
            socket
            |> next_position(record)

          {:error, error} ->
            LiveFunctions.sweet_alert(
              socket,
              "Error processing registration: #{inspect(error)}",
              "error"
            )
        end
        |> noreply()

      "back" ->
        {:noreply, pre_position(socket)}

      _ ->
        {:noreply, socket}
    end
  end

  defp search(%{"nrc" => nrc}, socket) do
    if String.trim(nrc) != "" do
      send(self(), {:search, String.trim(nrc)})

      socket =
        socket
        |> assign(:searching, true)
        |> assign(:searched, false)
        |> assign(:search_query, String.trim(nrc))
        |> assign(:search_results, [])

      {:noreply, socket}
    else
      {:noreply, assign(socket, :search_results, [])}
    end
  end

  def attach_application(socket) do
    new_params = %{
      "main_id" => Companies.get_main_application_id!(socket.assigns.current_user.id),
      "company_id" => Companies.get_company_id_by_user!(socket.assigns.current_user.id)
    }

    Companies.attach_application(
      socket,
      new_params,
      List.first(socket.assigns.search_results)
    )
    |> case do
      {:ok, _message} ->
        success_message(socket, "Successfully Attached.")

      {:error, message} ->
        error_message(socket, message)
    end
  end

  defp remove_file(socket, attrs) do
    Licenses.update_data_key(
      socket,
      Map.drop(socket.assigns.record.data || %{}, [attrs["field"]])
    )
    |> case do
      {:ok, record} ->
        send(self(), {:get_data, socket.assigns.params})

        {
          :noreply,
          socket
          |> assign(:record, record)
        }

      {:error, error} ->
        LiveFunctions.sweet_alert(
          socket,
          "Error removing file: #{inspect(error)}",
          "error"
        )

        {:noreply, socket}
    end
  end

  defp get_update(socket, params) do
    Entry.init(socket, params, "payments")
  end

  defp success_message(%{assigns: _assigns} = socket, message) do
    send(self(), {:get_data, socket.assigns.params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(:search_form, to_form(%{"nrc" => nil}))
      |> assign(:search_results, [])
      |> assign(:searching, false)
      |> assign(:searched, false)
      |> assign(:search_query, "")
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "success"
      )
    }
  end

  defp error_message(%{assigns: _assigns} = socket, message) do
    send(self(), {:get_data, socket.assigns.params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "error"
      )
    }
  end

  defp next_position(socket, record) do
    get_next_page =
      socket.assigns.steps
      |> Enum.find(fn step -> step.number > socket.assigns.current_position end)

    push_navigate(socket, to: "#{get_next_page.step.url}#{record.license_id}")
  end

  defp pre_position(socket) do
    get_prev_page =
      socket.assigns.steps
      |> Enum.filter(fn step -> step.number < socket.assigns.current_position end)
      |> Enum.max_by(& &1.number, fn -> nil end)

    Licenses.update_current_step(socket.assigns.record, get_prev_page.number)
    |> case do
      {:ok, record} ->
        push_navigate(socket, to: "#{get_prev_page.step.url}#{record.license_id}")

      {:error, _error} ->
        :error
    end
  end

  def error_to_string(:too_large), do: "Too large"
  def error_to_string(:not_accepted), do: "You have selected an unacceptable file type"
  def error_to_string(:too_many_files), do: "You have selected too many files"
end
