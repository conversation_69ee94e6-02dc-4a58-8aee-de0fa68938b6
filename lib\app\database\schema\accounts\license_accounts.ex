defmodule App.Schema.LicenseAccounts do
  use Ecto.Schema
  import Ecto.Changeset

  schema "accounts" do
    field :license_amount, :decimal
    field :other_fees, :decimal
    field :amount_paid, :decimal, default: 0
    field :balance, :decimal, default: 0
    field :new_balance, :decimal, virtual: true, redact: true
    field :verified, :boolean, default: false
    field :declined, :boolean, default: true

    belongs_to :user, App.Accounts.User
    belongs_to :application, App.Licenses.LicenseMapping

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(user, attrs) do
    user
    |> cast(attrs, [
      :license_amount,
      :amount_paid,
      :verified,
      :declined,
      :user_id,
      :application_id,
      :other_fees
    ])
    |> validate_required([:license_amount, :user_id, :application_id])
  end

  def debit_account_changeset(account, attrs) do
    account
    |> cast(attrs, [:new_balance])
    |> debit_account(account.license_amount)
  end

  defp debit_account(changeset, license_amount) do
    new_balance = get_change(changeset, :new_balance)

    if new_balance do
      sub = Decimal.sub(license_amount, new_balance)

      put_change(changeset, :balance, sub)
    else
      changeset
    end
  end
end
