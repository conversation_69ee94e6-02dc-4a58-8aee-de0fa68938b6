<!DOCTYPE html>
<html lang="en" class="h-full bg-gray-100">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content={get_csrf_token()} />
    <.live_title suffix=" · SUPTECH">
      <%= assigns[:page_title] || "PBS" %>
    </.live_title>
    <link phx-track-static rel="stylesheet" href={~p"/assets/app.css"} />
    <script defer phx-track-static type="text/javascript" src={~p"/assets/app.js"}>
    </script>
    <link rel="icon" href={~p"/images/logo-32x32.jpg"} sizes="32x32" />
    <link rel="icon" href={~p"/images/logo-192x192.jpg"} sizes="192x192" />
    <link rel="apple-touch-icon" href={~p"/images/logo-180x180.jpg"} />
    <meta name="msapplication-TileImage" content={~p"/images/logo-270x270.jpg"} />
    <script>
      sessionStorage.userToken = "<%= @user_token %>";
    </script>

    <script>
      sessionStorage.userSessionId = "<%= @live_socket_identifier %>";
    </script>
  </head>

  <body class="h-full">
    <div class="min-h-full bg-white">
      <div
        id="sidebar"
        class="z-50 border-r md:border-r-0 side-bar -translate-x-full transition-transform duration-500 ease-in-out fixed inset-y-0 flex w-64 border--r shadow-xl- flex-col"
        style="background-color: #4464AD; color: white;"
      >
        <div class="sticky top-0 pt-4 pb-2 px-2 z-10" style="background-color: #4464AD;">
          <div class="flex flex-shrink-0 items-center">
            <img class="w-full" src="/images/pbs-logo.png" alt="logo" />
          </div>

          <button
            id="x-close"
            phx-hook="sideBarToggle"
            id="jjhjjhjhj"
            class=" -translate-x-full transition-transform duration-500 ease-in-out absolute p-2 rounded-full border-r md:hidden hover:text-white top-4 -right-5 z-10"
            style="background-color:#4464AD;"
          >
            <.icon name="hero-x-mark" />
          </button>
        </div>
        <hr class="mx-auto h-1 border-none my-2 bg-white/30 rounded-full w-1/2" />
        <div class="pl-4 pr-3 mt-2 menu dark_scrollbar hidden_scrollbar_container overflow-y-scroll text-white">
          <%= live_render(@conn, Nav, sticky: true) %>
        </div>

        <div class="text-[0.6rem] text-gray-200 w-full text-center p-5 sticky bottom-0"></div>
      </div>

      <div id="main-body" class="flex flex-col transition-all duration-500 ease-in-out">
        <div
          class="flex flex-shrink-0 justify-between h-16 border-b border-gray-200 z-20 sticky top-0"
          style="background-color: #f2f2f2;"
        >
          <div class="flex items-center">
            <!-- OPEN SIDE BAR -->
            <button
              phx-hook="sideBarToggle"
              type="button"
              id="uyyfhfgdrsrfgfghgfhjgfdcvb"
              class="px-4 h-full text-gray-700 hover:text-gray-900 hover:bg-gray-100"
            >
              <span class="sr-only">Open sidebar</span> <.icon name="hero-bars-3" />
            </button>
            <span class="h-[60%] mr-4 w-px bg-gray-300 rounded-full"></span>
            <img id="sub-logo" class="h-8" src="/images/pbs-logo.png" alt="logo" />
          </div>
          <!-- Search bar -->
          <span></span>
          <div class="ml-4 flex items-center md:ml-6 sticky top-0 px-6">
            <!-- Notifications dropdown -->
            <%= live_render(@conn, AppWeb.Components.Notifications, id: "Notifications") %>
            <!-- Profile dropdown -->
            <div x-data="{ open: false }" class="relative">
              <button
                @click="open = ! open"
                type="button"
                class="-m-1.5 flex items-center p-1.5"
                id="user-menu-button"
                aria-expanded="false"
                aria-haspopup="true"
              >
                <span class="sr-only">Open user menu</span>
                <span class="absolute -inset-1.5 lg:hidden"></span>
                <img class="h-8 w-8 rounded-full" src={~p"/images/avatar/9.png"} alt="" />
                <div class="ml-3 text-sm flex flex-col items-start text-gray-700 lg:block">
                  <p class=" font-medium"><%= @current_user.first_name %> (<%= @role_name %>)</p>
                </div>

                <svg
                  class="ml-1 hidden h-5 w-5 flex-shrink-0 text-gray-700 lg:block"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>

              <div
                x-show="open"
                @click.outside="open = false"
                class="absolute w-[14rem] max-w-xs right-0 z-10 mt-2.5 w-32 origin-top-right flex flex-col divide-y justify-evenly rounded-md bg-white shadow-lg ring-1 overflow-hidden ring-gray-200 focus:outline-none"
                role="menu"
                x-transition:enter="transition ease-ease-in-out duration-300 transform"
                x-transition:enter-start="translate-x-full"
                x-transition:enter-end="translate-x-0"
                x-transition:leave="transition ease-in-out duration-300 transform"
                x-transition:leave-start="translate-y-0"
                x-transition:leave-end="-translate-y-full"
                aria-orientation="vertical"
                aria-labelledby="user-menu-button"
                tabindex="-1"
              >
                <.link
                  href={~p"/settings/home"}
                  class="flex items-center gap-4 px-3 py-4 text-sm leading-6 hover:text-brand-10 text-gray-900 font-medium hover:bg-gray-50"
                  role="menuitem"
                  tabindex="-1"
                  id="user-menu-item-1"
                >
                  <.icon_tag name="gear" /> Settings
                </.link>
                <!-- Active: "bg-gray-50", Not Active: "" -->
                <.link
                  href={~p"/users/log_out"}
                  method="delete"
                  class="flex items-center gap-4 px-3 py-4 text-sm leading-6 text-brand-2 hover:bg-gray-50 font-medium"
                  role="menuitem"
                  tabindex="-1"
                  id="user-menu-item-1"
                >
                  <.icon_tag name="arrow-right-from-box" /> Sign out
                </.link>
              </div>
            </div>
          </div>
        </div>

        <main
          class="flex-1 pb-8 border-l rounded-tl-lg bg-gradient-to-br from-slate-50 to-blue-50 transition-all duration-500 opacity-0 phx-page-loading:opacity-0"
          phx-mounted={JS.remove_class("opacity-0")}
        >
          <div class="container-depr min-h-[calc(100vh-8rem)] mx-auto">
            <%= @inner_content %>
          </div>
        </main>
      </div>
    </div>
    <!-- Loading Spinner -->
    <div
      id="main-loader"
      class="fixed inset-0 flex flex-col items-center justify-center bg-gray-100 bg-opacity-75 z-50"
    >
      <div class="w-10 h-10 border-4 border-t-transparent border-brand-10 rounded-full animate-spin">
      </div>

      <p class="mt-4 text-lg font-semibold text-gray-700">Loading, please wait...</p>
    </div>
    <%= live_render(@conn, Trace, sticky: true) %>
  </body>
</html>
