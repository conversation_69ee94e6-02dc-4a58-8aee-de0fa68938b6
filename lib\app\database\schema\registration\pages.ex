defmodule App.Registration.Pages do
  use Ecto.Schema
  import Ecto.Changeset
  schema "registration_pages" do
    field :name, :string
    field :url, :string
    field :icon, :string
    field :status, :integer, default: 1
    timestamps(type: :utc_datetime)
  end

  def changeset(page, attrs) do
    page
    |> cast(attrs, [:name, :url, :icon, :status])
    |> validate_required([:name, :url])
  end
end
