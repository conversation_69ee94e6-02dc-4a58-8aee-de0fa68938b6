defmodule App.ProductsTest do
  use App.DataCase

  alias App.Products

  describe "vouchers_types" do
    alias App.Products.Voucher

    import App.ProductsFixtures

    @invalid_attrs %{name: nil, status: nil}

    test "list_vouchers_types/0 returns all vouchers_types" do
      voucher = voucher_fixture()
      assert Products.list_vouchers_types() == [voucher]
    end

    test "get_voucher!/1 returns the voucher with given id" do
      voucher = voucher_fixture()
      assert Products.get_voucher!(voucher.id) == voucher
    end

    test "create_voucher/1 with valid data creates a voucher" do
      valid_attrs = %{name: "some name", status: 42}

      assert {:ok, %Voucher{} = voucher} = Products.create_voucher(valid_attrs)
      assert voucher.name == "some name"
      assert voucher.status == 42
    end

    test "create_voucher/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Products.create_voucher(@invalid_attrs)
    end

    test "update_voucher/2 with valid data updates the voucher" do
      voucher = voucher_fixture()
      update_attrs = %{name: "some updated name", status: 43}

      assert {:ok, %Voucher{} = voucher} = Products.update_voucher(voucher, update_attrs)
      assert voucher.name == "some updated name"
      assert voucher.status == 43
    end

    test "update_voucher/2 with invalid data returns error changeset" do
      voucher = voucher_fixture()
      assert {:error, %Ecto.Changeset{}} = Products.update_voucher(voucher, @invalid_attrs)
      assert voucher == Products.get_voucher!(voucher.id)
    end

    test "delete_voucher/1 deletes the voucher" do
      voucher = voucher_fixture()
      assert {:ok, %Voucher{}} = Products.delete_voucher(voucher)
      assert_raise Ecto.NoResultsError, fn -> Products.get_voucher!(voucher.id) end
    end

    test "change_voucher/1 returns a voucher changeset" do
      voucher = voucher_fixture()
      assert %Ecto.Changeset{} = Products.change_voucher(voucher)
    end
  end

  describe "vouchers" do
    alias App.Products.Voucher

    import App.ProductsFixtures

    @invalid_attrs %{name: nil, status: nil, description: nil, amount: nil, min_amount: nil}

    test "list_vouchers/0 returns all vouchers" do
      voucher = voucher_fixture()
      assert Products.list_vouchers() == [voucher]
    end

    test "get_voucher!/1 returns the voucher with given id" do
      voucher = voucher_fixture()
      assert Products.get_voucher!(voucher.id) == voucher
    end

    test "create_voucher/1 with valid data creates a voucher" do
      valid_attrs = %{
        name: "some name",
        status: 42,
        description: "some description",
        amount: 42,
        min_amount: 42
      }

      assert {:ok, %Voucher{} = voucher} = Products.create_voucher(valid_attrs)
      assert voucher.name == "some name"
      assert voucher.status == 42
      assert voucher.description == "some description"
      assert voucher.amount == 42
      assert voucher.min_amount == 42
    end

    test "create_voucher/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Products.create_voucher(@invalid_attrs)
    end

    test "update_voucher/2 with valid data updates the voucher" do
      voucher = voucher_fixture()

      update_attrs = %{
        name: "some updated name",
        status: 43,
        description: "some updated description",
        amount: 43,
        min_amount: 43
      }

      assert {:ok, %Voucher{} = voucher} = Products.update_voucher(voucher, update_attrs)
      assert voucher.name == "some updated name"
      assert voucher.status == 43
      assert voucher.description == "some updated description"
      assert voucher.amount == 43
      assert voucher.min_amount == 43
    end

    test "update_voucher/2 with invalid data returns error changeset" do
      voucher = voucher_fixture()
      assert {:error, %Ecto.Changeset{}} = Products.update_voucher(voucher, @invalid_attrs)
      assert voucher == Products.get_voucher!(voucher.id)
    end

    test "delete_voucher/1 deletes the voucher" do
      voucher = voucher_fixture()
      assert {:ok, %Voucher{}} = Products.delete_voucher(voucher)
      assert_raise Ecto.NoResultsError, fn -> Products.get_voucher!(voucher.id) end
    end

    test "change_voucher/1 returns a voucher changeset" do
      voucher = voucher_fixture()
      assert %Ecto.Changeset{} = Products.change_voucher(voucher)
    end
  end
end
