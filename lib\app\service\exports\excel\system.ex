defmodule App.Service.Export.SystemExcel do
  @moduledoc false

  alias Elixlsx.{Workbook, Sheet}

  alias App.Service.Table.AccessRoles
  alias App.Service.Table.Beneficiary
  alias App.Service.Table.BillPaymentReports
  alias App.Service.Logs.SystemLogs

  alias App.Service.Export.{
    ExtraData,
    Functions
  }

  def system_service(payload) do
    data_call =
      SystemLogs.export(payload)
      |> List.flatten()

    dates =
      Enum.map(data_call, & &1.inserted_at)
      |> Enum.filter(&(!is_nil(&1)))

    start_date =
      Functions.empty_check?(payload["start_date"]) ||
        try do
          Enum.max(dates)
        rescue
          _ -> ""
        end

    end_date =
      Functions.empty_check?(payload["end_date"]) ||
        try do
          Enum.min(dates)
        rescue
          _ -> ""
        end

    reports(data_call, start_date, end_date)
    |> content()
  end

  def reports(posts, start_date, end_date) do
    rows =
      posts
      |> Enum.map(&ExtraData.system_service_row/1)

    %Workbook{
      sheets: [
        %Sheet{
          name: "Clear System",
          rows:
            [
              [["System Service", align_horizontal: :center, bold: true]],
              [["FROM #{start_date} TO #{end_date}", align_horizontal: :center, bold: true]],
              ["", "", "", "", "", ""],
              ExtraData.system_service_header()
            ] ++ rows,
          merge_cells: [{"A1", "D1"}, {"A2", "D2"}]
        }
      ]
    }
  end

  def content(data) do
    Elixlsx.write_to_memory(data, "session_service#{:os.system_time()}.xlsx")
    |> elem(1)
    |> elem(1)
  end
end
