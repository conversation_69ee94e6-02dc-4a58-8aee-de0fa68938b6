defmodule AppWeb.Authenticated.Authentication.AccessRoleLive.View do
  @moduledoc false
  use AppWeb, :live_view
  import AppWeb.Helps.DataTable

  alias App.Service.Table.RoleUsers, as: TableQuery
  alias App.Roles

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("access_roles-view_users", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {
            "View Role Users Settings",
            "Accessed View Role Users Settings Page",
            "/"
          },
          {true, assigns.current_user.id}
        )
      end)

      socket =
        assign(socket, data: [])
        |> assign(:data_loader, true)
        |> assign(:role, params["role"])
        |> assign(live_socket_id: session["live_socket_id"])
        |> assign(:role_data, Roles.get_access_roles!(params["role"]))
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {
            "View Role Users Settings",
            "Access View Role Users Maintenance Page Denied due to permissions access",
            "/"
          },
          {false, assigns.current_user.id}
        )
      end)

      {:ok, push_navigate(socket, to: ~p"/dashboard", replace: true)}
    end
  end

  @impl true
  def handle_params(params, url, socket) do
    socket.endpoint.broadcast_from!(
      self(),
      "nav:" <> socket.assigns.live_socket_id,
      "change_nav",
      %{uri_path: URI.parse(url).path}
    )

    if connected?(socket), do: send(self(), {:get_list, params})

    {:noreply,
     socket
     |> assign(:params, params)}
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list -> list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})
      {:get_list, params} -> list(socket, params)
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, socket) do
    case target do
      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      "export" ->
        LiveFunctions.export_records(socket, value, "view_role_users", socket.assigns.params)

      _ ->
        {:noreply, socket}
    end
  end

  defp list(socket, params) do
    params = Map.put(params, "role_id", socket.assigns.role)

    {
      :noreply,
      assign(socket, :data, TableQuery.index(LivePageControl.create_table_params(socket, params)))
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end
end
