image: elixir:1.14

# Pick zero or more services to be used on all builds.
# Only needed when using a docker container to run your tests in.
services:
  - postgres:latest

variables:
  POSTGRES_DB: app_test
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres
  MIX_ENV: test

stages:
  - format
  - compile
  - test
  - deploy

before_script:
  - mix local.rebar --force
  - mix local.hex --force
  - mix deps.get

# Check code formatting
format:
  stage: format
  script:
    - mix format --check-formatted
  allow_failure: false
  rules:
    - if: $CI_PIPELINE_SOURCE == "push"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

# Compile with warnings as errors
compile:
  stage: compile
  script:
    - mix compile --warnings-as-errors
  allow_failure: false
  rules:
    - if: $CI_PIPELINE_SOURCE == "push"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

# Run tests
test:
  stage: test
  script:
    - mix test
  dependencies:
    - format
    - compile
  rules:
    - if: $CI_PIPELINE_SOURCE == "push"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

deploy:
  stage: deploy
  script: echo "Define your deployment script!"
  environment: production
  only:
    - main
    - master
