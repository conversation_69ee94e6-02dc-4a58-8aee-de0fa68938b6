defmodule Notification.Service.Logs.LogsSmsArchive do
  @moduledoc false
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false

  alias Notification.Notification.SmsArchive
  alias Notify.NotifyRepo, as: Repo
  alias App.{Accounts, Settings}

  @pagination [page_size: 10]

  def index(params, assigns) do
    compose_query(params, assigns)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
    |> optimize_query()
  end

  defp optimize_query(query) do
    query
    |> Map.update!(:entries, fn entries ->
      {updated_entries, _cache} =
        Enum.reduce(entries, {[], %{}}, fn entry, {acc, cache} ->
          sender_id = entry.sender_id
          client_id = entry.client_id

          # Fetch sender name, with caching
          {sender_name, cache} =
            case Map.get(cache, {:sender, sender_id}) do
              nil ->
                name = Settings.get_sender_by_id(sender_id || 0)
                {name, Map.put(cache, {:sender, sender_id}, name)}

              cached_name ->
                {cached_name, cache}
            end

          # Fetch client name, with caching
          {client_name, cache} =
            case Map.get(cache, {:client, client_id}) do
              nil ->
                name = Accounts.get_client_name_by_id(client_id || 0)
                {name, Map.put(cache, {:client, client_id}, name)}

              cached_name ->
                {cached_name, cache}
            end

          # Merge the updated entry and accumulate results
          updated_entry =
            Map.merge(entry, %{
              sender_id: sender_name,
              client: client_name
            })

          {[updated_entry | acc], cache}
        end)

      # Reverse the list to maintain the original order
      Enum.reverse(updated_entries)
    end)
  end

  def export(assigns, params) do
    assigns = Map.put_new(assigns, :client, nil)

    compose_query(params, assigns)
    |> Repo.all()
  end

  defp compose_query(params, assigns) do
    SmsArchive
    |> check_user_role(assigns)
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  defp check_user_role(query, assigns) do
    if !is_nil(assigns.client) do
      query
      |> where(
        [a],
        fragment(
          "? >= date_trunc('month', now() - interval '1 month') AND ? < date_trunc('month', now() + interval '1 month')",
          a.inserted_at,
          a.inserted_at
        ) and a.client_id == ^assigns.client.id
      )
    else
      query
      |> where(
        [a],
        fragment(
          "? >= date_trunc('month', now() - interval '1 month') AND ? < date_trunc('month', now() + interval '1 month')",
          a.inserted_at,
          a.inserted_at
        )
      )
    end
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(String.trim(value)))

      {"client_id", value}, query when byte_size(value) > 0 ->
        where(query, [a], a.client_id == ^value)

      {"mobile", value}, query when byte_size(value) > 0 ->
        where(query, [a], a.mobile == ^value)

      {"message_id", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.message_id, ^value))

      {"filename", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.filename, ^value))

      {"sender_id", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.sender_id, ^value))

      {"courier", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.courier, ^value))

      {"status", value}, query when byte_size(value) > 0 ->
        where(query, [a], a.status == ^value)

      {"start_date", value}, query when byte_size(value) > 0 ->
        naive_datetime = NaiveDateTime.from_iso8601!("#{value} 00:00:00")
        where(query, [s, c, d], s.inserted_at >= ^naive_datetime)

      {"end_date", value}, query when byte_size(value) > 0 ->
        naive_datetime = NaiveDateTime.from_iso8601!("#{value} 23:59:59")
        where(query, [s, c, d], s.inserted_at <= ^naive_datetime)

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  def compose_select(query) do
    select(query, [a], %{
      status: a.status,
      mobile: a.mobile,
      source: a.source,
      courier: a.courier,
      sender_id: a.sender_id,
      client_id: a.client_id,
      filename: a.filename,
      count: a.count,
      sent_at: a.sent_at,
      inserted_at: a.inserted_at,
      updated_at: a.updated_at,
      id: a.id
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      fragment("lower(?) LIKE lower(?)", a.mobile, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.source, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.filename, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
