defmodule App.Service.Export.CsvSmsLogsService do
  @moduledoc false
  alias App.Service.Logs.LogsSms

  @headers [
    "INIT DATE",
    "SENT DATE",
    "RECIPIENT",
    "MASSAGE ID",
    "ATTEMPT",
    "STATUS"
  ]

  def index(payload) do
    LogsSms.export(payload)
    |> Stream.map(
      &[
        Calendar.strftime(NaiveDateTime.add(&1.inserted_at, 7200, :second), "%d %B %Y %H:%M:%S"),
        Calendar.strftime(&1.date_sent, "%d %B %Y %H:%M:%S"),
        &1.recipient,
        &1.message_id,
        NumberF.comma_separated(&1.msg_count, 0),
        &1.status
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Sms Logs"],
              ["", "", "", "", ""],
              @headers,
              ["", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
