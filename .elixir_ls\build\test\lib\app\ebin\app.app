{application,app,
             [{compile_env,[{app,['Elixir.AppWeb.Gettext'],error},
                            {app,[dev_routes],error}]},
              {applications,[kernel,stdlib,elixir,logger,runtime_tools,
                             pdf_generator,os_mon,pbkdf2_elixir,phoenix,
                             phoenix_ecto,ecto_sql,postgrex,phoenix_html,
                             phoenix_live_view,floki,phoenix_live_dashboard,
                             swoosh,uuid,finch,telemetry_metrics,
                             telemetry_poller,gettext,jason,dns_cluster,joken,
                             bandit,cachex,ex_phone_number,phone,httpoison,
                             elixlsx,xlsxir,csv,quantum,browser,skooma,
                             bbmustache,scrivener_ecto,number_f,timex,
                             gen_smtp,sms_part_counter,live_select]},
              {description,"app"},
              {modules,['Elixir.App','Elixir.App.Accounts',
                        'Elixir.App.Accounts.AdminContacts',
                        'Elixir.App.Accounts.Company',
                        'Elixir.App.Accounts.Devices',
                        'Elixir.App.Accounts.User',
                        'Elixir.App.Accounts.UserToken',
                        'Elixir.App.AccountsFixtures',
                        'Elixir.App.Application','Elixir.App.Companies',
                        'Elixir.App.CustomContext','Elixir.App.DataCase',
                        'Elixir.App.Database.CustomDB',
                        'Elixir.App.ExceptionLogger','Elixir.App.Files',
                        'Elixir.App.Files.UploadedFile',
                        'Elixir.App.FilesFixtures',
                        'Elixir.App.Http.ResponseFormat',
                        'Elixir.App.Http.Send','Elixir.App.JobScheduler',
                        'Elixir.App.Jobs.JobCheckBalance',
                        'Elixir.App.Jobs.JobConditionTracking',
                        'Elixir.App.Jobs.LicenceApprovals',
                        'Elixir.App.LicenseConditions',
                        'Elixir.App.LicenseReviews',
                        'Elixir.App.LicenseReviews.Comments',
                        'Elixir.App.LicenseReviews.LicenseReview',
                        'Elixir.App.LicenseReviewsFixtures',
                        'Elixir.App.Licenses',
                        'Elixir.App.Licenses.ApprovalLevels',
                        'Elixir.App.Licenses.ConditionalLevels',
                        'Elixir.App.Licenses.Conditions',
                        'Elixir.App.Licenses.DynamicSchema',
                        'Elixir.App.Licenses.License',
                        'Elixir.App.Licenses.LicenseApprovalLogs',
                        'Elixir.App.Licenses.LicenseCategories',
                        'Elixir.App.Licenses.LicenseCategoriesData',
                        'Elixir.App.Licenses.LicenseCertificates',
                        'Elixir.App.Licenses.LicenseConditionsMapping',
                        'Elixir.App.Licenses.LicenseData',
                        'Elixir.App.Licenses.LicenseFields',
                        'Elixir.App.Licenses.LicenseMapping',
                        'Elixir.App.Licenses.NoticeOfChange',
                        'Elixir.App.LicensesFixtures','Elixir.App.Licensing',
                        'Elixir.App.Licensing.IssuedLicense',
                        'Elixir.App.Mailer',
                        'Elixir.App.Maintenance.Committees',
                        'Elixir.App.Management',
                        'Elixir.App.Management.ApiManagement',
                        'Elixir.App.Notification.UserNotifications',
                        'Elixir.App.Notifications.Messages',
                        'Elixir.App.NumberingLogic','Elixir.App.Registration',
                        'Elixir.App.Registration.ApplicationSteps',
                        'Elixir.App.Registration.Pages','Elixir.App.Release',
                        'Elixir.App.Repo',
                        'Elixir.App.RepresentativeDraftsFixtures',
                        'Elixir.App.Roles',
                        'Elixir.App.Roles.AccessRolePermission',
                        'Elixir.App.Roles.AccessRoles',
                        'Elixir.App.Roles.DepartmentRolePermission',
                        'Elixir.App.Roles.DepartmentRoles',
                        'Elixir.App.Roles.Permission',
                        'Elixir.App.Roles.UserPermission',
                        'Elixir.App.Schema.LicenseAccounts',
                        'Elixir.App.Send.Email',
                        'Elixir.App.Service.APIManagementLive.Functions',
                        'Elixir.App.Service.Dashboard.Stats',
                        'Elixir.App.Service.Export',
                        'Elixir.App.Service.Export.AccessRolesPdf',
                        'Elixir.App.Service.Export.AdminAccessRoleNoneRoleUserAccessPdf',
                        'Elixir.App.Service.Export.AdminEmailServicePdf',
                        'Elixir.App.Service.Export.AdminRoleAccessPdf',
                        'Elixir.App.Service.Export.ApiLogsServicePdf',
                        'Elixir.App.Service.Export.ApiServicesServicePdf',
                        'Elixir.App.Service.Export.ClientProfileServicePdf',
                        'Elixir.App.Service.Export.ClientServicePdf',
                        'Elixir.App.Service.Export.ClientStatisticsReportsPdf',
                        'Elixir.App.Service.Export.ContactPersonServicePdf',
                        'Elixir.App.Service.Export.CsvAccessService',
                        'Elixir.App.Service.Export.CsvAdminAccessNoneUserService',
                        'Elixir.App.Service.Export.CsvAdminAccessService',
                        'Elixir.App.Service.Export.CsvAdminEmailService',
                        'Elixir.App.Service.Export.CsvAnnualStatisticalService',
                        'Elixir.App.Service.Export.CsvApiLogs',
                        'Elixir.App.Service.Export.CsvApiLogsService',
                        'Elixir.App.Service.Export.CsvApiServicesService',
                        'Elixir.App.Service.Export.CsvArchieveSmsLogsServices',
                        'Elixir.App.Service.Export.CsvClientProfileService',
                        'Elixir.App.Service.Export.CsvClientStatisticsService',
                        'Elixir.App.Service.Export.CsvContactPersonService',
                        'Elixir.App.Service.Export.CsvDepartmentSRolesManagementService',
                        'Elixir.App.Service.Export.CsvDevice',
                        'Elixir.App.Service.Export.CsvErrorService',
                        'Elixir.App.Service.Export.CsvFunctions',
                        'Elixir.App.Service.Export.CsvMonthlyStatisticalService',
                        'Elixir.App.Service.Export.CsvNfsService',
                        'Elixir.App.Service.Export.CsvPaymentPlanServices',
                        'Elixir.App.Service.Export.CsvPostpaidClientStatementServices',
                        'Elixir.App.Service.Export.CsvPrepaidClientStatementServices',
                        'Elixir.App.Service.Export.CsvPurchasesService',
                        'Elixir.App.Service.Export.CsvRatesServices',
                        'Elixir.App.Service.Export.CsvSelfRegistrationApplicationsServices',
                        'Elixir.App.Service.Export.CsvSenderService',
                        'Elixir.App.Service.Export.CsvServiceProviderReports',
                        'Elixir.App.Service.Export.CsvSessionLogService',
                        'Elixir.App.Service.Export.CsvSessionLogsService',
                        'Elixir.App.Service.Export.CsvSmppServicesServices',
                        'Elixir.App.Service.Export.CsvSmsLogsService',
                        'Elixir.App.Service.Export.CsvSmsLogsServices',
                        'Elixir.App.Service.Export.CsvSystemAuditLogs',
                        'Elixir.App.Service.Export.CsvSystemLogService',
                        'Elixir.App.Service.Export.CsvTransactionReportsService',
                        'Elixir.App.Service.Export.CsvUsers',
                        'Elixir.App.Service.Export.DepartmentRolesManagementServicePdf',
                        'Elixir.App.Service.Export.DeviceServicePdf',
                        'Elixir.App.Service.Export.ExcelAccessRolesService',
                        'Elixir.App.Service.Export.ExcelAccessService',
                        'Elixir.App.Service.Export.ExcelAdminAccessRoleNoneRoleUserAccessService',
                        'Elixir.App.Service.Export.ExcelAdminEmailService',
                        'Elixir.App.Service.Export.ExcelAdminRoleAccessService',
                        'Elixir.App.Service.Export.ExcelAnnualStaticsticsService',
                        'Elixir.App.Service.Export.ExcelApiLogService',
                        'Elixir.App.Service.Export.ExcelApiLogsService',
                        'Elixir.App.Service.Export.ExcelApiServicesService',
                        'Elixir.App.Service.Export.ExcelClientProfileService',
                        'Elixir.App.Service.Export.ExcelClientService',
                        'Elixir.App.Service.Export.ExcelClientStatisticsService',
                        'Elixir.App.Service.Export.ExcelContactPersonService',
                        'Elixir.App.Service.Export.ExcelDepartmentRolesManagementService',
                        'Elixir.App.Service.Export.ExcelDeviceService',
                        'Elixir.App.Service.Export.ExcelErrorService',
                        'Elixir.App.Service.Export.ExcelFunctions',
                        'Elixir.App.Service.Export.ExcelMonthlyStaticsticsService',
                        'Elixir.App.Service.Export.ExcelNfsService',
                        'Elixir.App.Service.Export.ExcelPaymentPlansService',
                        'Elixir.App.Service.Export.ExcelPostpaidClientStatementService',
                        'Elixir.App.Service.Export.ExcelPrepaidClientStatementService',
                        'Elixir.App.Service.Export.ExcelPurchasesService',
                        'Elixir.App.Service.Export.ExcelRatesService',
                        'Elixir.App.Service.Export.ExcelSelfRegistrationApplicationsService',
                        'Elixir.App.Service.Export.ExcelSenderService',
                        'Elixir.App.Service.Export.ExcelServiceProviderReport',
                        'Elixir.App.Service.Export.ExcelSessionLogService',
                        'Elixir.App.Service.Export.ExcelSessionLogsService',
                        'Elixir.App.Service.Export.ExcelSmppServicesService',
                        'Elixir.App.Service.Export.ExcelSmsLogArchieve',
                        'Elixir.App.Service.Export.ExcelSmsLogService',
                        'Elixir.App.Service.Export.ExcelSmsLogsService',
                        'Elixir.App.Service.Export.ExcelSystemAuditLogsService',
                        'Elixir.App.Service.Export.ExcelSystemLogService',
                        'Elixir.App.Service.Export.ExcelTransactionReportsService',
                        'Elixir.App.Service.Export.ExcelUsersService',
                        'Elixir.App.Service.Export.ExtraData',
                        'Elixir.App.Service.Export.Functions',
                        'Elixir.App.Service.Export.NfsServicePdf',
                        'Elixir.App.Service.Export.PaymentPlansServicePdf',
                        'Elixir.App.Service.Export.PdfAnnualStaticsticsService',
                        'Elixir.App.Service.Export.PdfApiLogService',
                        'Elixir.App.Service.Export.PdfErrorServicePdf',
                        'Elixir.App.Service.Export.PdfFunctions',
                        'Elixir.App.Service.Export.PdfMonthlyStatisticalServicePdf',
                        'Elixir.App.Service.Export.PdfSessionLogService',
                        'Elixir.App.Service.Export.PdfSmppServicesService',
                        'Elixir.App.Service.Export.PdfSmsLogsAchieve',
                        'Elixir.App.Service.Export.PdfSmsLogsService',
                        'Elixir.App.Service.Export.PdfSystemLogService',
                        'Elixir.App.Service.Export.PdfTransactionReportsService',
                        'Elixir.App.Service.Export.PostpaidClientStatementServicePdf',
                        'Elixir.App.Service.Export.PrepaidClientStatementServicePdf',
                        'Elixir.App.Service.Export.PurchasesServicePdf',
                        'Elixir.App.Service.Export.RatesServicePdf',
                        'Elixir.App.Service.Export.SelfRegistrationApplicationsServicePdf',
                        'Elixir.App.Service.Export.SenderServicePdf',
                        'Elixir.App.Service.Export.ServiceProviderReportsPdf',
                        'Elixir.App.Service.Export.SessionLogsServicePdf',
                        'Elixir.App.Service.Export.SmsLogsServicePdf',
                        'Elixir.App.Service.Export.SystemAuditLogsServicePdf',
                        'Elixir.App.Service.Export.SystemCsv',
                        'Elixir.App.Service.Export.SystemExcel',
                        'Elixir.App.Service.Export.SystemPdf',
                        'Elixir.App.Service.Export.UserServicePdf',
                        'Elixir.App.Service.FileHandler',
                        'Elixir.App.Service.Functions.AccessRoleLive',
                        'Elixir.App.Service.Functions.AssociateUpload',
                        'Elixir.App.Service.Functions.SMS.BroadcastUpload',
                        'Elixir.App.Service.Functions.SMS.MulticastUpload',
                        'Elixir.App.Service.LicenceApplications.Functions',
                        'Elixir.App.Service.LicenseApplications.Functions',
                        'Elixir.App.Service.Logs.AccessLogs',
                        'Elixir.App.Service.Logs.ApiLogs',
                        'Elixir.App.Service.Logs.LogsSession',
                        'Elixir.App.Service.Logs.System',
                        'Elixir.App.Service.Logs.SystemLogs',
                        'Elixir.App.Service.ServicMappingConditions.Functions',
                        'Elixir.App.Service.ServiceAccountsMaintenance.Functions',
                        'Elixir.App.Service.ServiceAdminEmailMaintenance.Functions',
                        'Elixir.App.Service.ServiceAdminMaintenance.Functions',
                        'Elixir.App.Service.ServiceApprovalLevels.Functions',
                        'Elixir.App.Service.ServiceClientMaintenance.Functions',
                        'Elixir.App.Service.ServiceCommittee.Functions',
                        'Elixir.App.Service.ServiceCompanyAttach.Functions',
                        'Elixir.App.Service.ServiceConditionalLevels.Functions',
                        'Elixir.App.Service.ServiceConditions.Functions',
                        'Elixir.App.Service.ServiceContactPersonMaintenance.Functions',
                        'Elixir.App.Service.ServiceError.Functions',
                        'Elixir.App.Service.ServiceLicenseCategory.Functions',
                        'Elixir.App.Service.ServiceLicenseMaintenance.Functions',
                        'Elixir.App.Service.ServiceSender.Functions',
                        'Elixir.App.Service.Statistics.Dashboard',
                        'Elixir.App.Service.SystemRoleLive.Function',
                        'Elixir.App.Service.Table.AccessRoles1',
                        'Elixir.App.Service.Table.AddRoleUsers',
                        'Elixir.App.Service.Table.ApiManagement',
                        'Elixir.App.Service.Table.ApprovalLevels',
                        'Elixir.App.Service.Table.AssociatesManagement',
                        'Elixir.App.Service.Table.ClientLicenceApplications',
                        'Elixir.App.Service.Table.ClientMaintenance',
                        'Elixir.App.Service.Table.ClientReports',
                        'Elixir.App.Service.Table.Committee',
                        'Elixir.App.Service.Table.ConditionalApplications',
                        'Elixir.App.Service.Table.ConditionalLevels',
                        'Elixir.App.Service.Table.Conditions',
                        'Elixir.App.Service.Table.Contact',
                        'Elixir.App.Service.Table.DepartmentRoles',
                        'Elixir.App.Service.Table.IssuedApplications',
                        'Elixir.App.Service.Table.LicenceApplications',
                        'Elixir.App.Service.Table.LicenseApprovalLogs',
                        'Elixir.App.Service.Table.LicenseCategory',
                        'Elixir.App.Service.Table.Maintenance.ServiceDevices',
                        'Elixir.App.Service.Table.MonthlyReports',
                        'Elixir.App.Service.Table.Notifications',
                        'Elixir.App.Service.Table.PendingAttachments',
                        'Elixir.App.Service.Table.PeriodicReports',
                        'Elixir.App.Service.Table.ProviderReports',
                        'Elixir.App.Service.Table.Registration.ApplicationSteps.Functions',
                        'Elixir.App.Service.Table.Report.IssuedLicense',
                        'Elixir.App.Service.Table.Report.IssuedLicense.Functions',
                        'Elixir.App.Service.Table.RoleUsers',
                        'Elixir.App.Service.Table.ServiceClientUsers',
                        'Elixir.App.Service.Table.ServiceErrors',
                        'Elixir.App.Service.Table.ServiceSender',
                        'Elixir.App.Service.Table.Users',
                        'Elixir.App.Service.Table.YearlyReports',
                        'Elixir.App.Services.Table.AccessRoles',
                        'Elixir.App.Services.Table.Licenses.CertificateDraftService',
                        'Elixir.App.Services.Table.Messages.MessageDraftService',
                        'Elixir.App.Services.Table.Utilities.LicenceDraftService',
                        'Elixir.App.Services.Table.Utilities.SummaryDraftService',
                        'Elixir.App.SetUp.AdminContacts',
                        'Elixir.App.SetUp.ApiManagement',
                        'Elixir.App.SetUp.ApprovalLevels',
                        'Elixir.App.SetUp.Certificates',
                        'Elixir.App.SetUp.Committees',
                        'Elixir.App.SetUp.Conditions',
                        'Elixir.App.SetUp.LicenceDrafts',
                        'Elixir.App.SetUp.LicenseCategories',
                        'Elixir.App.SetUp.Licenses',
                        'Elixir.App.SetUp.MessageDrafts',
                        'Elixir.App.SetUp.Permissions',
                        'Elixir.App.SetUp.RegPages','Elixir.App.SetUp.Roles',
                        'Elixir.App.SetUp.Service','Elixir.App.SetUp.Setting',
                        'Elixir.App.SetUp.SummaryDrafts',
                        'Elixir.App.SetUp.Users','Elixir.App.Settings',
                        'Elixir.App.Settings.Setting',
                        'Elixir.App.Settings.SettingsConfig',
                        'Elixir.App.Setup.Cities','Elixir.App.SummaryData',
                        'Elixir.App.SummaryData.SummarisedData',
                        'Elixir.App.SummaryDataFixtures',
                        'Elixir.App.SummaryDrafts',
                        'Elixir.App.SummaryDrafts.SummaryDraft',
                        'Elixir.App.Table.Registration.ApplicationSteps.Service',
                        'Elixir.App.Table.Registration.Page.Service',
                        'Elixir.App.Users','Elixir.App.Util.Aa',
                        'Elixir.App.Util.CustomFunctions',
                        'Elixir.App.Util.CustomTime',
                        'Elixir.App.Util.DateCounter',
                        'Elixir.App.Util.Extract',
                        'Elixir.App.Util.NumberFunctions',
                        'Elixir.App.Util.Randomizer','Elixir.App.Utilities',
                        'Elixir.App.Utilities.ErrorCode',
                        'Elixir.App.Utilities.LicenceDraft',
                        'Elixir.App.Validators.DynamicFormValidator',
                        'Elixir.AppWeb','Elixir.AppWeb.AddToConn',
                        'Elixir.AppWeb.Admin.UserLive.Permissions',
                        'Elixir.AppWeb.ApiBasicAuth',
                        'Elixir.AppWeb.ApiDeviceAuth',
                        'Elixir.AppWeb.ApiSocket','Elixir.AppWeb.ApiUserAuth',
                        'Elixir.AppWeb.ApplyLive.Index',
                        'Elixir.AppWeb.ApprovalLevelsLive.FormComponent',
                        'Elixir.AppWeb.Auth.ApplicationDetails1Live.Index',
                        'Elixir.AppWeb.Auth.ApplicationSummaryLive.Index',
                        'Elixir.AppWeb.Auth.AssociatesManagementLive.Index',
                        'Elixir.AppWeb.Auth.Authentication.Payment.BundlePurchaseResponseLive.Response',
                        'Elixir.AppWeb.Auth.ClientApplicationsLive.Index',
                        'Elixir.AppWeb.Auth.ClientManagementLive.Index',
                        'Elixir.AppWeb.Auth.CommitteeLive.Index',
                        'Elixir.AppWeb.Auth.ConditionTrackingLive.Index',
                        'Elixir.AppWeb.Auth.ConditionalApplicationsLive.Index',
                        'Elixir.AppWeb.Auth.FinanceLive.Index',
                        'Elixir.AppWeb.Auth.IssuedApplicationsLive.Index',
                        'Elixir.AppWeb.Auth.LicenceApplicationsLive.ApprovalReport',
                        'Elixir.AppWeb.Auth.LicenceApplicationsLive.Index',
                        'Elixir.AppWeb.Auth.LicenceCertificateController',
                        'Elixir.AppWeb.Auth.LicenceChangeLive.Index',
                        'Elixir.AppWeb.Auth.LicenceRegistrationLive.Index',
                        'Elixir.AppWeb.Auth.LicenceTemplateController',
                        'Elixir.AppWeb.Auth.LicenseCategoriesLive.Index',
                        'Elixir.AppWeb.Auth.LicenseLive.Index',
                        'Elixir.AppWeb.Auth.LicenseMagementLive.Index',
                        'Elixir.AppWeb.Auth.LicenseSummaryLive.Index',
                        'Elixir.AppWeb.Auth.LicensesUpdateLive.Index',
                        'Elixir.AppWeb.Auth.LogsLive.Access',
                        'Elixir.AppWeb.Auth.LogsLive.Api',
                        'Elixir.AppWeb.Auth.LogsLive.ApiComponent',
                        'Elixir.AppWeb.Auth.LogsLive.Audit',
                        'Elixir.AppWeb.Auth.LogsLive.DevicesLive.Index',
                        'Elixir.AppWeb.Auth.LogsLive.Index',
                        'Elixir.AppWeb.Auth.LogsLive.MessagingAchieveLive.Index',
                        'Elixir.AppWeb.Auth.LogsLive.Session',
                        'Elixir.AppWeb.Auth.LogsLive.Sms',
                        'Elixir.AppWeb.Auth.Navigation',
                        'Elixir.AppWeb.Auth.NewAssociatesLive.Index',
                        'Elixir.AppWeb.Auth.NotificationLive.Index',
                        'Elixir.AppWeb.Auth.PendingAssociatesLive.Index',
                        'Elixir.AppWeb.Auth.Registration.PaymentsLive.Index',
                        'Elixir.AppWeb.Auth.Registration.SummaryLive.Index',
                        'Elixir.AppWeb.Auth.Registration.TextFieldsLive.Index',
                        'Elixir.AppWeb.Auth.Registration.UploadsLive.Index',
                        'Elixir.AppWeb.Auth.RegistrationLive.Entry',
                        'Elixir.AppWeb.Auth.Reports.TransactionReportLive.Index',
                        'Elixir.AppWeb.Auth.Schedule.BroadcastLive.Index',
                        'Elixir.AppWeb.Auth.Schedule.MulticastLive.Index',
                        'Elixir.AppWeb.Auth.Schedule.SingleLive.Index',
                        'Elixir.AppWeb.Auth.SummaryApplicationsLive.Index',
                        'Elixir.AppWeb.Auth.UploadAssociatesLive.Index',
                        'Elixir.AppWeb.Auth.UserLive.Index',
                        'Elixir.AppWeb.Auth.ViewApplicationsLive.Index',
                        'Elixir.AppWeb.Auth.ViewApplicationsLive.Representative',
                        'Elixir.AppWeb.Authenticated.Auth.AccessRoleLive.Redirect',
                        'Elixir.AppWeb.Authenticated.Authentication.AccessRoleLive.Add',
                        'Elixir.AppWeb.Authenticated.Authentication.AccessRoleLive.Index',
                        'Elixir.AppWeb.Authenticated.Authentication.AccessRoleLive.View',
                        'Elixir.AppWeb.Authenticated.Authentication.DepartmentRoleLive.Index',
                        'Elixir.AppWeb.Authenticated.Authentication.PermissionsLive.Access',
                        'Elixir.AppWeb.Authenticated.Authentication.PermissionsLive.Department',
                        'Elixir.AppWeb.Authenticated.Authentication.PermissionsLive.User',
                        'Elixir.AppWeb.Authenticated.ErrorLive.Index',
                        'Elixir.AppWeb.Authenticated.SettingsLive.ConfigLive.Index',
                        'Elixir.AppWeb.Authenticated.SettingsLive.FunctionLive.Index',
                        'Elixir.AppWeb.Authenticated.SettingsLive.Index',
                        'Elixir.AppWeb.Authenticated.SettingsLive.ProfileLive.Index',
                        'Elixir.AppWeb.CSPHeader','Elixir.AppWeb.ClassUtil',
                        'Elixir.AppWeb.CommitteeLive.FormComponent',
                        'Elixir.AppWeb.Component',
                        'Elixir.AppWeb.Components.FormNav.WizardFormNav',
                        'Elixir.AppWeb.Components.IframePrint',
                        'Elixir.AppWeb.Components.Notifications',
                        'Elixir.AppWeb.Components.RichTextEditorComponent',
                        'Elixir.AppWeb.Components.Transactions.PendingAmounts',
                        'Elixir.AppWeb.Components.Transactions.PendingPayment',
                        'Elixir.AppWeb.Components.Transactions.TotalAmountCard',
                        'Elixir.AppWeb.ConditionalLevelsLive.FormComponent',
                        'Elixir.AppWeb.ConditionsLive.FormComponent',
                        'Elixir.AppWeb.ConnCase',
                        'Elixir.AppWeb.CoreComponents',
                        'Elixir.AppWeb.Dashboard.Admin.NewApplications',
                        'Elixir.AppWeb.Dashboard.Admin.ReturnedToApplicant',
                        'Elixir.AppWeb.Dashboard.Admin.SubmittedMarketOperations',
                        'Elixir.AppWeb.Dashboard.Admin.SubmittedSupervisor',
                        'Elixir.AppWeb.Dashboard.AdminLive.Index',
                        'Elixir.AppWeb.Dashboard.ClientLandingLive',
                        'Elixir.AppWeb.Dashboard.ClientLive.Index',
                        'Elixir.AppWeb.Dashboard.Licenses',
                        'Elixir.AppWeb.Dashboard.PendingApplications',
                        'Elixir.AppWeb.Dashboard.RegisteredLicenses',
                        'Elixir.AppWeb.Dashboard.Sandbox',
                        'Elixir.AppWeb.Dashboard.ServerStats',
                        'Elixir.AppWeb.Dashboard.SystemTrack',
                        'Elixir.AppWeb.Dashboard.TotalClients',
                        'Elixir.AppWeb.Dashboard.TotalDealers',
                        'Elixir.AppWeb.Dashboard.TotalDeclinedLicenses',
                        'Elixir.AppWeb.Dashboard.TotalIssueLicenses',
                        'Elixir.AppWeb.Dashboard.TotalLicenses',
                        'Elixir.AppWeb.Dashboard.TrafficChart',
                        'Elixir.AppWeb.Dashboard.ViewComponent',
                        'Elixir.AppWeb.DashboardLive.Dashboard',
                        'Elixir.AppWeb.DashboardLive.Dashboard.Loader',
                        'Elixir.AppWeb.DocumentComponent',
                        'Elixir.AppWeb.DocumentUploadComponent',
                        'Elixir.AppWeb.Endpoint','Elixir.AppWeb.ErrorHTML',
                        'Elixir.AppWeb.ErrorJSON',
                        'Elixir.AppWeb.ErrorLive.FormComponent',
                        'Elixir.AppWeb.ExportsChannel',
                        'Elixir.AppWeb.FileController',
                        'Elixir.AppWeb.Gettext',
                        'Elixir.AppWeb.Helpers.IconHelper',
                        'Elixir.AppWeb.Helps.DataTable',
                        'Elixir.AppWeb.Helps.LivePageControl',
                        'Elixir.AppWeb.Helps.PaginationComponent',
                        'Elixir.AppWeb.Helps.Tracking',
                        'Elixir.AppWeb.IssuedLicensesLive.Index',
                        'Elixir.AppWeb.Layouts',
                        'Elixir.AppWeb.LicenceDetailsLive.ConditionComponent',
                        'Elixir.AppWeb.LicenceDetailsLive.UploadfileComponent',
                        'Elixir.AppWeb.LicenceDetailsLive.ViewComponent',
                        'Elixir.AppWeb.LicenceStepMappingLive.Index',
                        'Elixir.AppWeb.LicenseCategoriesLive.FormComponent',
                        'Elixir.AppWeb.LicenseDownloadController',
                        'Elixir.AppWeb.LicenseLive.FormComponent',
                        'Elixir.AppWeb.LiveFunctions',
                        'Elixir.AppWeb.Login.LoaderComponent',
                        'Elixir.AppWeb.Maintenance.ApiManagementLive.Index',
                        'Elixir.AppWeb.Maintenance.ApplicationLevelsLive.Index',
                        'Elixir.AppWeb.Maintenance.ApprovalLevelsLive.Index',
                        'Elixir.AppWeb.Maintenance.CertificateDraftLive.Index',
                        'Elixir.AppWeb.Maintenance.ConditionalLevelsLive.Index',
                        'Elixir.AppWeb.Maintenance.ConditionsLive.Index',
                        'Elixir.AppWeb.Maintenance.LicenceDraftLive.Index',
                        'Elixir.AppWeb.Maintenance.LicenceSummaryDraftLive.Index',
                        'Elixir.AppWeb.MessageDraftLive.FormComponent',
                        'Elixir.AppWeb.MessageDraftLive.Index',
                        'Elixir.AppWeb.Model.SessionCountDown',
                        'Elixir.AppWeb.PageController',
                        'Elixir.AppWeb.PageHTML',
                        'Elixir.AppWeb.PageLive.FormComponent',
                        'Elixir.AppWeb.PageLive.Index',
                        'Elixir.AppWeb.PermissionHelpers',
                        'Elixir.AppWeb.Plug.UserAuth',
                        'Elixir.AppWeb.Plugs.RequirePermission',
                        'Elixir.AppWeb.Plugs.SecureHeaders',
                        'Elixir.AppWeb.ReceiptController',
                        'Elixir.AppWeb.ReceiptHTML',
                        'Elixir.AppWeb.Registration.Navigation',
                        'Elixir.AppWeb.Registration.NavigationComponent',
                        'Elixir.AppWeb.Reports.ClientReportsLive.Index',
                        'Elixir.AppWeb.Reports.MonthlyLive.Index',
                        'Elixir.AppWeb.Reports.ProviderReportsLive.Index',
                        'Elixir.AppWeb.Reports.YearlyLive.Index',
                        'Elixir.AppWeb.Router',
                        'Elixir.AppWeb.SelfRegistration.LandingLive.Index',
                        'Elixir.AppWeb.SendSms.BroadcastLive.Index',
                        'Elixir.AppWeb.SendSms.MulticastLive.Index',
                        'Elixir.AppWeb.SendSms.SingleLive.Index',
                        'Elixir.AppWeb.Settings.AccessRoleLive.FormComponent',
                        'Elixir.AppWeb.Settings.DepartmentRoleLive.FormComponent',
                        'Elixir.AppWeb.SweetAlertChannel',
                        'Elixir.AppWeb.Telemetry',
                        'Elixir.AppWeb.TransactionChannel',
                        'Elixir.AppWeb.UploadedList.MessageList.ViewComponent',
                        'Elixir.AppWeb.UploadedList.MulticastList.ViewComponent',
                        'Elixir.AppWeb.UploadedList.ScheduleMessageList.ViewComponent',
                        'Elixir.AppWeb.UploadedList.ScheduleMulticastList.ViewComponent',
                        'Elixir.AppWeb.UploadedList.ViewComponent',
                        'Elixir.AppWeb.UserAuth',
                        'Elixir.AppWeb.UserForgotPasswordLive',
                        'Elixir.AppWeb.UserLive.FormComponent',
                        'Elixir.AppWeb.UserLoginLive',
                        'Elixir.AppWeb.UserRegistration.BusinessLive.Index',
                        'Elixir.AppWeb.UserRegistration.IndividualLive.Index',
                        'Elixir.AppWeb.UserSessionChannel',
                        'Elixir.AppWeb.UserSessionController',
                        'Elixir.AppWeb.UserSocket','Elixir.Card',
                        'Elixir.FormJ','Elixir.Inspect.App.Accounts.User',
                        'Elixir.Inspect.App.Schema.LicenseAccounts',
                        'Elixir.Inspect.App.Settings.SettingsConfig',
                        'Elixir.Log.Service.Export.ExtraData',
                        'Elixir.Logs.Audit','Elixir.Logs.Audit.Access',
                        'Elixir.Logs.Audit.Api','Elixir.Logs.Audit.Email',
                        'Elixir.Logs.Audit.Endpoints',
                        'Elixir.Logs.Audit.ExceptionErrorLogs',
                        'Elixir.Logs.Audit.Redirect',
                        'Elixir.Logs.Audit.Session',
                        'Elixir.Logs.Audit.System','Elixir.Logs.Health',
                        'Elixir.Logs.Health.HealthCheck',
                        'Elixir.Logs.Health.HealthCheckLog',
                        'Elixir.Logs.LogRepo','Elixir.Model',
                        'Elixir.Notification.MessageDrafts',
                        'Elixir.Notification.Notification.Email',
                        'Elixir.Notification.Notification.MessageDraft',
                        'Elixir.Notification.Notification.SmsArchive',
                        'Elixir.Notification.Notification.SmsLogs',
                        'Elixir.Notification.Service.Logs.LogsSms',
                        'Elixir.Notification.Service.Logs.LogsSmsArchive',
                        'Elixir.Notification.SetUp.Messages',
                        'Elixir.Notification.Sms','Elixir.Notify.NotifyRepo',
                        'Elixir.Option','Elixir.ParamValidator',
                        'Elixir.Reports.Context',
                        'Elixir.SmsLogs.Archive.ViewComponent',
                        'Elixir.SmsLogs.ViewComponent',
                        'Elixir.Statistics.Logs.MonthlyStatistic',
                        'Elixir.Statistics.Logs.PeriodicStatistic',
                        'Elixir.Statistics.Logs.SenderMonthlyStatistic',
                        'Elixir.Statistics.Logs.SenderPeriodicStatistic',
                        'Elixir.Statistics.StatRepo','Elixir.StatsLinux',
                        'Elixir.StatsWindows','Elixir.SystemStats',
                        'Elixir.Table','Elixir.TopNav',
                        'Elixir.TransactionReceiptComponent']},
              {registered,[]},
              {vsn,"0.1.0"},
              {mod,{'Elixir.App.Application',[]}}]}.
