defmodule App.Repo.Migrations.CreateDevices do
  use Ecto.Migration

  def change do
    create table(:devices) do
      add :device_id, :string, size: 50
      add :name, :string, size: 30
      add :platform, :string, size: 30
      add :operating_system, :string, size: 50
      add :status, :integer, default: 1
      add :last_activity, :naive_datetime
      add :user_id, references(:tbl_users, on_delete: :nothing)

      timestamps(type: :utc_datetime)
    end

    create index(:devices, [:user_id])
    create unique_index(:devices, [:device_id, :user_id])
  end
end
