defmodule App.Service.Export.PdfTransactionReportsService do
  @moduledoc false

  alias App.Service.Table.Transaction.ServiceTransactions
  alias App.Service.Export.Functions

  def index(assigns, payload) do
    results =
      ServiceTransactions.export(assigns, payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "txn_date" => NaiveDateTime.to_string(data.txn_date),
          "reference" => data.reference,
          "txb_amount" => data.txb_amount,
          "narration" => data.narration,
          "client" => data.client,
          "transacting_user" => data.transacting_user,
          "status" => data.status
        }
      end)
      |> Enum.map(fn data ->
        """
        <tr>
            <td style="text-align: center;">{{ txn_date }}</td>
            <td style="text-align: center;">{{ reference }}</td>
            <td style="text-align: center;">{{ txb_amount }}</td>
            <td style="text-align: center;">{{ narration }}</td>
            <td style="text-align: center;">{{ client }}</td>
            <td style="text-align: center;">{{ transacting_user }}</td>
            <td style="text-align: center;">{{ status }}</td>

        </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/transaction_reports_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "System Audit Logs",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end
