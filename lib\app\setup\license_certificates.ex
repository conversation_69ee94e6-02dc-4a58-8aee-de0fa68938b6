defmodule App.SetUp.Certificates do
  @moduledoc false
  alias App.{Repo, Licenses.LicenseCertificates}

  defp run_seeds_db(schema), do: Repo.insert!(schema)

  def init({sec_ex, dealer, inv_adviser, rep, ros}) do
    # Dealer's

    dealer_certificate =
      run_seeds_db(%LicenseCertificates{
        template: """
        <p class="ql-align-right"><strong>Licence No: DL/XX/XX</strong></p>
        <p class="ql-align-center" style="padding-bottom: 20px;">
        <img src="/images/pbs-logo.png" alt="SEC Logo" style="width: 150px; height: auto; display: block; margin: 0 auto;">
        </p>

        <p class="ql-align-center">In accordance with the provisions of Part V of the Securities Act, 2016 (Amended by Act NO.21 of 2022), the Securities and Exchange Commission hereby grants <strong>a DEALER'S LICENCE</strong> to:</p>

        <p class="ql-align-center"><strong>{{applicant_name}}</strong></p>



        <p class="ql-align-center">{{principal_business_address}}</p>

        <p class="ql-align-center">With effect from the <strong>2nd day of (May) 2025</strong>.</p>

        <p class="ql-align-right"><strong>BOARD CHAIRPERSON</strong></p>
        """,
        name: "Dealer's License",
        description: "This is a template for the Dealer's License issued by the SEC Zambia.",
        status: 1
      })

    # credit rating

    credit =
      run_seeds_db(%LicenseCertificates{
        template: """
        <p class="ql-align-center"><strong>Licence No. CRA/XX/XX</strong></p><br><br>

            <p class="ql-align-center" style="padding-bottom: 20px;">
        <img src="/images/pbs-logo.png" alt="SEC Logo" style="width: 150px; height: auto; display: block; margin: 0 auto;">
        </p>
        <p class="ql-align-center">In accordance with the provisions of Part VI of the Securities Act, 2016 (Amended by Act NO.21 of 2022)</p>
        <p class="ql-align-center">the Securities and Exchange Commission hereby grants</p><br>
        <p class="ql-align-center"><strong>a CREDIT RATING AGENCY LICENCE to:</strong></p><br>
        <p class="ql-align-center"><strong><em>{{applicant_name}}</em></strong></p>
        <p class="ql-align-center">{{principal_business_address}}</p><br>
        <p class="ql-align-center">With effect from the {{day}} day of {{month}} {{year}}.</p><br><br>
        <p class="ql-align-center">______________________________________</p>
        <p class="ql-align-center"><strong>BOARD CHAIRPERSON</strong></p>
        </div>

        """,
        name: "Credit Rating Agency License",
        description:
          "This is a template for the Credit Rating Agency License issued by the SEC Zambia.",
        status: 1
      })

    # share transfer agent

    share =
      run_seeds_db(%LicenseCertificates{
        template: """
         <p class="ql-align-center"><strong>Licence No. STAL/XX/XX</strong></p><br><br>

             <p class="ql-align-center" style="padding-bottom: 20px;">
        <img src="/images/pbs-logo.png" alt="SEC Logo" style="width: 150px; height: auto; display: block; margin: 0 auto;">
        </p>
        <p class="ql-align-center">In accordance with the provisions of Part V of the Securities Act, 2016 (Amended by Act NO.21 of 2022)</p>
        <p class="ql-align-center">the Securities and Exchange Commission hereby grants</p><br>
        <p class="ql-align-center"><strong>a SHARE TRANSFER AGENT’S LICENCE to:</strong></p><br>
        <p class="ql-align-center"><strong><em>{{applicant_name}}</em></strong></p>
        <p class="ql-align-center">{{principal_business_address}}</p><br>
        <p class="ql-align-center">With effect from the {{day}} day of {{month}} {{year}}.</p><br><br>
        <p class="ql-align-center">______________________________________</p>
        <p class="ql-align-center"><strong>BOARD CHAIRPERSON</strong></p>

        """,
        name: "Share Transfer Agent License",
        description:
          "This is a template for the Share Transfer Agent License issued by the SEC Zambia.",
        status: 1
      })

    # Representative's

    rep_certificate =
      run_seeds_db(%LicenseCertificates{
        template: """
        <p class="ql-align-right"><strong>Licence No: DRL/XX/XX</strong></p>

            <p class="ql-align-center" style="padding-bottom: 20px;">
        <img src="/images/pbs-logo.png" alt="SEC Logo" style="width: 150px; height: auto; display: block; margin: 0 auto;">
        </p>

        <p class="ql-align-center">In accordance with the provisions of Part V of the Securities Act, 2016 (Amended by Act NO.21 of 2022), the Securities and Exchange Commission hereby grants <strong>a DEALER'S REPRESENTATIVE LICENCE</strong> to:</p>

        <p class="ql-align-center"><strong>{{applicant_name}}</strong></p>

        <p class="ql-align-center">of <strong>{{principal_name}}</strong></p>

        <p class="ql-align-center">{{principal_business_address}}</p>

        <p class="ql-align-center">With effect from the <strong>2nd day of (May) 2025</strong>.</p>

        <p class="ql-align-right"><strong>BOARD CHAIRPERSON</strong></p>
        """,
        name: "Representative's License",
        description:
          "This is a template for the Representative's License issued by the SEC Zambia.",
        status: 1
      })

    {sec_ex, dealer, inv_adviser, rep, ros, dealer_certificate, rep_certificate}
  end
end
