<div class="px-4 sm:px-6 lg:px-8 mt-8 flow-root">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header  -->
    <div class="p-6 text-center rounded-md">
      <div class="flex items-center justify-between">
        <button
          onclick="window.history.back()"
          class="text-gray-600 hover:text-gray-800 focus:outline-none rounded-full p-3 border border-brand-10"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
        <h1 class="text-3xl font-bold tracking-tight text-gray-900 flex-1 text-center">
          Single SMS
        </h1>
        <div class="w-6"></div>
        <!-- Placeholder for alignment -->
      </div>
      <p class="mt-2 text-gray-600">
        Send a one-time SMS instantly to any recipient with delivery tracking and message customization
      </p>
    </div>
    <div class="flex justify-center items-center p-4">
      <div class="w-full max-w-2xl">
        <div class=" bg-white shadow-xl rounded-xl p-6 transition-all duration-300 hover:shadow-2xl">
          <.simple_form
            for={@form}
            page_title="Single SMS"
            id="fund-form"
            phx-change="validate"
            phx-submit="submit"
          >
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
              <.input
                field={@form[:sender_id]}
                type="select"
                prompt="--Select Sender--"
                options={@senders}
                required
                label={raw(~c"Sender <span class='text-rose-500'>*</span>")}
              />
              <.input
                field={@form[:mobile]}
                type="tel"
                phx-hook="validateNumberHook2"
                placeholder="Mobile: e.g 26097****"
                required
                label={raw(~c"Enter Recipient<span class='text-rose-500'>*</span>")}
              />
              <.input
                field={@form[:source]}
                type="text"
                placeholder="e.g Acc Department"
                label={raw(~c"Source")}
              />
            </div>
            <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
              <.input
                phx-hook="messageCounter"
                field={@form[:message]}
                type="textarea"
                label={raw(~c"Message <span class='text-red-500'>*</span>")}
                required
                wrapper_class="space-y-2"
                placeholder="Enter your message here"
                input_class="w-full p-3 border border-gray-300 rounded-lg min-h-[120px] resize-y"
              />
              <div class="text-sm text-gray-500 text-right" phx-update="ignore" id="kkkkk">
                <span class="count font-medium">0</span>
                <span class="text-gray-600"> message(s) • </span>
                <span class="remainder font-medium">160</span>
                <span class="text-gray-600"> characters remaining</span>
              </div>
            </div>
            <div class="flex justify-end space-x-4 pt-6">
              <%= if @loader do %>
                <.button
                  type="submit"
                  phx-disable-with="Sending..."
                  class="px-6 py-2.5 rounded-lg shadow-sm transition-all"
                >
                  Send SMS
                </.button>
              <% else %>
                <.button
                  type="button"
                  class="px-6 py-2.5 rounded-lg shadow-sm transition-all"
                  disabled
                >
                  Sending...
                </.button>
              <% end %>
            </div>
          </.simple_form>
        </div>
      </div>
    </div>
  </div>
</div>
