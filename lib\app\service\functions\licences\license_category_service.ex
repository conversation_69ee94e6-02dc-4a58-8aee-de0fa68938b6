defmodule App.Service.ServiceLicenseCategory.Functions do
  alias App.Licenses

  def index(socket, attrs, function \\ "change_status") do
    record = Licenses.get_license_category!(attrs["id"])

    cond do
      function == "change_status" ->
        Licenses.change_category_status(socket, attrs, record)
    end
  end

  def create(socket, attrs) do
    Licenses.create_license_category(socket, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end

  def update(socket, record, attrs) do
    Licenses.update_license_category(socket, record, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end
end
