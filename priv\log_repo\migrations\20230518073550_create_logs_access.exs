defmodule Logs.LogRepo.Migrations.CreateLogsAccess do
  use Ecto.Migration

  def change do
    create table(:logs_access) do
      add :session_id, :string
      add :route, :string, size: 300
      add :page, :string, size: 100
      add :page_access, :boolean, default: false, null: false
      add :description, :string, size: 300
      add :user_id, :integer

      timestamps()
    end

    create index(:logs_access, [:user_id])
  end
end
