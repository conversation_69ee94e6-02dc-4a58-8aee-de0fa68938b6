defmodule App.Licenses.Conditions do
  use Ecto.Schema
  import Ecto.Changeset

  schema "conditions" do
    field :name, :string
    field :description, :string
    field :type, :string
    field :status, :integer, default: 1
    belongs_to :user, App.Accounts.User

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(conditions, attrs) do
    conditions
    |> cast(attrs, [:name, :description, :status, :user_id, :type])
    |> validate_required([:name, :description, :type])
  end
end
