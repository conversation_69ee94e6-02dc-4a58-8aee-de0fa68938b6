defmodule App.Licenses.DynamicSchema do
  @moduledoc """
  Defines functions for working with dynamic schemas in license forms.
  """

  @doc """
  Creates a schema definition based on field definitions.

  This function converts the field definitions from the database into a structured
  schema that can be used for validation and form rendering.

  ## Parameters

  - `fields` - A list of maps containing field definitions

  ## Returns

  A structured schema map
  """
  def create_schema(fields) do
    fields
    |> Enum.map(&format_field/1)
    |> Enum.group_by(& &1.step)
  end

  @doc """
  Formats and normalizes a field definition from the database into a consistent structure.
  """
  def format_field(field) do
    %{
      field_name: field.field_name,
      field_label: field.field_label,
      field_type: field.field_type,
      field_options: parse_options(field.field_type, field.field_validations),
      description: Map.get(field, :field_description) || Map.get(field, :description, ""),
      # Default to required
      required: Map.get(field, :required, true),
      step: determine_step(field),
      dependent: extract_dependency(field)
    }
  end

  # Determine which step this field belongs to
  defp determine_step(field) do
    case field.field_type do
      "upload" -> 1
      _ -> 0
    end
  end

  # Parse field options based on field type
  defp parse_options("number", options) when is_map(options) do
    %{
      "min" => Map.get(options, "min"),
      "max" => Map.get(options, "max")
    }
  end

  defp parse_options("text", options) when is_map(options) do
    %{
      "min_length" => Map.get(options, "min_length"),
      "max_length" => Map.get(options, "max_length", 255),
      "pattern" => Map.get(options, "pattern")
    }
  end

  defp parse_options("textarea", options) when is_map(options) do
    %{
      "min_length" => Map.get(options, "min_length"),
      "max_length" => Map.get(options, "max_length", 1000)
    }
  end

  defp parse_options("radio", options) when is_list(options) do
    options
  end

  defp parse_options("checkbox_group", options) when is_list(options) do
    options
  end

  defp parse_options(_type, _options), do: nil

  # Extract dependency information if present
  defp extract_dependency(%{
         field_dependents: field_dependents,
         dependent_selection: dependent_selection
       })
       when is_list(field_dependents) and is_binary(dependent_selection) and
              length(field_dependents) > 0 do
    # For simplicity, just take the first dependent
    [first_dependent | _] = field_dependents

    %{
      field_name: first_dependent,
      value: dependent_selection
    }
  end

  defp extract_dependency(_field), do: nil

  @doc """
  Gets fields for a specific step from the schema.
  """
  def get_fields_for_step(schema, step) do
    Map.get(schema, step, [])
  end

  @doc """
  Determines if a field should be shown based on dependencies.
  """
  def should_show_field?(field, form_data) do
    # Safely handle nil field_dependents or map without field_dependents key
    field_dependents = Map.get(field, :field_dependents)

    # If no dependencies, always show the field
    if field_dependents == nil || !is_list(field_dependents) || Enum.empty?(field_dependents) do
      true
    else
      # Get the first dependent field name (safely)
      dependent_field_name = List.first(field_dependents)

      dependent_value = Map.get(field, :dependent_selection)

      if is_binary(dependent_field_name) && is_binary(dependent_value) do
        # Try to get form value using both string and atom keys
        form_value =
          Map.get(form_data, dependent_field_name) ||
            Map.get(form_data, String.to_atom(dependent_field_name))

        # Show field if the dependent field has the expected value
        form_value == dependent_value
      else
        # Default to showing the field if dependency info is invalid
        true
      end
    end
  end
end
