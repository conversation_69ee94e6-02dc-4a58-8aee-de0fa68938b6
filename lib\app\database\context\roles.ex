defmodule App.Roles do
  import Ecto.Query, warn: false
  alias App.Repo

  alias App.Roles.{
    Permission,
    DepartmentRoles,
    DepartmentRolePermission,
    AccessRoles,
    AccessRolePermission
  }

  # Permissions
  def list_permissions do
    Repo.all(Permission)
  end

  def get_department_roles_query,
    do: where(DepartmentRoles, [a], a.user_interface == ^true)

  def get_access_roles_query,
    do: where(AccessRoles, [a], a.user_interface == ^true)

  def get_access_roles_query(id),
    do: where(AccessRoles, [a], a.user_interface == ^true and a.department_role_id == ^id)

  def get_department_id_by_access_role_id(id) do
    where(AccessRoles, [a], a.id == ^id)
    |> select([a], a.department_role_id)
    |> Repo.one()
  end

  def get_role_name_by_id!(id) do
    AccessRoles
    |> where([a], a.id == ^id)
    |> select([a], a.name)
    |> limit(1)
    |> Repo.one()
  end

  def get_permission!(id), do: Repo.get!(Permission, id)

  def get_permission_by_code(code) do
    Repo.get_by(Permission, code: code)
  end

  def create_permission(attrs \\ %{}) do
    %Permission{}
    |> Permission.changeset(attrs)
    |> Repo.insert()
  end

  def update_permission(%Permission{} = permission, attrs) do
    permission
    |> Permission.changeset(attrs)
    |> Repo.update()
  end

  def delete_permission(%Permission{} = permission) do
    Repo.delete(permission)
  end

  # Department Roles
  def list_department_roles do
    Repo.all(
      from dr in DepartmentRoles,
        where: is_nil(dr.deleted_at),
        preload: [:permissions]
    )
  end

  def get_department_role!(id) do
    Repo.get!(DepartmentRoles, id)
    |> Repo.preload([:permissions, :access_roles])
  end

  def create_department_role(attrs \\ %{}) do
    %DepartmentRoles{}
    |> DepartmentRoles.changeset(attrs)
    |> Repo.insert()
  end

  def update_department_role(%DepartmentRoles{} = department_role, attrs) do
    department_role
    |> DepartmentRoles.changeset(attrs)
    |> Repo.update()
  end

  def update_department_role_permissions(%DepartmentRoles{} = department_role, permission_ids) do
    # Remove existing permissions
    Repo.delete_all(
      from drp in DepartmentRolePermission, where: drp.department_role_id == ^department_role.id
    )

    # Add new permissions
    Enum.each(
      permission_ids,
      fn permission_id ->
        %DepartmentRolePermission{}
        |> DepartmentRolePermission.changeset(%{
          department_role_id: department_role.id,
          permission_id: permission_id
        })
        |> Repo.insert()
      end
    )

    get_department_role!(department_role.id)
  end

  # Access Roles
  def list_access_roles do
    Repo.all(
      from ar in AccessRoles,
        where: is_nil(ar.deleted_at),
        preload: [:permissions, :department_role]
    )
  end

  def list_access_roles_by_department(department_role_id) do
    Repo.all(
      from ar in AccessRoles,
        where: ar.department_role_id == ^department_role_id and is_nil(ar.deleted_at),
        preload: [:permissions]
    )
  end

  def get_access_role!(id) do
    Repo.get!(AccessRoles, id)
    |> Repo.preload([:permissions, :department_role])
  end

  def create_access_role(attrs \\ %{}) do
    %AccessRoles{}
    |> AccessRoles.changeset(attrs)
    |> Repo.insert()
  end

  def update_access_role(%AccessRoles{} = access_role, attrs) do
    access_role
    |> AccessRoles.changeset(attrs)
    |> Repo.update()
  end

  def update_access_role_permissions(%AccessRoles{} = access_role, permission_ids) do
    # Get department role permissions to validate
    department_role = Repo.preload(access_role, department_role: :permissions).department_role

    allowed_permission_ids = Enum.map(department_role.permissions, & &1.id)

    # Filter only allowed permissions
    valid_permission_ids = Enum.filter(permission_ids, &(&1 in allowed_permission_ids))

    # Remove existing permissions
    Repo.delete_all(
      from arp in AccessRolePermission, where: arp.access_role_id == ^access_role.id
    )

    # Add new permissions
    Enum.each(
      valid_permission_ids,
      fn permission_id ->
        %AccessRolePermission{}
        |> AccessRolePermission.changeset(%{
          access_role_id: access_role.id,
          permission_id: permission_id
        })
        |> Repo.insert()
      end
    )

    get_access_role!(access_role.id)
  end

  def get_available_permissions_for_access_role(access_role_id) do
    access_role = get_access_role!(access_role_id)
    department_role = Repo.preload(access_role.department_role, :permissions)
    department_role.permissions
  end

  alias App.Roles.UserPermission

  # Get access role with all permissions preloaded
  def get_access_role_with_permissions(id) do
    AccessRoles
    |> where([a], a.id == ^id)
    |> preload([:department_role, :permissions])
    |> Repo.one()
  end

  # Get all permissions for a user (role + user-specific)
  def get_user_permissions(user_id, role_id) do
    # Get role permissions
    role_permissions =
      case get_access_role_with_permissions(role_id) do
        nil -> []
        role -> role.permissions
      end

    # Get user-specific permissions (excluding expired ones)
    user_permissions =
      Permission
      |> join(:inner, [p], up in UserPermission, on: up.permission_id == p.id)
      |> where([p, up], up.user_id == ^user_id)
      |> where([p, up], is_nil(up.expires_at) or up.expires_at > ^NaiveDateTime.utc_now())
      |> Repo.all()

    # Combine and deduplicate
    (role_permissions ++ user_permissions)
    |> Enum.uniq_by(& &1.id)
  end

  # Get only user-specific permissions
  def get_user_specific_permissions(user_id) do
    UserPermission
    |> where([up], up.user_id == ^user_id)
    |> where([up], is_nil(up.expires_at) or up.expires_at > ^NaiveDateTime.utc_now())
    |> preload([:permission, :granted_by_user])
    |> Repo.all()
  end

  # Grant permission to user
  def grant_user_permission(attrs \\ %{}) do
    %UserPermission{}
    |> UserPermission.changeset(attrs)
    |> Repo.insert()
  end

  # Revoke permission from user
  def revoke_user_permission(user_id, permission_id) do
    UserPermission
    |> where([up], up.user_id == ^user_id and up.permission_id == ^permission_id)
    |> Repo.delete_all()
  end

  # Get available permissions for user (department permissions not already assigned)
  def get_available_permissions_for_user(user_id, role_id) do
    # Get user's role and department
    role = get_access_role_with_permissions(role_id)

    if is_nil(role) do
      []
    else
      department_role = Repo.preload(role.department_role, :permissions)

      department_permission_ids = Enum.map(department_role.permissions, & &1.id)

      # Get current user permissions (role + user-specific)
      current_permission_ids =
        get_user_permissions(user_id, role_id)
        |> Enum.map(& &1.id)

      # Get permissions that are in department but not already assigned to user
      Permission
      |> where([p], p.id in ^department_permission_ids)
      |> where([p], p.id not in ^current_permission_ids)
      |> Repo.all()
    end
  end

  # Check if user has specific permission
  def user_has_permission?(user_id, role_id, permission_code) do
    permissions = get_user_permissions(user_id, role_id)
    Enum.any?(permissions, &(&1.code == permission_code))
  end

  # Bulk update user permissions
  def update_user_permissions(user_id, permission_ids, granted_by_id) do
    # Get current user-specific permissions
    current_user_permissions = get_user_specific_permissions(user_id)
    current_permission_ids = Enum.map(current_user_permissions, & &1.permission_id)

    # Determine what to add and remove
    to_add = permission_ids -- current_permission_ids
    to_remove = current_permission_ids -- permission_ids

    # Remove permissions
    if length(to_remove) > 0 do
      UserPermission
      |> where([up], up.user_id == ^user_id and up.permission_id in ^to_remove)
      |> Repo.delete_all()
    end

    # Add new permissions
    Enum.each(
      to_add,
      fn permission_id ->
        grant_user_permission(%{
          user_id: user_id,
          permission_id: permission_id,
          granted_by: granted_by_id
        })
      end
    )

    :ok
  end

  def select_access_role_from_department_by(id) do
    case id do
      "" ->
        []

      _ ->
        AccessRoles
        |> where([a], a.department_role_id == ^id)
        |> select([a], {a.name, a.id})
        |> Repo.all()
    end
  end

  def get_access_role_by_department(id, name) do
    AccessRoles
    |> where([a], a.department_role_id == ^id and a.name == ^name)
    |> select([a], a.id)
    |> limit(1)
    |> Repo.one()
  end

  def select_department_role_access_role(id) do
    case id do
      "" ->
        []

      _ ->
        role = get_access_roles!(id)
        {select_access_role_from_department_by(role.department_role_id), role.department_role_id}
    end
  end

  def get_access_roles!(id), do: Repo.get!(AccessRoles, id)

  def select_department_role do
    DepartmentRoles
    |> where([a], a.id not in [1, 8])
    |> select([a], {a.name, a.id})
    |> Repo.all()
  end

  def get_license_roles() do
    AccessRoles
    |> where([a], a.status == 1 and a.department_role_id == 8)
    |> select([a], {a.name, a.id})
    |> Repo.all()
  end

  def get_access_role_by_department(id, name) do
    AccessRoles
    |> join(:left, [a], b in assoc(a, :department_role))
    |> where([a, b], b.id == ^id and a.name == ^name)
    |> select([a], a.id)
    |> limit(1)
    |> Repo.one()
  end
end

# defmodule App.Roles do
#  @moduledoc """
#  The Roles context.
#  """
#
#  # App.Roles.get_role_id()
#
#  @topic "AccessRoles:"
#  @topic1 "SystemRoles:"
#
#  import Ecto.Query, warn: false
#
#  def subscribe1, do: Phoenix.PubSub.subscribe(App.PubSub, @topic1)
#
#  import Ecto.Query, warn: false
#  alias App.Roles.{AccessRoles, DepartmentRoles, Permission}
#  alias App.{CustomContext, Repo}
#  alias Logs.Audit
#
#  @topic2 "access_roles"
#  def subscribe2, do: Phoenix.PubSub.subscribe(App.PubSub, @topic2)
#
#  def subscribe2(id), do: Phoenix.PubSub.subscribe(App.PubSub, @topic2 <> to_string(id))
#
#  @doc """
#  Returns the list of department_roles.
#
#  ## Examples
#
#      iex> list_department_roles()
#      [%DepartmentRoles{}, ...]
#
#  """
#
#  def list_department_roles do
#    Repo.all(DepartmentRoles)
#  end
#

#
#  def select_all_system_roles do
#    DepartmentRoles
#    |> select([a], {a.name, a.id})
#    |> Repo.all()
#  end
#
#  @doc """
#  Gets a single department_roles.
#
#  Raises `Ecto.NoResultsError` if the Department roles does not exist.
#
#  ## Examples
#
#      iex> get_department_roles!(123)
#      %DepartmentRoles{}
#
#      iex> get_department_roles!(456)
#      ** (Ecto.NoResultsError)
#
#  """
#
#  def permissions() do
#    select(Permission, [m], %{s: m.service, tab: m.tab})
#    |> Repo.all()
#    |> Enum.group_by(fn m -> m.tab end)
#    |> Enum.sort_by(&len/1, :desc)
#  end
#
#  def select_access_role do
#    AccessRoles
#    |> join(:left, [a], b in assoc(a, :role))
#    |> where([a, b], b.department_id not in [1, 3, 4, 5])
#    |> select([a], {a.name, a.id})
#    |> Repo.all()
#  end
#
#  def get_role_id() do
#    Repo.all(from m in AccessRoles, where: m.name == "Client", select: {m.name, m.id})
#  end
#

#
#  def select_department_role do
#    DepartmentRoles
#    |> where([a], a.id not in [1, 8])
#    |> select([a], {a.name, a.id})
#    |> Repo.all()
#  end
#
#  def select_all_department_role do
#    DepartmentRoles
#    |> select([a], {a.name, a.id})
#    |> Repo.all()
#  end
#
#  def select_merchant_department_role do
#    DepartmentRoles
#    |> where([a], a.id in [4])
#    |> select([a], {a.name, a.id})
#    |> Repo.all()
#  end
#
#  def select_access_role(id),
#    do:
#      Repo.all(
#        from(m in AccessRoles,
#          where: m.status == 1 and m.department_id == ^id,
#          select: {m.name, m.id}
#        )
#      )
#
#  defp len({_a, b}), do: length(b)
#
#  def get_access_roles_by_id(id), do: Repo.get(AccessRoles, id)
#  def get_access_roles_by_id?(id), do: Repo.get!(AccessRoles, id)
#
#  def create_department_roles(socket, attrs) do
#    Ecto.Multi.new()
#    |> Ecto.Multi.insert(:role, DepartmentRoles.changeset(%DepartmentRoles{}, attrs))
#    |> Audit.create_system_log_session_live_multi(
#      socket,
#      "Created Role with [#{attrs["name"]}]",
#      "CREATE",
#      attrs,
#      "Roles and Permission"
#    )
#    |> Repo.transaction()
#  end
#
#  def create_department_rolesggg(_socket, attrs \\ %{}, after_save \\ &{:ok, &1}) do
#    Ecto.Multi.insert(
#      Ecto.Multi.new(),
#      :role,
#      DepartmentRoles.changeset(%DepartmentRoles{}, attrs)
#    )
#    # |> Audit.create_system_log_session_live_multi(
#    #      socket, "CreateRole with [#{attrs["name"]}]",
#    #      "CREATE", attrs, "Roles and Permission")
#    |> Repo.transaction()
#    |> CustomContext.after_save(after_save)
#  end
#
#  def update_department_roless(_conn, attrs) do
#    get_role_id = Repo.get!(DepartmentRoles, attrs["id"])
#
#    Ecto.Multi.new()
#    |> Ecto.Multi.update(:get_role_id, DepartmentRoles.changeset(get_role_id, attrs))
#    |> Repo.transaction()
#  end
#
#  def update_jackpot_multi(multi, attrs) do
#    department = Repo.get!(DepartmentRoles, attrs["id"])
#    Ecto.Multi.update(multi, :department_roles, DepartmentRoles.changeset(department, attrs))
#  end
#
#  def update_department_roles(socket, %DepartmentRoles{} = department_roles, attrs) do
#    Ecto.Multi.update(
#      Ecto.Multi.new(),
#      :department_roles,
#      DepartmentRoles.changeset(department_roles, attrs)
#    )
#    |> Audit.create_system_log_session_live_multi(
#      socket,
#      "Updated Department with id: #{department_roles.id}",
#      "UPDATE",
#      attrs,
#      "Roles and Permission"
#    )
#    |> Repo.transaction()
#  end
#
#  def multi_department_role_status(department_roles, status, current_user),
#    do:
#      DepartmentRoles.update_department_role_changeset(
#        department_roles,
#        %{"status" => status},
#        current_user
#      )
#
#  def disable_department_role_for_maker_checker(multi, record, current_user) do
#    Ecto.Multi.update(
#      multi,
#      :department_roles,
#      multi_department_role_status(record, 4, current_user)
#    )
#    |> Repo.transaction()
#    |> CustomContext.notify_subs(:updated, @topic, __MODULE__)
#  end
#
#  def unblock_department_role(_attrs, department_roles) do
#    Ecto.Multi.new()
#    |> Ecto.Multi.update(
#      :update,
#      DepartmentRoles.changeset(department_roles, %{"blocked" => false, "failed_attempts" => 0})
#    )
#    |> Repo.transaction()
#    |> CustomContext.notify_subs(:updated, @topic, __MODULE__)
#  end
#
#  def change_department_role_status(socket, attrs, status, department_roles, current_user) do
#    new_status = if status == "1", do: "Enable", else: "Disable"
#
#    Ecto.Multi.new()
#    |> Ecto.Multi.update(
#      :update,
#      multi_department_role_status(department_roles, status, current_user)
#    )
#    |> Audit.create_system_log_session_live_multi(
#      socket,
#      "#{new_status}d Department Role by [#{department_roles.name}]",
#      "UPDATE",
#      attrs,
#      "Roles and Permission"
#    )
#    |> Repo.transaction()
#    |> CustomContext.notify_subs(:updated, @topic, __MODULE__)
#  end
#
#  #
#  def get_department_roles!(id), do: Repo.get!(DepartmentRoles, id)
#
#  def get_department_roles(id), do: Repo.get(DepartmentRoles, id)
#
#  def get_department_role_name_by_id_with_permissions(id) do
#    data = get_department_roles!(id)
#
#    if data == nil,
#      do: %{permissions: []},
#      else: Map.merge(data, %{permissions: data.permissions})
#  end
#
#  @doc """
#  Creates a department_roles.
#
#  ## Examples
#
#      iex> create_department_roles(%{field: value})
#      {:ok, %DepartmentRoles{}}
#
#      iex> create_department_roles(%{field: bad_value})
#      {:error, %Ecto.Changeset{}}
#
#  """
#  def create_department_roless(_socket, attrs \\ %{}) do
#    %DepartmentRoles{}
#    |> DepartmentRoles.changeset(attrs)
#    |> Repo.insert()
#  end
#
#  @doc """
#  Updates a department_roles.
#
#  ## Examples
#
#      iex> update_department_roles(department_roles, %{field: new_value})
#      {:ok, %DepartmentRoles{}}
#
#      iex> update_department_roles(department_roles, %{field: bad_value})
#      {:error, %Ecto.Changeset{}}
#
#  """
#
#  def update_department_roles_permission(permissions, id, multiple \\ Ecto.Multi.new()) do
#    multiple
#    |> Ecto.Multi.run(:role, fn _repo, _ ->
#      {:ok, get_department_roles!(id)}
#    end)
#    |> Ecto.Multi.update(:update, fn %{role: role} ->
#      DepartmentRoles.changeset(role, %{permissions: permissions})
#    end)
#    |> Repo.transaction()
#  end
#
#  @doc """
#  Deletes a department_roles.
#
#  ## Examples
#
#      iex> delete_department_roles(department_roles)
#      {:ok, %DepartmentRoles{}}
#
#      iex> delete_department_roles(department_roles)
#      {:error, %Ecto.Changeset{}}
#
#  """
#  def delete_department_roles(%DepartmentRoles{} = department_roles) do
#    Repo.delete(department_roles)
#  end
#
#  @doc """
#  Returns an `%Ecto.Changeset{}` for tracking department_roles changes.
#
#  ## Examples
#
#      iex> change_department_roles(department_roles)
#      %Ecto.Changeset{data: %DepartmentRoles{}}
#
#  """
#  def change_department_roles(%DepartmentRoles{} = department_roles, attrs \\ %{}) do
#    DepartmentRoles.changeset(department_roles, attrs)
#  end
#
#  @doc """
#  Returns the list of access_roles.
#
#  ## Examples
#
#      iex> list_access_roles()
#      [%AccessRoles{}, ...]
#
#  """
#  def list_access_roles do
#    Repo.all(AccessRoles)
#  end
#
#  # def select_access_role(id),
#  #   do:
#  #     Repo.all(
#  #       from m in AccessRoles,
#  #         where: m.status == 1 and m.department_id == ^id,
#  #         select: {m.name, m.id}
#  #     )
#
#  def select_access_role_from_department_by(id) do
#    case id do
#      "" ->
#        []
#
#      _ ->
#        AccessRoles
#        |> where([a], a.department_id == ^id)
#        |> select([a], {a.name, a.id})
#        |> Repo.all()
#    end
#  end
#
#  #     )
#
#  def select_department_role_access_role(id) do
#    case id do
#      "" ->
#        []
#
#      _ ->
#        role = get_access_roles!(id)
#        {select_access_role_from_department_by(role.department_id), role.department_id}
#    end
#  end
#
#
#  #
#  @doc """
#  Gets a single access_roles.
#
#  Raises `Ecto.NoResultsError` if the Access roles does not exist.
#
#  ## Examples
#
#      iex> get_access_roles!(123)
#      %AccessRoles{}
#
#      iex> get_access_roles!(456)
#      ** (Ecto.NoResultsError)
#
#  """
#  def get_access_roles!(id), do: Repo.get!(AccessRoles, id)
#  def get_access_roles(id), do: Repo.get(AccessRoles, id)
#
#  def get_access_roles_query(id),
#    do: where(AccessRoles, [a], a.user_interface == ^true and a.department_id == ^id)
#
#  def get_access_roles_permission_by_id(id) do
#    data = get_access_roles(id)
#
#    if data == nil,
#      do: [],
#      else: data.permissions
#  end
#
#  def get_access_roles_name_by_id(id) do
#    data = get_access_roles(id)
#
#    if data == nil,
#      do: "",
#      else: data.name
#  end
#
# def get_access_roles_by_id_preload(id) do
#                                       AccessRoles
#                                       |> where([a], a.id == ^id)
#                                                      |> preload(:department)
#                                                                 |> Repo.one()
#                                       end
#
#  def change_access_status(socket, access_role, attrs, multiple \\ Ecto.Multi.new()) do
#    Ecto.Multi.update(multiple, :update, AccessRoles.update_status_changeset(access_role, attrs))
#    |> Audit.create_system_log_session_live_multi(
#      socket,
#      "#{if attrs["status"] == "0",
#        do: "Disabled",
#        else: "Enabled"} Access Role [#{access_role.name}]",
#      "UPDATE",
#      attrs,
#      "Access Role Maintenance"
#    )
#    |> Repo.transaction()
#    |> CustomContext.notify_subs(@topic2, :updated)
#  end
#
#  def get_access_roles_by_department_access_role_id_preload(id) do
#    DepartmentRoles
#    |> join(:left, [a], b in assoc(a, :role))
#    |> where([_, b], b.department_id == ^id)
#    |> select([_, b], {b.name, b.id})
#    |> Repo.all()
#  end
#
#  def get_department_role_name_by_access_role_id(id) do
#    DepartmentRoles
#    |> join(:left, [a], b in assoc(a, :role))
#    |> where([_, b], b.id == ^id)
#    |> select([a], a.name)
#    |> limit(1)
#    |> Repo.one()
#  end
#
#  def select_access_role_from_department_role_by_id(id) do
#    AccessRoles
#    |> join(:left, [a], b in assoc(a, :department))
#    |> where([a, b], b.id == ^id)
#    |> select([a], {a.name, a.id})
#    |> Repo.all()
#  end
#
#  def get_system_role_name_by_id_with_permissions(id) do
#    data = get_department_roles(id)
#
#    if data == nil,
#      do: %{permissions: []},
#      else: Map.merge(data, %{permissions: data.permissions})
#  end
#
#  def get_access_role_permissions(role),
#    do: get_system_role_name_by_id_with_permissions(role.department_id).permissions
#
#  def get_access_role_name_by_id_with_permissions(data) do
#    if data == nil do
#      []
#    else
#      Map.merge(data, %{permissions: data.permissions})
#    end
#  end
#
#  def register_access_role_multi(multi, attrs) do
#    Ecto.Multi.insert(multi, :document, AccessRoles.changeset(%AccessRoles{}, attrs))
#  end
#
#  def register_access_role(socket, attrs) do
#    register_access_role_multi(Ecto.Multi.new(), attrs)
#    |> Audit.create_system_log_session_live_multi(
#      socket,
#      "Create Access Role [#{attrs["name"]}]",
#      "CREATE",
#      attrs,
#      "Access Role Maintenance"
#    )
#    |> Repo.transaction()
#    |> CustomContext.notify_subs(@topic2, :updated)
#  end
#
#  @doc """
#  Creates a access_roles.
#
#  ## Examples
#
#      iex> create_access_roles(%{field: value})
#      {:ok, %AccessRoles{}}
#
#      iex> create_access_roles(%{field: bad_value})
#      {:error, %Ecto.Changeset{}}
#
#  """
#  def create_access_roles(attrs \\ %{}) do
#    %AccessRoles{}
#    |> AccessRoles.changeset(attrs)
#    |> Repo.insert()
#  end
#
#  @doc """
#  Updates a access_roles.
#
#  ## Examples
#
#      iex> update_access_roles(access_roles, %{field: new_value})
#      {:ok, %AccessRoles{}}
#
#      iex> update_access_roles(access_roles, %{field: bad_value})
#      {:error, %Ecto.Changeset{}}
#
#  """
#  def update_access_roles(%AccessRoles{} = access_roles, attrs) do
#    access_roles
#    |> AccessRoles.changeset(attrs)
#    |> Repo.update()
#  end
#
#  @doc """
#  Deletes a access_roles.
#
#  ## Examples
#
#      iex> delete_access_roles(access_roles)
#      {:ok, %AccessRoles{}}
#
#      iex> delete_access_roles(access_roles)
#      {:error, %Ecto.Changeset{}}
#
#  """
#  def delete_access_roles(%AccessRoles{} = access_roles) do
#    Repo.delete(access_roles)
#  end
#
#  @doc """
#  Returns an `%Ecto.Changeset{}` for tracking access_roles changes.
#
#  ## Examples
#
#      iex> change_access_roles(access_roles)
#      %Ecto.Changeset{data: %AccessRoles{}}
#
#  """
#  def change_access_roles(%AccessRoles{} = access_roles, attrs \\ %{}) do
#    AccessRoles.changeset(access_roles, attrs)
#  end
#
#  @doc """
#  Returns the list of permissions.
#
#  ## Examples
#
#      iex> list_permissions()
#      [%Permission{}, ...]
#
#  """
#  def list_permissions do
#    Repo.all(Permission)
#  end
#
#  def get_all_permissions do
#    Permission
#    |> select([a], %{role: a.service, tab: a.tab})
#    |> Repo.all()
#  end
#
#  def get_filter_permissions(query),
#    do:
#      Permission
#      |> where([a], like(a.service, ^"%#{query}%"))
#      |> select([a], %{role: a.service, tab: a.tab})
#      |> Repo.all()
#
#  @doc """
#  Gets a single permission.
#
#  Raises `Ecto.NoResultsError` if the Permission does not exist.
#
#  ## Examples
#
#      iex> get_permission!(123)
#      %Permission{}
#
#      iex> get_permission!(456)
#      ** (Ecto.NoResultsError)
#
#  """
#  def get_permission!(id), do: Repo.get!(Permission, id)
#
#  @doc """
#  Creates a permission.
#
#  ## Examples
#
#      iex> create_permission(%{field: value})
#      {:ok, %Permission{}}
#
#      iex> create_permission(%{field: bad_value})
#      {:error, %Ecto.Changeset{}}
#
#  """
#  def create_permission(attrs \\ %{}) do
#    %Permission{}
#    |> Permission.changeset(attrs)
#    |> Repo.insert()
#  end
#
#  def update_access_roles_permission(permissions, id, user, multiple \\ Ecto.Multi.new()) do
#    multiple
#    |> Ecto.Multi.run(:role, fn _repo, _ ->
#      {:ok, get_access_roles_by_id(id)}
#    end)
#    |> Ecto.Multi.update(:update, fn %{role: role} ->
#      AccessRoles.update_permission_changeset(
#        role,
#        %{permissions: permissions},
#        user
#      )
#    end)
#    |> Repo.transaction()
#    |> CustomContext.notify_subs(@topic2, :updated, __MODULE__)
#    |> notify_subs_with_role(@topic2, :updated)
#  end
#
#  def update_access_role(socket, role, attrs, multiple \\ Ecto.Multi.new()) do
#    role = get_access_roles_by_id(role.id)
#
#    Ecto.Multi.update(multiple, :update, AccessRoles.update_role_changeset(role, attrs))
#    |> Audit.create_system_log_session_live_multi(
#      socket,
#      "Update Access Role [#{role.name}]",
#      "UPDATE",
#      attrs,
#      "Access Role Maintenance"
#    )
#    |> Repo.transaction()
#    |> CustomContext.notify_subs(@topic2, :updated)
#  end
#
#  @doc """
#  Updates a permission.
#
#  ## Examples
#
#      iex> update_permission(permission, %{field: new_value})
#      {:ok, %Permission{}}
#
#      iex> update_permission(permission, %{field: bad_value})
#      {:error, %Ecto.Changeset{}}
#
#  """
#  def update_permission(%Permission{} = permission, attrs) do
#    permission
#    |> Permission.changeset(attrs)
#    |> Repo.update()
#  end
#
#  @doc """
#  Deletes a permission.
#
#  ## Examples
#
#      iex> delete_permission(permission)
#      {:ok, %Permission{}}
#
#      iex> delete_permission(permission)
#      {:error, %Ecto.Changeset{}}
#
#  """
#  def delete_permission(%Permission{} = permission) do
#    Repo.delete(permission)
#  end
#
#  @doc """
#  Returns an `%Ecto.Changeset{}` for tracking permission changes.
#
#  ## Examples
#
#      iex> change_permission(permission)
#      %Ecto.Changeset{data: %Permission{}}
#
#  """
#  def change_permission(%Permission{} = permission, attrs \\ %{}) do
#    Permission.changeset(permission, attrs)
#  end
#
#  defp notify_subs_with_role({:ok, %{update: update} = result}, topic, event) do
#    Phoenix.PubSub.broadcast(
#      App.PubSub,
#      topic <> to_string(update.id),
#      {__MODULE__, event, update}
#    )
#
#    {:ok, result}
#  end
#
#  defp notify_subs_with_role({:error, result}, _, _event), do: {:error, result}
#
#  def notify_subs_with_user_role_change({:ok, result}, new_id, user, event) do
#    new_role = get_access_roles(new_id)
#
#    Phoenix.PubSub.broadcast(
#      App.PubSub,
#      @topic2 <> "change_user" <> to_string(user.id),
#      {__MODULE__, event, new_role}
#    )
#
#    {:ok, result}
#  end
#
#  def get_system_role_from_access_role_id(access_role_id) do
#    AccessRoles
#    |> where([a], a.id == ^access_role_id)
#    |> select([a], a.department_id)
#    |> limit(1)
#    |> Repo.one()
#  end
#
#  def get_from_access_role_id(access_role_id) do
#    AccessRoles
#    |> where([a], a.id == ^access_role_id)
#    |> select([a], a.name)
#    |> limit(1)
#    |> Repo.one()
#  end
#
#  def get_permission_by_service(service) do
#    Permission
#    |> where([a], a.service == ^service)
#    |> limit(1)
#    |> Repo.one()
#  end
#
#  def notify_subs_with_user_role_change({:error, result}, _, _event), do: {:error, result}
# end
