defmodule App.SetUp.Permissions do
  @moduledoc """
  Seeds permissions data with proper service grouping and code fields
  """
  alias App.Roles

  def init do
    permissions = [
      # Committee Management
      %{
        service: "Committee Management",
        tab: "Maintenance",
        description: "View committee maintenance",
        code: "committee-view"
      },
      %{
        service: "Committee Management",
        tab: "Maintenance",
        description: "Create a committee",
        code: "committee-create"
      },
      %{
        service: "Committee Management",
        tab: "Maintenance",
        description: "Edit a committee",
        code: "committee-edit"
      },

      # Admin Email Management
      %{
        service: "Admin Email Management",
        tab: "Maintenance",
        description: "View admin emails",
        code: "admin_emails_view"
      },
      %{
        service: "Admin Email Management",
        tab: "Maintenance",
        description: "Create admin emails",
        code: "admin_emails_create"
      },
      %{
        service: "Admin Email Management",
        tab: "Maintenance",
        description: "Edit admin emails",
        code: "admin_emails_edit"
      },

      # Dashboard
      %{
        service: "Dashboard",
        tab: "Dashboard",
        description: "View payments by payment provider",
        code: "view_payments_by_payment_provider"
      },
      %{
        service: "Dashboard",
        tab: "Dashboard",
        description: "View pending applications count",
        code: "dashboard-pending_applications"
      },
      %{
        service: "Dashboard",
        tab: "Dashboard",
        description: "View total dealers count",
        code: "dashboard-total_dealers"
      },
      %{
        service: "Dashboard",
        tab: "Dashboard",
        description: "View total clients count",
        code: "dashboard-total_clients"
      },
      %{
        service: "Dashboard",
        tab: "Dashboard",
        description: "View total licenses count",
        code: "dashboard-total_licenses"
      },
      %{
        service: "Dashboard",
        tab: "Dashboard",
        description: "View yesterday's traffic trends",
        code: "dashboard-traffic_trends"
      },
      %{
        service: "Dashboard",
        tab: "Dashboard",
        description: "View server resources statistics",
        code: "dashboard-server_stats"
      },
      %{
        service: "Dashboard",
        tab: "Dashboard",
        description: "Track addresses availability",
        code: "dashboard-health_checker"
      },
      %{
        service: "Dashboard",
        tab: "Dashboard",
        description: "Access license registration for clients",
        code: "dashboard-license_registration"
      },

      # User Management
      %{
        service: "User Management",
        tab: "Users Maintenance",
        description: "View users",
        code: "user_view"
      },
      %{
        service: "User Management",
        tab: "Users Maintenance",
        description: "Create new users",
        code: "user_create"
      },
      %{
        service: "User Management",
        tab: "Users Maintenance",
        description: "Edit user details",
        code: "user_edit"
      },
      %{
        service: "User Management",
        tab: "Users Maintenance",
        description: "Enable/disable users",
        code: "user_status"
      },

      # Client User Management
      %{
        service: "Client User Management",
        tab: "Users Maintenance",
        description: "View client users",
        code: "client_user_view"
      },
      %{
        service: "Client User Management",
        tab: "Users Maintenance",
        description: "Create client users",
        code: "client_user_create"
      },
      %{
        service: "Client User Management",
        tab: "Users Maintenance",
        description: "Edit client users",
        code: "client_user_edit"
      },
      %{
        service: "Client User Management",
        tab: "Users Maintenance",
        description: "Enable/disable client users",
        code: "client_user_status"
      },

      # Sender Management
      %{
        service: "Sender Management",
        tab: "Senders Maintenance",
        description: "View senders",
        code: "senders-view"
      },
      %{
        service: "Sender Management",
        tab: "Senders Maintenance",
        description: "Create senders",
        code: "sender-create"
      },
      %{
        service: "Sender Management",
        tab: "Senders Maintenance",
        description: "Edit senders",
        code: "sender-edit"
      },
      %{
        service: "Sender Management",
        tab: "Sender Maintenance",
        description: "Enable/disable senders",
        code: "sender-status"
      },

      # License Applications
      %{
        service: "License Applications",
        tab: "License Applications",
        description: "View client applications",
        code: "client_applications-view"
      },


      %{
        service: "License Applications",
        tab: "Applications",
        description: "View finance view of license applications",
        code: "finance-view"
      },
      %{
        service: "License Applications",
        tab: "Applications",
        description: "View detailed license applications",
        code: "license_details-view"
      },
      %{
        service: "New License Applications",
        tab: "Applications",
        description: "View pending license applications",
        code: "applications-new_license-view"
      },
      %{
        service: "Submitted to Market Operations",
        tab: "Applications",
        description: "View pending license applications",
        code: "applications-submit_market_operations-view"
      },
      %{
        service: "Returned to Applicant",
        tab: "Applications",
        description: "View pending license applications",
        code: "applications-returned_applicant-view"
      },
      %{
        service: "Submitted to Supervisor",
        tab: "Applications",
        description: "View pending license applications",
        code: "applications-submitted_to_supervisor-view"
      },
      %{
        service: "Submitted to Subordinate",
        tab: "Applications",
        description: "View pending license applications",
        code: "applications-submitted_to_subordinate-view"
      },
      %{
        service: "Submitted to LC",
        tab: "Applications",
        description: "View pending license applications",
        code: "applications-submitted_to_lc-view"
      },
      %{
        service: "Submitted to Board",
        tab: "Applications",
        description: "View pending license applications",
        code: "applications-submitted_to_board-view"
      },
      %{
        service: "Returned from Supervisor",
        tab: "Applications",
        description: "View pending license applications",
        code: "applications-returned_from_supervisor-view"
      },
      %{
        service: "License Applications",
        tab: "Applications",
        description: "View conditional license applications",
        code: "conditional_application-view"
      },
      %{
        service: "License Applications",
        tab: "Applications",
        description: "View issued license applications",
        code: "license_issued-view"
      },

      # API Management
      %{
        service: "API Management",
        tab: "API Maintenance",
        description: "View API maintenance",
        code: "api_maintenance_view"
      },
      %{
        service: "API Management",
        tab: "API Maintenance",
        description: "Modify API maintenance",
        code: "api_maintenance_modify"
      },
      %{
        service: "API Management",
        tab: "API Services",
        description: "View API services",
        code: "api_services-view"
      },

      # Approval Management
      %{
        service: "Approval Management",
        tab: "Approval Levels Maintenance",
        description: "View approval levels",
        code: "approval_levels-view"
      },
      %{
        service: "Approval Management",
        tab: "Approval Levels Maintenance",
        description: "Modify approval levels",
        code: "approval_levels-modify"
      },
      %{
        service: "Approval Management",
        tab: "Approval Levels Maintenance",
        description: "Change approval levels status",
        code: "approval_levels-status"
      },

      # Conditions Management
      %{
        service: "Conditions Management",
        tab: "Conditions Maintenance",
        description: "View conditions",
        code: "conditions-view"
      },
      %{
        service: "Conditions Management",
        tab: "Conditions Maintenance",
        description: "View conditional tracking",
        code: "conditional_tracking-view"
      },
      %{
        service: "Conditions Management",
        tab: "Conditions Maintenance",
        description: "Modify conditions",
        code: "conditions-modify"
      },
      %{
        service: "Conditions Management",
        tab: "Conditions Maintenance",
        description: "Change conditions status",
        code: "conditions-status"
      },

      # License Management
      %{
        service: "License Management",
        tab: "Licenses Maintenance",
        description: "View licenses",
        code: "licenses-view"
      },
      %{
        service: "steps_mapping",
        tab: "steps_mapping",
        description: "View step mapping",
        code: "steps_mapping-view"
      },

       %{
        service: "page",
        tab: "page",
        description: "View page",
        code: "page-view"
      },
      %{
        service: "License Management",
        tab: "Licenses Maintenance",
        description: "Modify licenses",
        code: "licenses-modify"
      },
      %{
        service: "License Management",
        tab: "Licenses Maintenance",
        description: "Change license status",
        code: "licenses-status"
      },
      %{
        service: "License Management",
        tab: "License Categories Maintenance",
        description: "View license categories",
        code: "license_categories-view"
      },
      %{
        service: "License Management",
        tab: "License Categories Maintenance",
        description: "Modify license categories",
        code: "license_categories-modify"
      },
      %{
        service: "License Management",
        tab: "License Categories Maintenance",
        description: "Change license categories status",
        code: "license_categories-status"
      },
      %{
        service: "License Management",
        tab: "Licences",
        description: "View licence maintenance",
        code: "licence-view"
      },
      %{
        service: "License Management",
        tab: "Licences",
        description: "Edit licence maintenance",
        code: "licence-edit"
      },
      %{
        service: "License Management",
        tab: "Licences",
        description: "Add licence",
        code: "licence-add"
      },
      %{
        service: "License Management",
        tab: "Licences",
        description: "Change licence status",
        code: "licence-status"
      },
      %{
        service: "License Management",
        tab: "Licences",
        description: "View licence drafts",
        code: "licence_drafts-view"
      },
      %{
        service: "License Management",
        tab: "Licences",
        description: "Edit licence drafts",
        code: "licence_drafts-edit"
      },
      %{
        service: "License Management",
        tab: "Licences",
        description: "Access licence registration",
        code: "license_registration"
      },

      # Messaging
      %{
        service: "Messaging",
        tab: "Messaging",
        description: "View single messages",
        code: "single_sms-view"
      },
      %{
        service: "Messaging",
        tab: "Messaging",
        description: "View broadcast messages",
        code: "broadcast_sms-view"
      },
      %{
        service: "Messaging",
        tab: "Messaging",
        description: "View multicast messages",
        code: "multicast_sms-view"
      },
      %{
        service: "Messaging",
        tab: "Messaging",
        description: "View SMS schedule",
        code: "schedule_sms-view"
      },
      %{
        service: "Messaging",
        tab: "Message Draft",
        description: "View message drafts",
        code: "message_draft_view"
      },
      %{
        service: "Messaging",
        tab: "Message Draft",
        description: "Modify message drafts",
        code: "message_draft_modify"
      },

      # Roles and Permissions
      %{
        service: "Roles and Permissions",
        tab: "Roles & Permissions",
        description: "Add users to access roles",
        code: "access_roles-add_users"
      },
      %{
        service: "Roles and Permissions",
        tab: "Roles & Permissions",
        description: "View users in access roles",
        code: "access_roles-view_users"
      },
      %{
        service: "Roles and Permissions",
        tab: "Roles & Permissions",
        description: "View access roles",
        code: "access_roles-view"
      },
      %{
        service: "Roles and Permissions",
        tab: "Roles & Permissions",
        description: "View access role permissions",
        code: "access_roles-view_permissions"
      },
      %{
        service: "Roles and Permissions",
        tab: "Roles & Permissions",
        description: "Modify access roles",
        code: "access_roles-modify"
      },
      %{
        service: "Roles and Permissions",
        tab: "Roles & Permissions",
        description: "Add access roles",
        code: "access_roles-add"
      },
      %{
        service: "Roles and Permissions",
        tab: "Roles & Permissions",
        description: "Assign permissions to access roles",
        code: "access_roles-assigns_permissions"
      },
      %{
        service: "Roles and Permissions",
        tab: "Roles & Permissions",
        description: "View department roles",
        code: "department_roles-view"
      },
      %{
        service: "Roles and Permissions",
        tab: "Roles & Permissions",
        description: "View department role permissions",
        code: "department_roles-view_permissions"
      },
      %{
        service: "Roles and Permissions",
        tab: "Roles & Permissions",
        description: "Assign permissions to department roles",
        code: "department_roles-assigns_permissions"
      },
      %{
        service: "Roles and Permissions",
        tab: "Roles & Permissions",
        description: "Modify department roles",
        code: "department_roles-modify"
      },
      %{
        service: "Roles and Permissions",
        tab: "Roles & Permissions",
        description: "Add department roles",
        code: "department_roles-add"
      },
      %{
        service: "Roles and Permissions",
        tab: "Authentication",
        description: "View system roles",
        code: "system_roles-view"
      },

      # Logs and Auditing
      %{
        service: "Logs and Auditing",
        tab: "Access Logs",
        description: "View access logs",
        code: "access_logs-view"
      },
      %{
        service: "Logs and Auditing",
        tab: "API Logs",
        description: "View API logs",
        code: "api_logs-view"
      },
      %{
        service: "Logs and Auditing",
        tab: "Session Logs",
        description: "View session logs",
        code: "session_logs-view"
      },
      %{
        service: "Logs and Auditing",
        tab: "System Audit Logs",
        description: "View system audit logs",
        code: "audit_logs-view"
      },
      %{
        service: "Logs and Auditing",
        tab: "SMS Logs",
        description: "View SMS logs",
        code: "sms_logs-view"
      },

      # Reports
      %{
        service: "Reports",
        tab: "Reports",
        description: "View prepaid statements",
        code: "prepaid_statements-view"
      },
      %{
        service: "Reports",
        tab: "Reports",
        description: "View postpaid statements",
        code: "postpaid_statements-view"
      },
      %{
        service: "Reports",
        tab: "Statements",
        description: "View monthly statements",
        code: "monthly_statements-view"
      },
      %{
        service: "Reports",
        tab: "Reports",
        description: "View monthly statistics",
        code: "monthly_statistics-view"
      },
      %{
        service: "Reports",
        tab: "Reports",
        description: "View message records",
        code: "message_records-view"
      },
      %{
        service: "Reports",
        tab: "Reports",
        description: "View message archives",
        code: "message_archives-view"
      },
      %{
        service: "Reports",
        tab: "Reports",
        description: "View all reports",
        code: "reports_view"
      },
      %{
        service: "Reports",
        tab: "Reports",
        description: "View provider reports",
        code: "reports_view-provider"
      },
      %{
        service: "Reports",
        tab: "Reports",
        description: "View client reports",
        code: "reports_view-client"
      },
      %{
        service: "Reports",
        tab: "Reports",
        description: "View daily reports",
        code: "view_daily_reports"
      },
      %{
        service: "Reports",
        tab: "Reports",
        description: "View weekly reports",
        code: "view_weekly_reports"
      },
      %{
        service: "Reports",
        tab: "Reports",
        description: "View monthly reports",
        code: "view_monthly_reports"
      },
      %{
        service: "Reports",
        tab: "Reports",
        description: "View annual reports",
        code: "view_annual_reports"
      },
      %{
        service: "Reports",
        tab: "Reports",
        description: "View accumulative reports",
        code: "view_accumulative_reports"
      },
      %{
        service: "Reports",
        tab: "Reports",
        description: "View transaction reports",
        code: "transactions-view"
      },
      %{
        service: "Reports",
        tab: "Reports",
        description: "View bills reports",
        code: "bills_reports_view"
      },
      %{
        service: "Reports",
        tab: "Reports",
        description: "View merchant reports",
        code: "merchant_reports_view"
      },

      # System Configuration
      %{
        service: "System Configuration",
        tab: "Error Codes",
        description: "View error codes",
        code: "error-view"
      },
      %{
        service: "System Configuration",
        tab: "Error Codes",
        description: "Edit error codes",
        code: "error-edit"
      },
      %{
        service: "System Configuration",
        tab: "Error Codes",
        description: "Add error codes",
        code: "error-add"
      },
      %{
        service: "System Configuration",
        tab: "Error Codes",
        description: "Change error code status",
        code: "error-status"
      },
      %{
        service: "System Configuration",
        tab: "SMPP",
        description: "View SMPP configuration",
        code: "smpp-view"
      },
      %{
        service: "System Configuration",
        tab: "SMPP",
        description: "Edit SMPP configuration",
        code: "smpp-edit"
      },
      %{
        service: "System Configuration",
        tab: "SMPP",
        description: "Add SMPP configuration",
        code: "smpp-add"
      },
      %{
        service: "System Configuration",
        tab: "SMPP",
        description: "Change SMPP status",
        code: "smpp-status"
      },

      # Device Management
      %{
        service: "Device Management",
        tab: "Devices",
        description: "View devices",
        code: "device_view"
      },
      %{
        service: "Device Management",
        tab: "Devices",
        description: "Edit devices",
        code: "device_edit"
      },
      %{
        service: "Device Management",
        tab: "Devices",
        description: "Add devices",
        code: "device_add"
      },
      %{
        service: "Device Management",
        tab: "Devices",
        description: "Change device status",
        code: "device_status"
      },
      %{
        service: "Device Management",
        tab: "Devices",
        description: "Remove devices",
        code: "device_remove"
      },

      # Rate Management
      %{
        service: "Rate Management",
        tab: "Rates",
        description: "View rates",
        code: "rates_view"
      },
      %{
        service: "Rate Management",
        tab: "Rates",
        description: "Edit rates",
        code: "rates_edit"
      },
      %{
        service: "Rate Management",
        tab: "Rates",
        description: "Add rates",
        code: "rates_add"
      },
      %{
        service: "Rate Management",
        tab: "Rates",
        description: "Change rate status",
        code: "rates_status"
      },

      # Category Management
      %{
        service: "Category Management",
        tab: "Categories",
        description: "View categories",
        code: "category_view"
      },
      %{
        service: "Category Management",
        tab: "Categories",
        description: "Edit categories",
        code: "category_edit"
      },
      %{
        service: "Category Management",
        tab: "Categories",
        description: "Add categories",
        code: "category_add"
      },
      %{
        service: "Category Management",
        tab: "Categories",
        description: "Change category status",
        code: "category_status"
      },

      # Voucher Management
      %{
        service: "Voucher Management",
        tab: "Vouchers",
        description: "View vouchers",
        code: "voucher_view"
      },
      %{
        service: "Voucher Management",
        tab: "Vouchers",
        description: "Edit vouchers",
        code: "voucher_edit"
      },
      %{
        service: "Voucher Management",
        tab: "Vouchers",
        description: "Add vouchers",
        code: "voucher_add"
      },
      %{
        service: "Voucher Management",
        tab: "Vouchers",
        description: "Change voucher status",
        code: "voucher_status"
      },

      # Payment Management
      %{
        service: "Payment Management",
        tab: "Payments",
        description: "View bundle purchases",
        code: "bundle_purchase-view"
      },
      %{
        service: "Payment Management",
        tab: "Payments",
        description: "View payment plans",
        code: "plan_maintenace-view"
      },
      %{
        service: "Payment Management",
        tab: "Payments",
        description: "View bill purchase",
        code: "bill_service_view"
      },
      %{
        service: "Payment Management",
        tab: "Payments",
        description: "Upload bulk payments",
        code: "bulk_upload_payments"
      },
      %{
        service: "Payment Management",
        tab: "Payments",
        description: "View single payment",
        code: "single_payment_view"
      },

      # NFS Management
      %{
        service: "NFS Management",
        tab: "NFS Maintenance",
        description: "View NFS configuration",
        code: "nfs_view"
      },
      %{
        service: "NFS Management",
        tab: "NFS Maintenance",
        description: "Create NFS configuration",
        code: "nfs_create"
      },
      %{
        service: "NFS Management",
        tab: "NFS Maintenance",
        description: "Edit NFS configuration",
        code: "nfs_edit"
      },
      %{
        service: "NFS Management",
        tab: "NFS Maintenance",
        description: "Enable/disable NFS",
        code: "nfs_status"
      },

      # Transaction Management
      %{
        service: "Transaction Management",
        tab: "Transaction",
        description: "Upload bulk vouchers",
        code: "bulk_upload_vouchers"
      },
      %{
        service: "Transaction Management",
        tab: "Transaction",
        description: "View batch transactions",
        code: "batch_transactions_view"
      },
      %{
        service: "Transaction Management",
        tab: "Transaction",
        description: "Decline batch transactions",
        code: "batch_decline"
      },
      %{
        service: "Transaction Management",
        tab: "Transaction",
        description: "First level approval",
        code: "batch_first_approve"
      },
      %{
        service: "Transaction Management",
        tab: "Transaction",
        description: "Second level approval",
        code: "batch_second_approve"
      },
      %{
        service: "Transaction Management",
        tab: "Transaction",
        description: "Add decline reason",
        code: "batch_decline_reason"
      },
      %{
        service: "Transaction Management",
        tab: "Transaction",
        description: "View transaction approvals",
        code: "txn_approval_view"
      },
      %{
        service: "Transaction Management",
        tab: "Transaction",
        description: "Decline transaction approvals",
        code: "txn_approval_decline"
      },
      %{
        service: "Transaction Management",
        tab: "Transaction",
        description: "Approve transactions",
        code: "txn_approval_approve"
      },

      # Settings
      %{
        service: "Settings",
        tab: "Settings",
        description: "View settings",
        code: "settings-view"
      },
      %{
        service: "Settings",
        tab: "Settings",
        description: "View configuration settings",
        code: "configuration-view"
      },
      %{
        service: "Settings",
        tab: "Settings",
        description: "View function settings",
        code: "function-view"
      }
    ]

    # Insert or update permissions
    Enum.each(permissions, fn data ->
      case Roles.get_permission_by_code(data.code) do
        nil ->
          Roles.create_permission(data)

        permission ->
          Roles.update_permission(permission, data)
      end
    end)
  end
end
