defmodule Statistics.Logs.PeriodicStatistic do
  use Ecto.Schema
  import Ecto.Changeset

  schema "tbl_prd_statistics" do
    field(:count, :integer)
    field(:status, :string)
    field(:submitted_at, :utc_datetime)
    field(:courier, :string)
    field(:courier_type, :string)

    belongs_to(:client, App.Accounts.Client, foreign_key: :client_id, type: :id)
    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(periodic_statistic, attrs) do
    periodic_statistic
    |> cast(attrs, [:count, :status, :submitted_at, :client_id, :courier, :courier_type])
    |> validate_required([:count, :status, :submitted_at, :client_id])
    |> unique_constraint(:count,
      name: :tbl_prd_statistics_count_status_submitted_at_client_id_courier_,
      message: "Already logged"
    )
  end
end
