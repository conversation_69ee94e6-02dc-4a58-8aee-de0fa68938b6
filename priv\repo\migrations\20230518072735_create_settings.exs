defmodule App.Repo.Migrations.CreateSettings do
  use Ecto.Migration

  def change do
    create table(:settings) do
      add :name, :string, size: 100
      add :status, :boolean, default: false, null: false
      add :description, :string, size: 300
      add :deleted_at, :naive_datetime
      add :updated_by, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create index(:settings, [:updated_by])
    create unique_index(:settings, [:name])
  end
end
