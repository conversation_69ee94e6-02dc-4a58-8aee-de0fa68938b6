defmodule SmsLogs.ViewComponent do
  use AppWeb, :live_component

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.simple_form for={@form} id="preview-form">
        <div class="rounded-lg bg-gray-50 shadow-sm ring-1 ring-gray-900/5">
          <!-- Farmer KYC Section -->
          <div class="p-4">
            <div class="mb-4 border-b pb-2">
              <h2 class="text-lg font-bold">Extra Details</h2>
            </div>

            <dl class="grid grid-cols-1 sm:grid-cols-2 gap-y-4">
              <div>
                <dt class="text-sm font-medium text-gray-500">Initiator:</dt>

                <dd class="text-lg font-semibold"><%= @details.initiator %></dd>
              </div>

              <div>
                <dt class="text-sm font-medium text-gray-500">Message ID:</dt>

                <dd class="text-lg font-semibold"><%= @details.smsc_msg_id || "N/A" %></dd>
              </div>
            </dl>
          </div>
          <!-- Extra Info Section -->
          <div class="p-4 border-t border-gray-300 flex">
            <!-- Location Section -->
            <div class="flex-1 pr-8">
              <div class="mb-4 border-b pb-2">
                <h2 class="text-lg font-bold">Other</h2>
              </div>

              <dl class="grid grid-cols-1 sm:grid-cols-2 gap-y-4">
                <div>
                  <dt class="text-sm font-medium text-gray-500">Country:</dt>

                  <dd class="text-lg font-semibold"><%= @details.country %></dd>
                </div>

                <div>
                  <dt class="text-sm font-medium text-gray-500">Date Sent:</dt>

                  <dd class="text-lg font-semibold">
                    <%= Calendar.strftime(
                      @details.sent_at,
                      "%d/%m/%Y"
                    ) %>
                  </dd>
                </div>
              </dl>
            </div>
          </div>
        </div>

        <.button type="button" phx-click="close_model">Close</.button>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{details: details} = assigns, socket) do
    form =
      to_form(
        %{
          "details" => details
        },
        as: "details_view"
      )

    {
      :ok,
      socket
      |> assign(assigns)
      |> assign(details: details)
      |> assign(form: form)
    }
  end
end
