<div class="px-4 sm:px-6 lg:px-8 mt-5">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">License Maintenance</h1>
    </div>

    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
      <.header>
        <:actions>
          <.button phx-click="add_license">Add New License</.button>
        </:actions>
      </.header>
    </div>
  </div>
  <!-- FILTERS -->
  <%= if @showFilter do %>
    <div class="border overflow-hidden animate-slide-in-from-top transition-all ease-in-out duration-500 rounded-lg bg-white my-4 flex flex-col gap-4 items-start">
      <h1 class="px-4 py-2 bg-gray-50 border-b font-medium w-full">Filter Users</h1>

      <FormJ.filter_form
        for={@form}
        class="w-full px-4"
        id="filter"
        phx-change="filter_change"
        phx-submit="search"
      >
        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 mb-4">
          <FormJ.input_filter
            field={@form[:email]}
            type="email"
            label="Email"
            placeholder="Enter email address"
          />
          <FormJ.input_filter
            field={@form[:mobile]}
            type="number"
            label="Mobile Number"
            placeholder="Mobile Number"
          />
          <FormJ.input_filter
            field={@form[:status]}
            type="select"
            label="Status"
            prompt="--Select Status--"
            options={[{"All", ""}, {"Active", "A"}, {"Blocked", "D"}]}
          />
        </div>

        <p class="text-gray-500 font-medium">Date Filters</p>

        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 pt-2">
          <FormJ.input_filter field={@form[:start_date]} type="date" label="From" />
          <FormJ.input_filter field={@form[:end_date]} type="date" label="To" />
        </div>

        <div class="flex gap-4 items-center justify-start mt-4 p-4 w-full border-t font-medium">
          <.button
            type="button"
            phx-click="reset_filter"
            class="cursor-pointer hover:text-brand-1 py-2"
          >
            Reset
          </.button>

          <.button type="submit" class="cursor-pointer hover:text-brand-1 py-2">Search</.button>
        </div>
      </FormJ.filter_form>
    </div>
  <% end %>
  <!-- END OF FILTERS -->
  <div class="w-full mt-8 flow-root">
    <Table.main_table id="adverts" rows={@data} params={@params} data_loader={@data_loader}>
      <:col :let={el} label={table_link(@params, "Created at", :inserted_at)}>
        <%= el.inserted_at %>
      </:col>

      <:col :let={el} label={table_link(@params, "Icon", :icon)}>
        <div
          class="w-10 h-10 rounded-full flex items-center justify-center"
          style={"background-color: #{el.color}"}
        >
          <.licence_icon name={el.icon} class="h-5 w-5 text-white" />
        </div>
      </:col>

      <:col :let={el} label={table_link(@params, "Name", :name)}>
        <%= el.name %>
      </:col>

      <:col :let={el} label={table_link(@params, "Form Number", :form_number)}>
        <%= el.form_number %>
      </:col>

      <:col :let={el} label={table_link(@params, "Security Number", :security_act_no)}>
        <%= el.security_act_no %>
      </:col>

      <:col :let={el} label={table_link(@params, "Section", :section)}>
        <%= el.section %>
      </:col>

      <:col :let={rj} label={table_link(@params, "Status", :status)}>
        <Table.table_string_status status={rj.status} />
      </:col>

      <:action :let={el}>
        <Option.bordered>
          <%= if el.status == "A" do %>
            <.link
              phx-click="edit"
              phx-value-id={el.id}
              class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-white"
            >
              Edit
            </.link>

            <.link
              navigate={"/license/management/#{el.id}"}
              class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-white"
            >
              Manage License
            </.link>

            <.link
              navigate={"/license/summary/#{el.id}"}
              class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-white"
            >
              License Summary
            </.link>

            <.link
              phx-click="update_status"
              phx-value-status="D"
              phx-value-id={el.id}
              class="w-full text-left text-rose-700 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-rose-900"
            >
              Deactivate
            </.link>
          <% end %>

          <%= if el.status == "D" do %>
            <.link
              phx-click="update_status"
              phx-value-status="A"
              phx-value-id={el.id}
              class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-brand-1 hover:text-green-500"
            >
              Activate
            </.link>

            
          <% end %>
        </Option.bordered>
      </:action>
    </Table.main_table>
  </div>
</div>

<Model.small :if={@live_action in [:filter]} id="transaction-modal" show return_to="close_model">
  <.live_component
    module={AppWeb.Component.DateFilter}
    id={:filter}
    title={@page_title}
    @click.outside="open = false"
    params={@params}
  />
</Model.small>

  <Model.small
    :if={@live_action in [:new, :edit]}
    id="user-modal"
    show
    return_to="close_model"
    title={@page_title}
  >
  <.live_component
    module={AppWeb.LicenseLive.FormComponent}
    id={@license.id || :new}
    title={@page_title}
    action={@live_action}
    license={@license}
    browser_info={@browser_info}
    current_user={@current_user}
  />
</Model.small>

<Model.confirmation_model
  :if={@live_action == :confirm}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
<Model.confirmation_with_des
  :if={@live_action == :confirm_with_des}
  show
  id="confirmation_with_des-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
