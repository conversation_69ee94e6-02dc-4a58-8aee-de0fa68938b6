defmodule App.Repo.Migrations.CreatePermissions do
  use Ecto.Migration

  def change do
    create table(:permissions) do
      add :service, :string, size: 100
      add :description, :string, size: 300
      add :tab, :string, size: 300
      add :code, :string, size: 100

      timestamps()
    end

    create index(:permissions, [:service])
    create index(:permissions, [:tab])
    create unique_index(:permissions, [:code])
  end
end
