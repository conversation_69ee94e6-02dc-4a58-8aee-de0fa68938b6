defmodule AppWeb.Reports.YearlyLive.Index do
  use AppWeb, :live_view

  alias App.Service.Table.YearlyReports, as: TableQuery
  alias App.{Accounts, Clients}

  @impl true
  def mount(_params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("view_annual_reports", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Yearly Reports", "Accessed Yearly Reports Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      filter_data = %{
        "year" => nil,
        "client_id" => nil
      }

      assign(socket, data: [])
      |> assign(show_filter: false)
      |> assign(data_loader: true)
      |> assign(years: 2018..DateTime.utc_now().year)
      |> assign(year: [])
      |> assign(:clients, Accounts.get_clients!())
      |> assign(maker_checker: false)
      |> assign(form: filter_form())
      |> assign(live_socket_id: session["live_socket_id"])
      |> assign(showFilter: false)
      |> assign(form: useform(filter_data))
      |> LivePageControl.order_by_composer()
      |> LivePageControl.i_search_composer()
      |> ok()
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Yearly Reports", "Access Denied", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  defp useform(data) do
    to_form(
      data,
      as: "filter"
    )
  end

  defp filter_form(data \\ %{}), do: to_form(data, as: "filter")

  @impl true
  def handle_params(params, url, socket) do
    socket.endpoint.broadcast_from!(
      self(),
      "nav:" <> socket.assigns.live_socket_id,
      "change_nav",
      %{uri_path: URI.parse(url).path}
    )

    if connected?(socket), do: send(self(), {:get_list, params})

    socket
    |> assign(:params, params)
    |> apply_action(socket.assigns.live_action, params)
    |> noreply()
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "Yearly Reports")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {_record, _info} ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :live_action, :index)
        |> apply_action(:index, %{})
        |> noreply()
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, socket) do
    case target do
      "iSearch" ->
        send(self(), {:get_list, value})

        LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))
        |> noreply()

      "refresh_table" ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :data_loader, true)
        |> noreply()

      "filter" ->
        value = if socket.assigns.show_filter == true, do: false, else: true

        assign(socket, :show_filter, value)
        |> noreply()

      "filter_change" ->
        assign(socket, form: useform(value["filter"]))
        |> noreply()

      "filter" ->
        value = if socket.assigns.showFilter == true, do: false, else: true

        assign(socket, :showFilter, value)
        |> noreply()

      "reset_filter" ->
        socket
        |> assign(form: useform(%{}))
        |> noreply()

      "search" ->
        send(self(), {:get_list, value})

        noreply(
          socket
          |> assign(checked_ids: [])
          |> assign(param_list: [])
        )

      "live_select_change" ->
        handle_search(value, socket)

      "export" ->
        LiveFunctions.export_records(
          socket,
          value,
          "annual_provider_report_service",
          Map.put(socket.assigns.params, "department", socket.assigns.role_department)
        )

      "refresh_table" ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :data_loader, true)
        |> noreply()

        # _ ->
        {:noreply, socket}
    end
  end

  def handle_search(%{"text" => text, "id" => live_select_id}, socket) do
    clients = Clients.search(text)

    send_update(AppWeb.Component, id: live_select_id, options: clients)

    {:noreply, socket}
  end

  defp list(socket, params) do
    data = TableQuery.index(socket, LivePageControl.create_table_params(socket, params))

    {
      :noreply,
      assign(socket, :data, data)
      |> assign(data_loader: false)
      |> assign(params: params)
      #  |> assign(year: if(data != [], do: List.first(data.entries).year))
    }
  end
end
