defmodule Logs.LogRepo.Migrations.CreateLogsSystem do
  use Ecto.Migration

  def change do
    create table(:logs_system) do
      add :narration, :string, size: 400, null: false
      add :action, :string, size: 50, null: false
      add :attrs, :string, size: 6000, null: false
      add :service, :string, size: 100, null: false
      add :device_uuid, :string, null: true
      add :session_id, :string, size: 150, null: true
      add :ip_address, :string, size: 50, null: true
      add :full_browser_name, :string, size: 200, null: true
      add :browser_details, :string, size: 200, null: true
      add :system_platform_name, :string, size: 300, null: true
      add :device_type, :string, size: 150, null: true
      add :known_browser, :boolean, default: false, null: false
      add :user_id, :integer

      timestamps()
    end

    create index(:logs_system, [:user_id])
  end
end
