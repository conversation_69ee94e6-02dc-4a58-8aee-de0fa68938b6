defmodule App.Service.ServiceSender.Functions do
  alias App.Settings

  def index(socket, attrs, function \\ "change_status") do
    record = Settings.get_sender!(attrs["id"])

    cond do
      function == "change_status" ->
        Settings.change_sender_status(socket, attrs, record)

      function == "approve" ->
        Settings.approve_sender(socket, attrs, record)

      function == "decline" ->
        Settings.approve_sender(socket, attrs, record)
    end
  end

  def create(socket, attrs) do
    Settings.create_sender(socket, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end

  def update(socket, record, attrs) do
    Settings.update_sender(socket, record, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end
end
