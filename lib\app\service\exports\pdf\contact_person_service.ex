defmodule App.Service.Export.ContactPersonServicePdf do
  @moduledoc false

  alias App.Service.Table.Contact

  alias App.Service.Export.{
    Functions
  }

  def index(payload) do
    results =
      Contact.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "first_name" => data.first_name,
          "last_name" => data.last_name,
          "mobile" => data.mobile,
          "email" => data.email,
          "client_id" => data.client_id,
          "inserted_at" => data.inserted_at,
          "updated_at" => data.updated_at,
          "status" => Functions.table_numeric_status(data.status)
        }
      end)
      |> Enum.map(fn data ->
        """
         <tr>
            <td style="text-align: center;">{{ first_name }}</td>
            <td style="text-align: center;">{{ last_name }}</td>
            <td style="text-align: center;">{{ mobile }}</td>
            <td style="text-align: center;">{{ email }}</td>
            <td style="text-align: center;">{{ client_id }}</td>
            <td style="text-align: center;">{{ inserted_at }}</td>
            <td style="text-align: center;">{{ updated_at }}</td>
            <td style="text-align: center;">{{ status }}</td>


         </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/contact_person.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Contact Person",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end
