defmodule App.Service.Dashboard.Stats do
  alias App.{Licenses, Roles}

  def new(socket) do
    # Licenses.get_license_category_by_name("Licencing") |> IO.inspect
    Licenses.get_license_approval_status_list(
      socket.assigns.role_department,
      socket.assigns.current_user.role_id
    )
    |> Enum.filter(&(!is_nil(&1)))
    |> Enum.uniq()
    |> Licenses.get_new_applications_count() || 0
  end

  def market_operations(socket) do
    Licenses.get_license_approval_status(
      socket.assigns.role_department,
      socket.assigns.current_user.role_id
    )
    |> Licenses.get_market_operations(socket.assigns.current_user.id) ||
      0
  end

  def supervisor(socket) do
    Licenses.get_license_approval_status_list(
      socket.assigns.role_department,
      socket.assigns.current_user.role_id
    )
    |> Licenses.get_submitted_to_supervisor(socket.assigns.current_user.id) ||
      0
  end

  def returned_to_application(socket),
    do:
      Licenses.get_returned_to_applicant_mappings(socket.assigns.current_user.id) ||
        0
end
