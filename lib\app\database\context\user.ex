defmodule App.Users do
  import Ecto.Query, warn: false
  alias App.Repo

  alias App.{
    Accounts.Devices,
    Accounts.User,
    Accounts.UserToken,
    Util.CustomTime,
    Send.Email,
    Roles
  }

  @doc """
  Gets a single user.

  Raises `Ecto.NoResultsError` if the User does not exist.

  ## Examples

      iex> get_user!(123)
      %User{}

      iex> get_user!(456)
      ** (Ecto.NoResultsError)

  """
  def get_user!(id) do
    User
    |> where([a], a.id == ^id)
    |> limit(1)
    |> Repo.one()
  end

  def get_user_by_email(email) do
    User
    |> where([a], a.email == ^email)
    |> limit(1)
    |> Repo.one()
  end

  def registration_seed_user(attrs) do
    %User{}
    |> User.registration_seed_changeset(attrs, hash_password: true)
    |> Repo.insert()
  end

  def registration_admin_user(attrs) do
    %User{}
    |> User.registration_admin_changeset(attrs, hash_password: true)
    |> Repo.insert()
  end

  def update_user(user, attrs) do
    User.changeset(user, attrs)
    |> Repo.update()
  end

  def change_user_login(attrs \\ %{}) do
    User.login_changeset(%User{}, attrs, hash_password: false, validate_email: false)
  end

  def create_self_reg_user(attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "users",
      User.self_registration_insert_changeset(
        %User{},
        %{
          "auto_password" => "N",
          "role_id" => Roles.get_access_role_by_department(8, "Client"),
          "sex" => attrs["sex"],
          "user_type" => "CLIENT",
          "registration_type" => "INDIVIDUAL",
          "email" => attrs["email"],
          "first_name" => attrs["first_name"],
          "mobile" => attrs["mobile"],
          "last_name" => attrs["last_name"],
          "password" => attrs["password"]
        }
      )
    )
    |> Repo.transaction()
  end

  @doc """
  Gets a user by email and password.

  ## Examples

      iex> get_user_by_email_and_password("<EMAIL>", "correct_password")
      %User{}

      iex> get_user_by_email_and_password("<EMAIL>", "invalid_password")
      nil

  """
  def get_user_by_email_and_password(email, password)
      when is_binary(email) and is_binary(password) do
    user = Repo.get_by(User, email: email)

    if User.valid_password?(user, password), do: user
  end

  def get_user_device!(device_id, user_id) do
    Devices
    |> where([a], a.device_id == ^device_id and a.user_id == ^user_id)
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Creates a devices.

  ## Examples

      iex> create_devices(%{field: value})
      {:ok, %Devices{}}

      iex> create_devices(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_devices(attrs \\ %{}) do
    %Devices{}
    |> Devices.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a devices.

  ## Examples

      iex> update_devices(devices, %{field: new_value})
      {:ok, %Devices{}}

      iex> update_devices(devices, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_devices(%Devices{} = devices, attrs) do
    devices
    |> Devices.changeset(attrs)
    |> Repo.update()
  end

  def register_device(conn, data, user) do
    if device_id = get_user_device!(data["device_id"], user.id) do
      update_devices(device_id, %{
        "last_activity" => CustomTime.local_datetime(),
        "user_id" => user.id
      })
    else
      create_devices(%{
        "device_id" => data["device_id"],
        "name" => Browser.full_browser_name(conn),
        "platform" => to_string(Browser.device_type(conn)),
        "operating_system" => Browser.full_platform_name(conn),
        "last_activity" => CustomTime.local_datetime(),
        "user_id" => user.id
      })
    end
  end

  ## Session

  @doc """
  Generates a session token.
  """
  def generate_user_session_token(user) do
    {token, user_token} = UserToken.build_session_token(user)
    Repo.insert!(user_token)
    token
  end

  @doc """
  Gets the user with the given signed token.
  """
  def get_user_by_session_token(token) do
    {:ok, query} = UserToken.verify_session_token_query(token)
    Repo.one(query)
  end

  @doc """
  Deletes the signed token with the given context.
  """
  def delete_user_session_token(token) do
    Repo.delete_all(UserToken.token_and_context_query(token, "session"))
    :ok
  end

  def change_password(attrs \\ %{}, user \\ %User{}) do
    User.change_password(user, attrs, hash_password: false)
  end

  def change_user_password_on_login(user, attrs \\ %{}) do
    User.change_password(user, Map.put(attrs, "auto_password", "N"), hash_password: true)
    |> Repo.update()
  end

  def get_all_user_sessions do
    UserToken
    |> select([a], {a.token, a.user_id})
    |> Repo.all()
  end

  alias App.Accounts.AdminContacts

  @doc """
  Returns the list of tbl_admin_contacts.
  ## Examples
      iex> list_tbl_admin_contacts()
      [%AdminContacts{}, ...]
  """
  def list_tbl_admin_contacts do
    Repo.all(AdminContacts)
  end

  @doc """
  Gets a single AdminContacts.

  Raises `Ecto.NoResultsError` if the AdminContacts does not exist.

  ## Examples

      iex> get_user!(123)
      %AdminContacts{}

      iex> get_user!(456)
      ** (Ecto.NoResultsError)

  """
  def get_admin_contacts!(id), do: Repo.get!(AdminContacts, id)

  def get_admin_contact_by_name(name) do
    AdminContacts
    |> where([a], a.name == ^name)
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Creates a AdminContacts.

  ## Examples

      iex> create_user(%{field: value})
      {:ok, %AdminContacts{}}

      iex> create_user(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_admin_contacts(attrs \\ %{}) do
    %AdminContacts{}
    |> AdminContacts.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a AdminContacts.

  ## Examples

      iex> update_user(AdminContacts, %{field: new_value})
      {:ok, %AdminContacts{}}

      iex> update_user(AdminContacts, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_admin_contacts(%AdminContacts{} = admin_contacts, attrs) do
    admin_contacts
    |> AdminContacts.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a admin_contacts.

  ## Examples

      iex> delete_user(admin_contacts)
      {:ok, %AdminContacts{}}

      iex> delete_user(AdminContacts)
      {:error, %Ecto.Changeset{}}

  """
  def delete_admin_contacts(%AdminContacts{} = admin_contacts) do
    Repo.delete(admin_contacts)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking admin_contacts changes.

  ## Examples

      iex> change_user(admin_contacts)
      %Ecto.Changeset{data: %AdminContacts{}}

  """
  def change_users(%User{} = user, attrs \\ %{}) do
    User.changeset(user, attrs)
  end

  def self_change_users(attrs \\ %{}) do
    User.self_registration_changeset(%User{}, attrs, hash_password: false)
  end

  def company_change_users(attrs \\ %{}) do
    User.company_registration_changeset(%User{}, attrs, hash_password: false)
  end

  def send_login_otp(user) do
    cond do
      is_nil(user) ->
        {:error, user}

      true ->
        cache_put(user)
    end
  end

  def send_otp_to_user(attrs) do
    user = get_user_by_email(attrs["email"] || "")

    cond do
      is_nil(user) ->
        {:error, user}

      true ->
        cache_put(user)
    end
  end

  def send_validation_otp(params) do
    otp = NumberF.randomizer(4, :numeric)

    IO.inspect(otp, label: :labelllll)

    case Cachex.put(:system, "otp:#{params["email"]}", otp, ttl: :timer.minutes(5)) do
      {:ok, true} ->
        Task.start(fn ->
          Email.send_otp_notification(params["email"], otp)
        end)

        :ok

      {:ok, false} ->
        {:error, "Failed to put in Cache"}

      {:error, :no_cache} ->
        {:error, "NO Cache Found"}
    end
  end

  def validate_self_reg_otp(socket, user, attrs) do
    otp =
      Enum.reduce(
        1..socket.assigns.otp_field_number,
        [],
        fn x, result ->
          Enum.concat(result, [attrs[to_string(x)]])
        end
      )
      |> Enum.join("")

    check_otp =
      case Cachex.get(:system, "otp:#{user["email"]}") do
        {:ok, ^otp} ->
          true

        {:ok, _} ->
          false
      end

    cond do
      !check_otp ->
        :error

      true ->
        case Cachex.del(:system, "otp:#{user["email"]}") do
          {:ok, true} ->
            :ok
        end
    end
  end

  def resend_self_reg_otp(user) do
    case Cachex.del(:system, "otp:#{user["email"]}") do
      {:ok, true} ->
        send_validation_otp(user)

      {:ok, false} ->
        {:error, "Failed to remove from Cache"}
    end
  end

  defp cache_put(user) do
    otp = NumberF.randomizer(4, :numeric)

    case Cachex.put(:system, "otp:#{user.id}", otp, ttl: :timer.minutes(5)) do
      {:ok, true} ->
        Task.start(fn ->
          Email.send_otp_notification(user.email, otp)
        end)

        {:ok, user}

      {:ok, false} ->
        {:error, "Failed to put in Cache"}

      {:error, :no_cache} ->
        {:error, "NO Cache Found"}
    end
  end

  def resend_otp(user) do
    case Cachex.del(:system, "otp:#{user.id}") do
      {:ok, true} ->
        cache_put(user)

      {:ok, false} ->
        {:error, "Failed to remove from Cache"}
    end
  end

  def validate_otp(socket, user, attrs) do
    otp =
      Enum.reduce(
        1..socket.assigns.otp_field_number,
        [],
        fn x, result ->
          Enum.concat(result, [attrs[to_string(x)]])
        end
      )
      |> Enum.join("")

    check_otp =
      case Cachex.get(:system, "otp:#{user.id}") do
        {:ok, ^otp} ->
          true

        {:ok, _} ->
          false
      end

    cond do
      !check_otp ->
        {:error, user}

      true ->
        case Cachex.del(:system, "otp:#{user.id}") do
          {:ok, true} ->
            {:ok, user}
        end
    end
  end
end
