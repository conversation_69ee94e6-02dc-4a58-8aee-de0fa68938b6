defmodule AppWeb.Auth.LogsLive.DevicesLive.Index do
  use AppWeb, :live_view
  use Ecto.Schema

  alias App.Service.Table.Maintenance.ServiceDevices, as: TableQuery
  alias App.Accounts

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("device_view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Device Maintenance", "Accessed Device Maintenance Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      filter_data = %{
        "name" => nil,
        "status" => nil,
        "start_date" => nil,
        "end_date" => nil
      }

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> assign(params: params)
        |> assign(confirmation_model: false)
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(session_id: session["live_socket_id"])
        |> assign(enable_nfs_form: "hide")
        |> assign(disable_nfs_form: "hide")
        |> assign(showFilter: false)
        |> assign(form: useform(filter_data))
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Payment Providers Maintenance", "Accessed Payment Providers Maintenance Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  defp useform(data) do
    to_form(
      data,
      as: "filter"
    )
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_list, params})

    {:noreply,
     socket
     |> assign(:params, params)
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "Devices")
    |> assign(:record, nil)
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {:search_records, params} ->
        send(self(), {:get_list, %{"filter" => params}})
        noreply(socket)

      {AppWeb.Component.DateFilter, {:filtered, data}} ->
        send(self(), {:get_list, %{"filter" => data}})

        {
          :noreply,
          socket
          |> assign(:live_action, :index)
          |> assign(:page_title, "Listing Devices")
        }

      {:change_record_status, params} ->
        update(socket, params)

      {AppWeb.LiveFunctions, message} ->
        send(self(), {:get_list, %{}})

        {
          :noreply,
          socket
          |> put_flash(:info, message)
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }
    end
  end

  defp update(socket, attrs) do
    nfs = Accounts.get_devices!(attrs["id"])

    Accounts.change_device_status(socket, attrs, nfs)
    |> case do
      {:ok, _} ->
        send(self(), {:get_list, %{}})

        {
          :noreply,
          assign(socket, confirmation_model: false)
          |> assign(confirmation_model_icon: "exclamation_circle")
          |> LiveFunctions.sweet_alert("Status Successfully Changed", "success")
        }

      {:error, error} ->
        send(self(), {:get_list, %{}})

        {
          :noreply,
          assign(socket, confirmation_model: false)
          |> assign(confirmation_model_icon: "exclamation_circle")
          |> LiveFunctions.sweet_alert(error, "error")
        }
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, %{assigns: assigns} = socket) do
    case target do
      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      "change_record_status" ->
        LiveFunctions.change_record(value, socket, :change_record_status)
        {:noreply, socket}

      "filter" ->
        value = if assigns.showFilter == true, do: false, else: true

        assign(socket, :showFilter, value)
        |> noreply()

      "search" ->
        send(self(), {:get_list, value})

        noreply(
          socket
          |> assign(checked_ids: [])
          |> assign(param_list: [])
        )

      "export" ->
        LiveFunctions.export_records(socket, value, "nfs_service", socket.assigns.form.params())

      "close_model" ->
        socket =
          socket
          |> assign(:new_edit_modal, false)
          |> assign(:view_modal, false)
          |> assign(:live_action, :index)
          |> apply_action(:index, value)

        {:noreply, socket}

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :confirmation_model, false)
        }

      "update_status" ->
        new_status = if value["status"] == "0", do: "remove", else: "enable"

        {
          :noreply,
          assign(socket, :confirmation_model, true)
          |> assign(
            :confirmation_model_text,
            "Are you sure you want to #{new_status} this Device"
          )
          |> assign(:confirmation_model_params, value)
          |> assign(:confirmation_model_agree, "change_record_status")
        }

      _ ->
        {:noreply, socket}
    end
  end

  defp list(%{assigns: assigns} = socket, params) do
    data =
      LivePageControl.create_table_params(socket, params)
      |> Map.merge(%{"session_id" => socket.assigns.session_id})

    {
      :noreply,
      assign(socket, :data, TableQuery.index(assigns, data))
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end

  def check_compare(current, target) do
    String.downcase(current)
    |> String.contains?(String.downcase(target))
  end
end
