defmodule App.SetUp.Conditions do
  @moduledoc false
  alias App.LicenseConditions

  def init do
    [
      %{
        "name" => "Zambia Police (ZP)",
        "description" => "This condition applies to all law enforcement agencies.",
        "type" => "Law Enforcement",
        "status" => 1,
        "user_id" => 1
      },
      %{
        "name" => "Office of the President (OP)",
        "description" => "This condition applies to all law enforcement agencies.",
        "type" => "Law Enforcement",
        "status" => 1,
        "user_id" => 1
      },
      %{
        "name" => "Drug Enforcement Commission (DEC)",
        "description" => "This condition applies to all law enforcement agencies.",
        "type" => "Law Enforcement",
        "status" => 1,
        "user_id" => 1
      },
      %{
        "name" => "Anti Corruption Commission (ACC)",
        "description" => "This condition applies to all law enforcement agencies.",
        "type" => "Law Enforcement",
        "status" => 1,
        "user_id" => 1
      },
      %{
        "name" => "Financial Intelligence Centre (FIC)",
        "description" => "This condition applies to all law enforcement agencies.",
        "type" => "Law Enforcement",
        "status" => 1,
        "user_id" => 1
      },
      %{
        "name" => "Bank of Zambia (BoZ)",
        "description" => "This condition applies to all law enforcement agencies.",
        "type" => "Law Enforcement",
        "status" => 1,
        "user_id" => 1
      },
      %{
        "name" => "Pensions and Insurance Authority (PIA)",
        "description" => "This condition applies to all law enforcement agencies.",
        "type" => "Law Enforcement",
        "status" => 1,
        "user_id" => 1
      },
      %{
        "name" => "Expired Work Permit",
        "description" => "This condition applies to an expired work permit.",
        "type" => "Non-law Enforcement",
        "status" => 1,
        "user_id" => 1
      }
    ]
    |> Enum.each(fn data ->
      if user = LicenseConditions.get_condition_by_name(data["name"]) do
        LicenseConditions.update_conditions(user, data)
      else
        LicenseConditions.create_conditions(data)
      end
    end)
  end
end
