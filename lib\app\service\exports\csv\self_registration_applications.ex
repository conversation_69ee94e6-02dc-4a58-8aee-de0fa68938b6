defmodule App.Service.Export.CsvSelfRegistrationApplicationsServices do
  @moduledoc false
  alias App.Service.Table.SelfRegistration
  alias App.Service.Export.Functions

  @headers [
    "REGISTRATION DATE",
    "COMPANY NAME",
    "CONTACT NAME",
    "EMAIL",
    "MO<PERSON>LE NUMBER",
    "STATUS"
  ]

  def index(assigns, payload) do
    SelfRegistration.export(assigns, payload)
    |> Stream.map(
      &[
        &1.inserted_at,
        &1.company_name,
        &1.full_name,
        &1.email,
        &1.mobile_number,
        Functions.self_registration_status(&1.status)
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Self Registration Applications"],
              ["", "", "", ""],
              @headers,
              ["", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
