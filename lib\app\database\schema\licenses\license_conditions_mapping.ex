defmodule App.Licenses.LicenseConditionsMapping do
  use Ecto.Schema
  import Ecto.Changeset

  schema "license_conditions_mapping" do
    field :condition_met, :boolean, default: false
    field :status, :integer, default: 0
    field :responsibility, :string
    field :comments, :string
    field :reason, :string
    field :expiring_date, :string
    field :expiring_status, :string
    belongs_to :added_by, App.Accounts.User
    belongs_to :confirmed_by, App.Accounts.User
    belongs_to :condition, App.Licenses.Conditions
    belongs_to :application, App.Licenses.LicenseMapping

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(license_conditions, attrs) do
    license_conditions
    |> cast(attrs, [
      :condition_id,
      :responsibility,
      :application_id,
      :expiring_date,
      :expiring_status,
      :comments,
      :reason,
      :condition_met,
      :added_by_id,
      :confirmed_by_id,
      :status
    ])
    |> validate_required([:condition_id, :responsibility])
  end
end
