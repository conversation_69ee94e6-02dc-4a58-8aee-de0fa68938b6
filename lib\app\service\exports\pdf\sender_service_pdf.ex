defmodule App.Service.Export.SenderServicePdf do
  @moduledoc false

  alias App.Service.Table.ServiceSender

  alias App.Service.Export.{
    Functions
  }

  def index(assigns, payload) do
    results =
      ServiceSender.export(assigns, payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "sender" => data.sender,
          "client_name" => data.client_name,
          "created_by" => data.created_by,
          "inserted_at" => data.inserted_at,
          "updated_at" => data.updated_at
        }
      end)
      |> Enum.map(fn data ->
        """
          <tr>
            <td style="text-align: center;">{{ sender }}</td>
            <td style="text-align: center;">{{ client_name }}</td>
            <td style="text-align: center;">{{ created_by }}</td>
            <td style="text-align: center;">{{ inserted_at }}</td>
            <td style="text-align: center;">{{ updated_at }}</td>
          </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/sender_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Sender",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end
