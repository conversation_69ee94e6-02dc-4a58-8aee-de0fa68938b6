defmodule AppWeb.Auth.LicenseMagementLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias App.Roles
  alias App.Licenses.LicenseData
  alias App.Licenses

  @impl true
  def mount(params, _s, socket) do
    forms = App.Licenses.query_from_licence_field()
    selected_ids = App.Licenses.get_licence_data_by_license_id(params["id"])

    {:ok,
     socket
     |> assign(:licenses_forms, forms)
     # |> assign(:ilike_forms, forms)
     |> assign(:name, App.Licenses.get_license_name(params["id"]))
     |> assign(:field_label, App.Licenses.query_field_label())
     |> assign(:selected_ids, selected_ids)
     |> assign(:updated_list, [])
     |> assign(:filter, [])
     |> assign(:license_id, params["id"])
     |> assign(confirmation_model: false)
     |> assign(confirmation_model_title: "Are you sure?")
     |> assign(confirmation_model_text: "")
     |> assign(confirmation_model_agree: "")
     |> assign(confirmation_model_reject: "close_confirmation_model")
     |> assign(confirmation_model_params: "")
     |> assign(confirmation_model_icon: "exclamation_circle")}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply,
     socket
     |> assign(:params, params)
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "License")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      {:save, params} -> save(params, socket)
    end
  end

  @impl true
  def handle_event(target, params, socket), do: handle_event_switch(target, params, socket)

  def handle_event_switch(target, params, %{assigns: assigns} = socket) do
    case target do
      "search" ->
        search(params, socket)

      "save" ->
        save(params, socket)

      "validate" ->
        validate(params, socket)

      "check_id" ->
        id = String.to_integer(params["id"])

        update_list =
          case assigns.selected_ids do
            [] ->
              [id]

            _ ->
              if Enum.member?(assigns.selected_ids, id) do
                Enum.reject(assigns.selected_ids, &(&1 == id))
              else
                assigns.selected_ids ++ [id]
              end
          end

        {:noreply,
         socket
         |> assign(selected_ids: Enum.map(update_list, & &1))
         |> assign(:updated_list, update_list)}
    end
  end

  def search(params, socket) do
    filter = String.downcase(params["filter"] || "")

    forms =
      App.Licenses.query_field_label()
      |> Enum.filter(fn %{field_label: field_label} ->
        String.contains?(String.downcase(field_label), filter)
      end)

    {:noreply,
     socket
     |> assign(licenses_forms: forms, filter: filter)}
  end

  def validate(params, socket) do
    {:noreply,
     socket
     |> assign(:license_manage, params)}
  end

  def save(params, %{assigns: assigns} = socket) do
    param = %{"license_id" => assigns.license_id, "license_field_id" => assigns.updated_list}

    case App.Licenses.create_license_data(socket, param) do
      {:ok, _} ->
        socket =
          socket
          |> LiveFunctions.sweet_alert("Successfully Created.", "success")

        reassign(socket)
        {:noreply, socket}

      _ ->
        {:noreply, LiveFunctions.sweet_alert(socket, "No data created.", "warning")}
    end
  end

  defp reassign(socket) do
    selected = App.Licenses.get_licence_data_by_license_id(socket.assigns.license_id)

    {:noreply,
     socket
     |> assign(:updated_list, [])
     |> assign(:selected_ids, selected)
     |> push_navigate(to: "/license/management/#{socket.assigns.license_id}")}
  end
end
