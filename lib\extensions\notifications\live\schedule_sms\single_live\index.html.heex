<div class="px-4 sm:px-6 lg:px-8 mt-8 flow-root">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header  -->
    <div class="p-6 text-center rounded-md">
      <div class="flex items-center justify-between">
        <button
          onclick="window.history.back()"
          class="text-gray-600 hover:text-gray-800 focus:outline-none rounded-full p-3 border border-brand-10"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
        <h1 class="text-3xl font-bold tracking-tight text-gray-900 flex-1 text-center">
          Schedule SMS
        </h1>
        <div class="w-6"></div>
        <!-- Placeholder for alignment -->
      </div>
      <p class="mt-2 text-gray-600">Send personalized messages to your recipients</p>
    </div>
    <div class="flex justify-center items-center p-4">
      <div class="w-full max-w-4xl mx-auto">
        <div class="bg-white shadow-xl rounded-xl p-6 transition-all duration-300 hover:shadow-2xl">
          <.simple_form for={@form} id="city-form" phx-change="validate" phx-submit="save">
            <div class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <.input
                  field={@form[:sender_id]}
                  type="select"
                  options={@chose_sender}
                  prompt="Select Sender"
                  required
                  label={raw(~c"Sender <span class='text-red-500'>*</span>")}
                  class="custom-select"
                  wrapper_class="space-y-2"
                />
                <.input
                  field={@form[:mobile]}
                  type="tel"
                  phx-hook="validateNumberHook2"
                  placeholder="e.g. 26096*******"
                  required
                  label={raw(~c"Recipient's Number <span class='text-red-500'>*</span>")}
                  wrapper_class="space-y-2"
                  input_class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <.input
                field={@form[:source]}
                type="text"
                placeholder="e.g. Accounting Department"
                required
                label={raw(~c"Source <span class='text-red-500'>*</span>")}
                wrapper_class="space-y-2"
                input_class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <.input
                  field={@form[:date]}
                  type="date"
                  label={raw(~c"Schedule Date <span class='text-red-500'>*</span>")}
                  value={@min_date}
                  min={@min_date}
                  required
                />
                <.input
                  field={@form[:time]}
                  type="time"
                  value={
                    {_, {h, m, _s}} = :calendar.local_time()
                    "#{h}:#{m}"
                  }
                  label={raw(~c"Time <span class='text-red-500'>*</span>")}
                  required
                  wrapper_class="space-y-2"
                  input_class="w-full p-3 border border-gray-300 rounded-lg"
                />
              </div>
              <.input
                phx-hook="messageCounter"
                field={@form[:message]}
                type="textarea"
                label={raw(~c"Message <span class='text-red-500'>*</span>")}
                required
                wrapper_class="space-y-2"
                placeholder="Write your message here"
                input_class="w-full p-3 border border-gray-300 rounded-lg min-h-[120px] resize-y"
              />
              <div class="text-sm text-gray-500 text-right" phx-update="ignore" id="kkkkk">
                <span class="count font-medium">0</span>
                <span class="text-gray-600"> message(s) • </span>
                <span class="remainder font-medium">160</span>
                <span class="text-gray-600"> characters remaining</span>
              </div>
            </div>
            <:actions>
              <div class="mt-8 flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:justify-between sm:items-center">
                <%= if !@loader do %>
                  <div class="flex space-x-3">
                    <.button
                      type="button"
                      onclick="history.back()"
                      class="px-6 py-2.5 rounded-lg transition-colors"
                    >
                      Back
                    </.button>
                    <.button
                      type="submit"
                      phx-disable-with="Sending..."
                      class="px-6 py-2.5 rounded-lg shadow-sm transition-all"
                    >
                      Schedule SMS
                    </.button>
                  </div>
                <% else %>
                  <.button
                    type="button"
                    class="px-6 py-2.5 rounded-lg shadow-sm transition-all"
                    disabled
                  >
                    Schedule...
                  </.button>
                <% end %>
              </div>
            </:actions>
          </.simple_form>
        </div>
      </div>
    </div>
  </div>
</div>
