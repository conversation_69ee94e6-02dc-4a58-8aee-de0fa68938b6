defmodule App.Service.Export.CsvUsers do
  @moduledoc false
  alias App.Service.Table.Users

  alias App.Service.Export.Functions

  @headers [
    "CREATED AT",
    "EMAIL",
    "FIRST NAME",
    "LAST NAME",
    "MOBILE NUMBER",
    "STATUS"
  ]

  def index(payload) do
    Users.export(payload)
    |> Stream.map(
      &[
        &1.inserted_at,
        &1.email,
        &1.first_name,
        &1.last_name,
        &1.mobile,
        Functions.table_numeric_status(&1.status)
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Users"],
              ["", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
