defmodule App.Service.Export.ExcelApiLogsService do
  @moduledoc false

  alias Elixlsx.{Workbook, Sheet}

  alias App.Service.Logs.ApiLogs
  alias App.Service.Export.Functions
  alias Log.Service.Export.ExtraData

  def index(payload) do
    data_call =
      ApiLogs.export(payload)
      |> List.flatten()

    dates =
      Enum.map(data_call, & &1.inserted_at)
      |> Enum.filter(&(!is_nil(&1)))

    start_date =
      Functions.empty_check?(payload["start_date"]) ||
        try do
          Enum.max(dates)
        rescue
          _ -> ""
        end

    end_date =
      Functions.empty_check?(payload["end_date"]) ||
        try do
          Enum.min(dates)
        rescue
          _ -> ""
        end

    reports(data_call, start_date, end_date)
    |> content()
  end

  def reports(posts, start_date, end_date) do
    rows =
      posts
      |> Enum.map(&ExtraData.api_logs_service_row/1)

    %Workbook{
      sheets: [
        %Sheet{
          name: "API Logs",
          rows:
            [
              [["API Logs", align_horizontal: :center, bold: true]],
              [["FROM #{start_date} TO #{end_date}", align_horizontal: :center, bold: true]],
              ["", "", "", "", "", "", ""],
              ExtraData.api_logs_service_header()
            ] ++ rows,
          merge_cells: [{"A1", "H1"}, {"A2", "H2"}]
        }
      ]
    }
  end

  def content(data) do
    Elixlsx.write_to_memory(data, "api_logs_service_#{:os.system_time()}.xlsx")
    |> elem(1)
    |> elem(1)
  end
end
