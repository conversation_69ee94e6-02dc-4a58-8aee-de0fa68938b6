defmodule AppWeb.Auth.Schedule.MulticastLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias Notification.{Sms, Notification.SmsLogs}

  alias App.{
    Service.Functions.SMS.MulticastUpload,
    Settings,
    Notifications.ServerCalls
  }

  alias Notification.MessagesDrafts

  @impl true
  def mount(_params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("multicast_sms-view", assigns.permissions) && !is_nil(assigns.client) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Multicast SMS", "Accessed Multicast SMS Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      changeset = MessagesDrafts.change_sms_logs_upload()

      menu_list = [
        %{
          "position" => 1,
          "name" => "Upload a File"
        },
        %{
          "position" => 2,
          "name" => "Verify Details"
        }
      ]

      socket =
        assign(socket, maker_checker: false)
        |> assign(process: 1)
        |> assign(min_date: Date.utc_today() |> Date.to_iso8601())
        |> assign(:senders, Settings.get_senders_by_client_id!(assigns.client.id))
        |> assign_form(changeset)
        |> assign(menu_list: menu_list)
        |> assign(loader: false)
        |> allow_upload(:file, accept: ~w(.csv .xlsx), max_entries: 1)
        |> assign(:attachment, nil)

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Multicast SMS", "Accessed Multicast SMS Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_event(target, params, socket), do: handle_event_switch(target, params, socket)

  defp handle_event_switch(target, params, socket) do
    case target do
      "proceed_file" ->
        proceed_file(socket)

      "cancel_upload" ->
        cancel_upload(socket)

      "validate" ->
        {:noreply, validate(params, socket)}

      "save" ->
        upload(params, socket)

      "view_valid_records" ->
        view_valid_records(socket)

      "view_invalid_records" ->
        view_invalid_records(socket)

      "cancel-entry" ->
        cancel_upload(params, socket)

      "close_modal" ->
        {
          :noreply,
          socket
          |> assign(:page_title, "Multicast Upload")
          |> assign(:live_action, :index)
        }
    end
  end

  defp validate(%{"sms_logs" => params} = _attrs, socket) do
    filename = %{
      "filename" =>
        try do
          Enum.at(socket.assigns.uploads.file.entries, 0).client_name
        rescue
          _ -> ""
        end
    }

    new_params = Map.merge(params, filename)

    changeset =
      MessagesDrafts.change_sms_logs_upload(new_params)
      |> Map.put(:action, :validate)

    assign_form(socket, changeset)
    |> assign(:new_params, new_params)
  end

  def cancel_upload(%{"ref" => ref}, socket) do
    {:noreply, cancel_upload(socket, :file, ref)}
  end

  def cancel_upload(socket) do
    {
      :noreply,
      assign(socket, process: 1)
      |> assign(data: nil)
    }
  end

  def proceed_file(socket) do
    send(self(), :process_file_entries)

    {
      :noreply,
      assign(socket, loader: true)
    }
  end

  def upload(params, socket) do
    path = LiveFunctions.priv_path("/uploads/scheduled_sms/multicast")

    files =
      consume_uploaded_entries(socket, :file, fn meta, entry ->
        file_name = "/#{Path.rootname(entry.client_name)}.#{LiveFunctions.ext(entry)}"
        dest = Path.join(path, file_name)

        if File.exists?(path <> "/") do
          File.cp(meta.path, dest)
          {:ok, dest}
        else
          File.mkdir_p(path <> "/")
          File.cp_r(meta.path, dest)
          {:ok, dest}
        end
      end)

    send(self(), {:extract_file, Enum.at(files, 0), params})

    {
      :noreply,
      assign(socket, process: 0)
      |> assign(loading_status: "Extracting Data")
      |> assign(attachment: List.first(files))
    }
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      {:extract_file, file, params} ->
        extract_file_file(file, params, socket)

      :process_file_entries ->
        process_file_entries(socket)
    end
  end

  defp extract_file_file(file, params, socket) do
    MulticastUpload.execute(socket, file, params)
    |> case do
      {:error, :start, message} ->
        LiveFunctions.sweet_alert(socket, message, "error")

        {
          :noreply,
          socket
          |> assign(process: 1)
        }

      {:ok, target, data, message} ->
        LiveFunctions.sweet_alert(socket, message, target)

        {
          :noreply,
          socket
          |> assign(proceed: target)
          |> assign(data: data)
          |> assign(process: 2)
        }
    end
  end

  defp process_file_entries(socket) do
    attrs = socket.assigns.new_params

    value =
      CustomFunctions.get_value_from_select_data2(attrs, socket.assigns.senders, "sender_id")[
        "sender"
      ]

    attrs =
      Map.merge(attrs, %{
        "sender" => value,
        "client_id" => socket.assigns.client.id
      })

    changeset = Notification.Sms.change_sms_bulk_logs(%SmsLogs{}, attrs)

    if changeset.valid? do
      [_, file_path] = String.split(socket.assigns.attachment, "priv")

      Map.put(attrs, "file_path", file_path)
      |> Map.put("rows", socket.assigns.data.total)
      |> send_request(socket)
    else
      LiveFunctions.sweet_alert(socket, "Make sure all fields are filled", "error")
    end
  end

  # defp validate_params(params) do
  #   ParamValidator.validate(
  #     params,
  #     %{
  #       "sender_id" => :string,
  #       "source" => :string
  #     }
  #   )
  # end

  defp view_valid_records(%{assigns: assigns} = socket) do
    {
      :noreply,
      assign(socket, :record_title, "Valid Records")
      |> assign(live_action: :view)
      |> assign(:records, assigns.data.success_list)
    }
  end

  defp view_invalid_records(%{assigns: assigns} = socket) do
    {
      :noreply,
      assign(socket, :record_title, "Invalid Records")
      |> assign(live_action: :view)
      |> assign(:records, assigns.data.fail_list)
    }
  end

  def send_request(attrs, socket) do
    ServerCalls.api_server(
      "scheduled_multicast",
      Map.put(attrs, "user_id", socket.assigns.current_user.id)
    )
    |> case do
      {:ok, body} ->
        if body["status"] do
          changeset = Sms.change_sms_logs(%SmsLogs{}, %{})

          LiveFunctions.sweet_alert(socket, body["message"], "success")
          |> assign(loader: false)
          |> assign_form(changeset)
          |> assign(process: 1)
        else
          LiveFunctions.sweet_alert(socket, body["message"], "error")
          |> assign(loader: false)
          |> assign(process: 1)
        end

      {:error, message} ->
        LiveFunctions.sweet_alert(socket, message, "error")
        |> assign(loader: false)
        |> assign(process: 1)
    end
  end

  def assigns_form(socket, %Ecto.Changeset{} = changeset) do
    assign(socket, :form, to_form(changeset))
  end
end
