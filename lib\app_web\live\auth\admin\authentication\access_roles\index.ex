defmodule AppWeb.Authenticated.Authentication.AccessRoleLive.Index do
  @moduledoc false
  use AppWeb, :live_view

  alias App.Services.Table.AccessRoles, as: TableQuery

  alias App.{Roles.AccessRoles, Service.Functions.AccessRoleLive}
  alias App.Roles

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("access_roles-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Access Role Settings", "Accessed Access Role Settings Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      filter_data = %{
        "name" => nil,
        "status" => nil,
        "start_date" => nil,
        "end_date" => nil
      }

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> assign(:role, params["role"])
        |> assign(maker_checker: false)
        |> assign(info_wording: "Yes")
        |> assign(info_message: "")
        |> assign(confirmation_model: false)
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(new_edit_modal: false)
        |> assign(live_socket_id: session["live_socket_id"])
        |> assign(showFilter: false)
        |> assign(form: useform(filter_data))
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()
        |> LivePageControl.maker_checker_status()

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Access Role Settings", "Accessed Access Role Settings Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok, push_navigate(socket, to: ~p"/dashboard", replace: true)}
    end
  end

  defp useform(data) do
    to_form(
      data,
      as: "filter"
    )
  end

  @impl true
  def handle_params(params, _url, socket) do
    socket.endpoint.broadcast_from!(
      self(),
      "nav:" <> socket.assigns.live_socket_id,
      "change_nav",
      %{
        uri_path:
          push_navigate(socket,
            to: "/admin/settings/roles/access/redirect/#{socket.assigns.current_user.role_id}"
          )
      }
    )

    send(self(), {:get_list, params})
    socket = assign(socket, :params, params)

    {
      :noreply,
      LivePageControl.order_by_composer(socket, params)
      |> apply_action(socket.assigns.live_action, params)
    }
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "Access Roles")
    |> assign(:user, %AccessRoles{})
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:status, params} ->
        record_status(socket, params)

      {:get_list, params} ->
        list(socket, params)

      {:change_record_status, params} ->
        record_status(socket, params)

      {:search_records, params} ->
        send(self(), {:get_list, %{"filter" => params}})
        noreply(socket)

      {FormLive, _, %{action: action, param: params}} ->
        if action == "dismiss" do
          {:noreply, assign(socket, :maker_checker, false)}
        else
          send(self(), {:status, Map.merge(params, action)})
          {:noreply, assign(socket, :info_wording, "Processing")}
        end

      {RolesContext, _, %{permissions: permissions}} ->
        if Audit.page_access("manage_access_roles", permissions) do
          {:noreply, socket}
        else
          {:noreply, push_navigate(socket, to: ~p"/dashboard", replace: true)}
        end

      {AppWeb.LiveFunctions, message} ->
        send(self(), {:get_list, %{}})

        {
          :noreply,
          put_flash(socket, :info, message)
          |> assign(:page_title, "Listing access roles")
          |> assign(:live_action, :index)
          |> assign(:record, %AccessRoles{})
        }

      {AppWeb.Component.DateFilter, {:filtered, data}} ->
        send(self(), {:get_list, %{"filter" => data}})

        {
          :noreply,
          socket
          |> assign(:live_action, :index)
          |> assign(:page_title, "Access Roles")
        }
    end
  end

  # @impl true
  # def handle_info(
  #       {AppWeb.Settings.AccessRoleLive.FormComponent, {:saved, access_roles, message}},
  #       socket
  #     ) do
  #   send(self(), {:get_list, socket.assigns.params})
  #   LiveFunctions.sweet_alert(socket, message, "success")

  #   {:noreply,
  #    assign(socket, :access_roles, access_roles)
  #    |> assign(page_title: "Listing Access Roles")
  #    |> assign(:live_action, :index)}
  # end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  defp handle_event_switch(target, value, %{assigns: assigns} = socket) do
    case target do
      "status" ->
        status = if value["status"] == "0", do: "disable", else: "enable"

        if assigns.maker_checker_status do
          form = [%{type: "textarea", label: "Remarks", name: "remarks", value: ""}]

          {
            :noreply,
            assign(socket, :maker_checker, true)
            |> assign(:form, form)
            |> assign(:no_click, false)
            |> assign(:form_title, "User Description")
            |> assign(:info_message, "Are you sure you want to #{status} Access Role?")
            |> assign(:info_modal_param, Map.merge(value, %{"action" => status}))
            |> assign(:info_wording, String.capitalize(status))
          }
        else
          {
            :noreply,
            assign(socket, :confirmation_model, true)
            |> assign(:confirmation_model_params, Map.merge(value, %{"action" => status}))
            |> assign(:confirmation_model_text, "You want to #{String.capitalize(status)} role")
            |> assign(:confirmation_model_agree, "change_record_status")
          }
        end

      "change_record_status" ->
        LiveFunctions.change_record_status(value, socket)

      "add_record" ->
        socket =
          socket
          |> assign(:page_title, "New access role")
          |> assign(:new_edit_modal, true)
          |> assign(:live_action, :new)
          |> assign(:record, %AccessRoles{})

        {:noreply, socket}

      "filter_change" ->
        assign(socket, form: useform(value["filter"]))
        |> noreply()

      "filter" ->
        value = if socket.assigns.showFilter == true, do: false, else: true

        assign(socket, :showFilter, value)
        |> noreply()

      "reset_filter" ->
        socket
        |> assign(form: useform(%{}))
        |> noreply()

      "search" ->
        send(self(), {:get_list, value})

        noreply(
          socket
          |> assign(checked_ids: [])
          |> assign(param_list: [])
        )

      "export" ->
        LiveFunctions.export_records(socket, value, "access_service", socket.assigns.params)

      "close_model" ->
        socket =
          socket
          |> assign(:page_title, "Listing access roles")
          |> assign(:new_edit_modal, false)
          |> assign(:view_modal, false)
          |> assign(:live_action, :index)

        {:noreply, socket}

      "edit_record" ->
        socket =
          socket
          |> assign(:page_title, "Edit access role")
          |> assign(:new_edit_modal, true)
          |> assign(:live_action, :edit)
          |> assign(:record, Roles.get_access_roles!(value["id"]))

        {:noreply, socket}

      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :confirmation_model, false)
        }

      "refresh_table" ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :data_loader, true)
        |> noreply()

      _ ->
        {:noreply, socket}
    end
  end

  defp list(socket, params) do
    data =
      LivePageControl.create_table_params(socket, params)
      |> Map.merge(%{"role" => socket.assigns.role})

    {
      :noreply,
      assign(socket, :data, TableQuery.index(data))
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end

  defp record_status(%{assigns: assigns} = socket, params) do
    # status = if params["status"] == "0", do: "disabled", else: "enabled"

    AccessRoleLive.status_changer(
      socket,
      Roles.get_access_roles_by_id?(params["id"]),
      Map.put(
        params,
        "updated_by",
        assigns.current_user.id
      )
    )
    |> case do
      {:ok, message, _maker_checker} ->
        Process.send_after(self(), :get_list, 10)

        {
          :noreply,
          assign(socket, :confirmation_model, false)
          |> assign(confirmation_model_icon: "exclamation_circle")
          |> assign(:maker_checker, false)
          |> assign(info_wording: "Yes")
          |> LiveFunctions.sweet_alert(message, "success")
        }

      {:error, error} ->
        {
          :noreply,
          assign(socket, :confirmation_model, false)
          |> assign(confirmation_model_icon: "exclamation_circle")
          |> assign(:maker_checker, false)
          |> assign(:info_wording, "Yes")
          |> LiveFunctions.sweet_alert(error, "error")
        }
    end
  end
end
