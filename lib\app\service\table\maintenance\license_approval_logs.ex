defmodule App.Service.Table.LicenseApprovalLogs do
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false

  alias App.Licenses.LicenseApprovalLogs

  alias App.{Repo, Roles}

  # assigns = %{}
  # params = %{"filter" => %{}, "order_by" => "-inserted_at"}
  # records = App.Service.Table.LicenseApprovalLogs.export(assigns, params)
  # IO.inspect(records)

  @pagination [page_size: 10]
  def index(assigns, params) do
    compose_query(params, assigns)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
    |> (fn %{entries: entries} = result ->
          Map.put(
            result,
            :entries,
            Enum.map(
              entries,
              &Map.merge(&1, %{role_name: Roles.get_role_name_by_id!(&1.role_id)})
            )
          )
        end).()
  end

  def export(assigns, params) do
    compose_query(params, assigns)
    |> Repo.all()
  end

  def compose_query(params, _assigns) do
    LicenseApprovalLogs
    |> join(:left, [a], b in assoc(a, :license_mapping))
    |> join(:left, [a], c in assoc(a, :user))
    |> where([a], a.license_mapping_id == ^params["id"])
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"is_approved", value}, query when byte_size(value) > 0 ->
        where(query, [a], a.is_approved == ^(value == "true"))

      {"status", value}, query when byte_size(value) > 0 ->
        where(query, [a], a.status == type(^value, :integer))

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a, b, c], %{
      id: a.id,
      is_approved: a.is_approved,
      status: a.status,
      record_name: b.record_name,
      user_email: c.email,
      role_id: c.role_id,
      user_name: fragment("concat(?, ' ', ?)", c.first_name, c.last_name),
      inserted_at: a.inserted_at,
      updated_at: a.updated_at
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      fragment("lower(?) LIKE lower(?)", b.record_name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", c.email, ^search_term) or
        fragment(
          "lower(concat(?, ' ', ?)) LIKE lower(?)",
          c.first_name,
          c.last_name,
          ^search_term
        )
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
