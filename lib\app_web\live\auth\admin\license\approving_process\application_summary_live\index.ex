defmodule AppWeb.Auth.ApplicationSummaryLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias App.{Licenses, Utilities, LicenseConditions}
  alias App.Service.LicenceApplications.Functions
  alias AppWeb.Components.RichTextEditorComponent

  alias App.Files.UploadedFile

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("license_details-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Application Summary", "Accessed Application Summary Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      socket =
        socket
        |> assign(data_loader: true)
        |> assign(params: params)
        |> assign(edited: "hide")
        |> assign(show_modal: "hide")
        |> assign(reject_document_form: "hide")
        |> assign(field_name: "")
        |> assign(field_id: "")
        |> assign(submit_for_review: "hide")
        |> assign(confirmation_model: false)
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(session_id: session["live_socket_id"])
        |> assign(:uploaded_files, [])
        |> assign(
          :upload_form,
          to_form(Licenses.change_upload_file(%UploadedFile{}), as: "upload")
        )
        |> allow_upload(
          :file,
          accept: ~w(.jpg .jpeg .png .pdf .docx .doc .xlsx),
          max_entries: 1,
          max_file_size: 10_000_000
        )

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Application Summary", "Accessed Application Summary Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {
        :ok,
        push_navigate(
          LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
          to: ~p"/dashboard"
        )
      }
    end
  end

  defp assigns_summary(socket, %{show_summary: false}, _associates), do: socket

  defp assigns_summary(socket, record, associates) do
    assoc =
      Enum.map(
        associates,
        fn associate ->
          %{
            "id" => associate.id,
            "name" => associate.license.name
          }
        end
      )

    editors = %{
      "id" => "editor#{record.id}",
      "value" => record.summary_user_draft,
      "associates" => assoc
    }

    # First push events for the record itself
    socket =
      socket
      |> push_event("rich_text_data", %{
        "id" => "editor#{record.id}",
        "value" => record.summary_user_draft
      })
      |> push_event("rich_text_status", %{"id" => "editor#{record.id}", "status" => false})
      |> assign(editors: editors)
      |> assign(edited_html: editors)
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_data, params})

    {
      :noreply,
      socket
      |> assign(:params, params)
      |> apply_action(socket.assigns.live_action, params)
    }
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "Application Summary")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      {:get_data, _params} ->
        get_update(socket)

      {:process_approval, params} ->
        process_approval(socket, params)

      {:upload_file, file} ->
        upload_file(socket, file)

      {AppWeb.LiveFunctions, message} ->
        send(self(), {:get_data, %{}})

        {
          :noreply,
          socket
          |> put_flash(:info, message)
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }

      {AppWeb.LicenceDetailsLive.ConditionComponent, {:saved, summary_user_draft, msg}} ->
        socket
        |> assign(:live_action, :index)
        |> LiveFunctions.sweet_alert(msg, "success")
        |> assign(:page_title, "Application Summary")
        |> push_event("rich_text_data", %{
          "id" => socket.assigns.editors["id"],
          "value" => summary_user_draft
        })
        |> noreply()

      {AppWeb.LicenceDetailsLive.ConditionComponent, {:error, msg}} ->
        socket
        |> assign(:live_action, :index)
        |> LiveFunctions.sweet_alert(msg, "error")
        |> assign(:page_title, "Application Summary")
        |> noreply()

      {App.CustomContext, "update_review", _} ->
        review_data = App.LicenseReviews.query_field_label_for_review(socket.assigns.record.id)

        socket
        |> assign(review: review_data)
        |> assign(display: Enum.map(review_data, fn x -> x.attention_field end))
        |> noreply()

      :navigate_to_main ->
        redirect(socket, to: ~p"/license/applications/New")
        |> noreply()
    end
  end

  def handle_event("cancel-entry", params, socket) do
    cancel_upload(params, socket)
  end

  def handle_event("export_word", %{"file_name" => file_name}, socket) do
    # Send event to JS hook to export Word document
    {:noreply, push_event(socket, "export_word", %{file_name: file_name})}
  end

  def handle_event("edit_form", %{"id" => id}, socket) do
    socket
    |> push_event("rich_text_status", %{"id" => id, "status" => true})
    |> assign(edited: "show")
    |> noreply()
  end

  def handle_event("export_pdf", %{"file_name" => file_name}, socket) do
    # Send event to JS hook to export PDF document
    {:noreply, push_event(socket, "export_pdf", %{file_name: file_name})}
  end

  def handle_event("export_error", %{"format" => format, "message" => message}, socket) do
    # Handle export errors (could show a notification)
    {:noreply, put_flash(socket, :error, "Failed to export #{format}: #{message}")}
  end

  def cancel_upload(%{"ref" => ref}, socket) do
    {:noreply, cancel_upload(socket, :file, ref)}
  end

  def upload(params, socket) do
    new_path = LiveFunctions.static_path("/uploads/approval_docs")

    file =
      consume_uploaded_entries(
        socket,
        :file,
        fn %{path: path}, entry ->
          file_name = "/#{Path.rootname(entry.client_name)}.#{LiveFunctions.ext(entry)}"
          dest = Path.join(new_path, file_name)

          if File.exists?(new_path <> "/") do
            File.cp(path, dest)
            {:ok, dest}
          else
            File.mkdir_p(new_path <> "/")
            File.cp_r(path, dest)
            {:ok, dest}
          end
        end
      )

    send(self(), {:upload_file, Enum.at(file, 0)})

    {
      :noreply,
      socket
      |> assign(loader: true)
    }
  end

  defp upload_file(socket, file) do
    Licenses.upload_level_file(socket, file)
    |> case do
      {:ok, _} ->
        socket
        |> assign(loader: false)
        |> success_message("File Successfully Uploaded.")

      {:error, error} ->
        socket
        |> assign(loader: false)
        |> error_message(error)
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, %{assigns: _assigns} = socket) do
    colored_fields =
      Enum.map(
        socket.assigns.display,
        fn field ->
          "<span style=\"color:red;\">#{field}</span>"
        end
      )

    display_field = Enum.join(colored_fields, "<br>")

    case target do
      "view_licence" ->
        {
          :noreply,
          assign(socket, :live_action, :view_licence_form)
          |> assign(:page_title, "License Form")
          |> assign(:licence_form, ~p"/license/template/#{value["id"]}")
        }

      "view_details" ->
        {
          :noreply,
          push_navigate(socket,
            to: ~p"/license/applications/details/#{socket.assigns.params["id"]}"
          )
        }

      "view_condition" ->
        {
          :noreply,
          assign(socket, :live_action, :view_conditions)
          |> assign(:page_title, "License Conditions")
        }

      "view_assoc_id" ->
        {
          :noreply,
          assign(socket, :live_action, :assoc)
          |> assign(:page_title, "#{value["name"]} Associated")
          |> assign(:assoc_id, value["assoc_id"])
        }

      "view_licence_summary" ->
        {
          :noreply,
          assign(socket, :live_action, :view_licence_form)
          |> assign(:page_title, "Licence Form")
          |> assign(:licence_form, ~p"/license/template/#{value["id"]}")
        }

      "update_rich_text" ->
        update_rich_text_validate(socket, value)

      "close_model" ->
        {:noreply, assign(socket, :live_action, :index)}

      "view_associate" ->
        socket =
          socket
          |> assign(:page_title, "View Associate")
          |> assign(:live_action, :view_associate)
          |> assign(:associate, ~p"/applications/association/#{value["id"]}")

        {:noreply, socket}

      "view_document" ->
        socket =
          socket
          |> assign(:page_title, "View Document")
          |> assign(:live_action, :view_document)
          |> assign(:doc_name, value["doc_name"])
          |> assign(
            :document,
            String.replace(value["path"], Path.join(:code.priv_dir(:app), "static/"), "")
          )

        {:noreply, socket}

      "approve_application" ->
        status =
          case value["activity"] do
            "decline" -> "decline"
            "lc_approval" -> "lc_approval"
            "approve" -> "approve"
          end

        result =
          "Are you sure you want to #{if value["activity"] == "lc_approval", do: "approve", else: status}?<br>#{display_field}"

        model_call =
          cond do
            value["activity"] == "decline" -> :confirm_with_des
            value["status"] == "1" -> :confirmation_amount
            true -> :confirmation_with_comments
          end

        {
          :noreply,
          assign(socket, :live_action, model_call)
          |> assign(:confirmation_model_text, result)
          |> assign(
            :confirmation_model_params,
            Map.merge(
              value,
              %{
                "action" => status
              }
            )
          )
          |> assign(:confirmation_model_agree, "approve")
        }

      "remove_condition" ->
        {
          :noreply,
          assign(socket, :live_action, :confirm)
          |> assign(
            :confirmation_model_text,
            "Are you sure you want to remove this condition: #{value["name"]}?"
          )
          |> assign(
            :confirmation_model_params,
            Map.merge(
              value,
              %{
                "action" => "remove"
              }
            )
          )
          |> assign(:confirmation_model_agree, "condition_remove")
        }

      "condition_remove" ->
        attrs = Jason.decode!(value["params"])

        LicenseConditions.remove_condition(socket, attrs)
        |> case do
          {:ok, _record} ->
            socket
            |> assign(loader: false)
            |> success_message("Condition Removed.")

          {:error, error} ->
            socket
            |> assign(loader: false)
            |> error_message(error)
        end

      "approve" ->
        send(self(), {:process_approval, value})

        socket
        |> assign(confirmation_model_title: "")
        |> assign(:confirmation_model_icon, "loading")
        |> assign(
          :confirmation_model_text,
          "Processing Approval."
        )
        |> noreply()

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :live_action, :index)
          |> assign(reset_user_form: "hide")
        }

      "close_modal" ->
        close_modal(value, socket)

      "editable_update" ->
        editable_update(value, socket)

      "editable_blur" ->
        editable_blur(value, socket)

      "save_edits" ->
        save_edits(value, socket)

      "validate_file" ->
        validate(value, socket)

      "cancel_entry" ->
        cancel_entry(value, socket)

      "upload" ->
        upload(value, socket)
    end
  end

  @impl true
  def cancel_entry(%{"ref" => ref}, socket) do
    {:noreply, cancel_upload(socket, :path, ref)}
  end

  @impl true
  def editable_update(%{"html" => html}, socket) do
    {:noreply, assign(socket, edited_html: html, edited: "show")}
  end

  def editable_blur(%{"html" => html}, socket) do
    {:noreply, assign(socket, edited_html: html, edited: "show")}
  end

  def close_modal(value, socket) do
    {
      :noreply,
      socket
      |> assign(show_modal: "hide")
      |> assign(reject_document_form: "hide")
    }
  end

  def validate(value, socket) do
    {:noreply, socket}
  end

  def update_rich_text_validate(socket, value) do
    socket
    |> assign(:edited_html_id, value["id"])
    |> assign(:edited_html, value)
    |> noreply()
  end

  @impl true
  def save_edits(params, socket) do
    case App.Licenses.update_application(
           String.replace(socket.assigns.edited_html["id"], "editor", ""),
           socket,
           socket.assigns.edited_html["value"]
         ) do
      {:ok, %{updates: record}} ->
        socket
        |> LiveFunctions.sweet_alert("Summary Successfully Updated.", "success")
        |> push_event("rich_text_status", %{
          "id" => socket.assigns.edited_html["id"],
          "status" => false
        })
        |> assign(:edited, "hide")
        |> noreply()

      {:error, changeset} ->
        socket
        |> LiveFunctions.sweet_alert("Failed to save.", "warning")
        |> push_event("rich_text_status", %{
          "id" => socket.assigns.edited_html["id"],
          "status" => false
        })
        |> assign(:edited, "hide")
        |> noreply()
    end
  end

  def get_update(socket) do
    {record, associates} = Licenses.get_application_w_assoc(socket.assigns.params["id"])
    license_data = Licenses.get_license_data(record.license_id)

    App.LicenseReviews.subscribe(record.id)

    form =
      to_form(
        %{
          "license_data" => license_data
        },
        as: "details_views"
      )

    review_data = App.LicenseReviews.query_field_label_for_review(record.id)

    socket
    |> assign(review: review_data)
    |> assign(display: Enum.map(review_data, fn x -> x.attention_field end))
    |> assign(files: App.Files.list_uploaded_files(record.id))
    |> assign(submit_for_review_list: Enum.map(review_data, &to_string(&1.id)))
    |> assign(license_data: license_data)
    |> assign(query_field_d: App.LicenseReviews.query_field_d(record.license_id))
    |> assign(field_data: record.data)
    |> assign(assoc: Licenses.get_application_w_assoc_representative(socket.assigns.params["id"]))
    |> assign(record: record)
    |> assign(show_summary: record.show_summary)
    |> assign(
      current_level:
        Utilities.get_current_level_role_by_category(record.status, record.license.categories_id)
    )
    |> assign(db_files: Licenses.get_uploaded_files_by_application!(record.id))
    |> assign(data_loader: false)
    |> assign(form: form)
    |> assign(associates: associates)
    |> assign(
      approval_level:
        Utilities.get_level_by_category_role!(
          record.license.categories_id,
          record.status,
          socket.assigns.current_user.role_id
        )
    )
    |> assigns_summary(record, associates)
    |> noreply()
  end

  defp process_approval(socket, params) do
    attrs = Jason.decode!(params["params"])

    Functions.index(
      socket,
      Map.merge(
        attrs,
        %{
          "reason" => params["reason"],
          "amount_paid" => params["amount_paid"],
          "comments" => params["comments"]
        }
      ),
      socket.assigns.record,
      socket.assigns.associates,
      attrs["action"]
    )
    |> case do
      {:ok, _user} ->
        if attrs["action"] == "decline" do
          socket
          |> assign(loader: false)
          |> success_message("Application Successfully Declined.")
        else
          socket
          |> assign(loader: false)
          |> success_message("Application Successfully Approved.")
        end

      {:error, error} ->
        socket
        |> assign(loader: false)
        |> error_message(error)
    end
  end

  def success_message(%{assigns: _assigns} = socket, message) do
    send(self(), {:get_data, socket.assigns.params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "success"
      )
    }
  end

  def error_message(%{assigns: _assigns} = socket, message) do
    send(self(), {:get_data, socket.assigns.params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "error"
      )
    }
  end

  def error_to_string(:too_large), do: "Too large"
  def error_to_string(:not_accepted), do: "You have selected an unacceptable file type"
  def error_to_string(:too_many_files), do: "You have selected too many files"
end
