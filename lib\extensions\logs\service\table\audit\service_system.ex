defmodule App.Service.Logs.SystemLogs do
  @moduledoc false
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false
  alias App.{Accounts}
  alias Logs.LogRepo, as: Repo

  alias Logs.Audit.System

  @pagination [page_size: 10]

  def index(params) do
    compose_query(params)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
    |> (fn %{entries: entries} = result ->
          Map.put(
            result,
            :entries,
            Enum.map(entries, &Map.put(&1, :user, Accounts.get_username_as_email_by_id(&1.user)))
          )
        end).()
  end

  def export(params) do
    compose_query(params)
    |> Repo.all()
    |> Enum.map(&Map.put(&1, :user, Accounts.get_username_as_email_by_id(&1.user)))
  end

  def compose_query(params) do
    System
    |> compose_select()
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(String.trim(value)))

      {"ip_address", value}, query when byte_size(value) > 0 ->
        where(query, [a], a.ip_address == ^value)

      {"status", value}, query when byte_size(value) > 0 ->
        where(query, [b], b.status == type(^value, :integer))

      {"start_date", value}, query when byte_size(value) > 0 ->
        naive_datetime = NaiveDateTime.from_iso8601!("#{value} 00:00:00")
        where(query, [s, c, d], s.inserted_at >= ^naive_datetime)

      {"end_date", value}, query when byte_size(value) > 0 ->
        naive_datetime = NaiveDateTime.from_iso8601!("#{value} 23:59:59")
        where(query, [s, c, d], s.inserted_at <= ^naive_datetime)

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a, b, c], %{
      id: a.id,
      service: a.service,
      action: a.action,
      ip_address: a.ip_address,
      narration: a.narration,
      user: a.user_id,
      inserted_at: a.inserted_at
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      fragment("lower(?) LIKE lower(?)", a.narration, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.action, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.service, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
