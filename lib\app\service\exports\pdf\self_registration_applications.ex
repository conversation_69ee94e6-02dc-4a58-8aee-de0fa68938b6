defmodule App.Service.Export.SelfRegistrationApplicationsServicePdf do
  @moduledoc false

  alias App.Service.Table.SelfRegistration

  alias App.Service.Export.{
    Functions
  }

  def index(assigns, payload) do
    results =
      SelfRegistration.export(assigns, payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "inserted_at" => NaiveDateTime.to_string(data.inserted_at),
          "company_name" => data.company_name,
          "full_name" => data.full_name,
          "email" => data.email,
          "mobile_number" => data.mobile_number,
          "status" => Functions.self_registration_status(data.status)
        }
      end)
      |> Enum.map(fn data ->
        """
          <tr>
            <td style="text-align: center;">{{ inserted_at }}</td>
            <td style="text-align: center;">{{ company_name }}</td>
            <td style="text-align: center;">{{ full_name }}</td>
            <td style="text-align: center;">{{ email }}</td>
            <td style="text-align: center;">{{ mobile_number }}</td>
            <td style="text-align: center;">{{ status }}</td>

          </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/self_registration_applications.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Self Registration Applications",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end
