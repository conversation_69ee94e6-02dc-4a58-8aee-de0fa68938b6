defmodule AppWeb.DashboardLive.Dashboard.Loader do
  @moduledoc false
  use AppWeb, :live_component

  def render(assigns) do
    ~H"""
    <div class="p-4 sm:p-6 lg:p-8 animate-pulse">
      <div class="sm:flex sm:items-center mb-10">
        <div class="sm:flex-auto animate-fadeInTopRight">
          <h1 class="h-3 rounded-full bg-gray-300 w-48"></h1>
        </div>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
          <span class="h-3 rounded-full bg-gray-300 w-48"></span>
        </div>
      </div>

      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <div class="border border-grey-300 shadow rounded-md p-4 w-full animate-fadeInLeft">
          <div class="animate-pulse flex">
            <div class="rounded-full bg-gray-300 h-12 w-12"></div>
            <div class="flex-1 space-y-4 py-1">
              <div class="h-4 bg-gray-300 rounded w-3/4"></div>
              <div class="space-y-2">
                <div class="h-4 bg-gray-300 rounded"></div>
                <div class="h-4 bg-gray-300 rounded w-5/6"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="border border-grey-300 shadow rounded-md p-4 w-full mx-auto animate-fadeInDown">
          <div class="animate-pulse flex">
            <div class="rounded-full bg-gray-300 h-12 w-12"></div>
            <div class="flex-1 space-y-4 py-1">
              <div class="h-4 bg-gray-300 rounded w-3/4"></div>
              <div class="space-y-2">
                <div class="h-4 bg-gray-300 rounded"></div>
                <div class="h-4 bg-gray-300 rounded w-5/6"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="border border-grey-300 shadow rounded-md p-4 w-full mx-auto animate-fadeInDown">
          <div class="animate-pulse flex">
            <div class="rounded-full bg-gray-300 h-12 w-12"></div>
            <div class="flex-1 space-y-4 py-1">
              <div class="h-4 bg-gray-300 rounded w-3/4"></div>
              <div class="space-y-2">
                <div class="h-4 bg-gray-300 rounded"></div>
                <div class="h-4 bg-gray-300 rounded w-5/6"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="border border-grey-300 shadow rounded-md p-4 w-full mx-auto animate-fadeInRight">
          <div class="animate-pulse flex">
            <div class="rounded-full bg-gray-300 h-12 w-12"></div>
            <div class="flex-1 space-y-4 py-1">
              <div class="h-4 bg-gray-300 rounded w-3/4"></div>
              <div class="space-y-2">
                <div class="h-4 bg-gray-300 rounded"></div>
                <div class="h-4 bg-gray-300 rounded w-5/6"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3  xl:grid-cols-4 gap-4">
        <div class="w-full mt-7 xl:col-span-3 lg:col-span-2 grid-cols-1 animate-fadeInLeft">
          <h1 class="w-48 h-2 mt-4 bg-gray-300 rounded-lg mb-4"></h1>
          <div class="w-full h-64 bg-gray-300 rounded-lg"></div>
        </div>
        <div class="w-full mt-7 animate-fadeInRight">
          <h1 class="w-48 h-2 mt-4 bg-gray-300 rounded-lg mb-4"></h1>
          <div class="w-full h-64 bg-gray-300 rounded-lg"></div>
        </div>
      </div>
    </div>
    """
  end
end
