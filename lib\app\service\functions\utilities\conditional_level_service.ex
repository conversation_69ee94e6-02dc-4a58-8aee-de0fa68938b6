defmodule App.Service.ServiceConditionalLevels.Functions do
  alias App.Utilities

  def index(socket, attrs, function \\ "change_status") do
    record = Utilities.get_conditoinal_approval_level!(attrs["id"])

    cond do
      function == "change_status" ->
        Utilities.change_conditional_level_status(socket, attrs, record)
    end
  end

  def create(socket, attrs) do
    Utilities.create_conditonal_level(socket, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end

  def update(socket, record, attrs) do
    Utilities.update_conditional_level(socket, record, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end
end
