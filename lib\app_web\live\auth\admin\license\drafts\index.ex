defmodule AppWeb.Maintenance.LicenceSummaryDraftLive.Index do
  use AppWeb, :live_view
  alias App.Services.Table.Utilities.SummaryDraftService
  alias App.SummaryDrafts.SummaryDraft
  alias App.SummaryDrafts

  @impl true
  def mount(_params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("licence_drafts-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Summary Drafts Configuration", "Accessed Summary Drafts Configuration Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      changeset = SummaryDrafts.change_summary_draft()

      socket =
        assign(socket, data: [])
        |> assign_form(changeset)
        |> assign(data_loader: true)
        |> assign(maker_checker: false)
        |> assign(confirmation_model_title: "Confirm Changes")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(edit_form: true)
        |> assign(info_wording: "Yes")
        |> assign(info_modal_param: "")
        |> assign(selected_item: nil)
        |> assign(selected_item_name: nil)
        |> assign(selected_info: nil)
        |> assign(:changeset, changeset)
        |> assign(display_message: false)
        |> assign(display_message_text: nil)
        |> assign(live_socket_id: session["live_socket_id"])
        |> assign(draft: nil)
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Summary Drafts Configuration", "Accessed Summary Drafts Configuration Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_params(params, url, socket) do
    socket.endpoint.broadcast_from!(
      self(),
      "nav:" <> socket.assigns.live_socket_id,
      "change_nav",
      %{uri_path: URI.parse(url).path}
    )

    if connected?(socket), do: send(self(), {:get_list, params})

    {
      :noreply,
      socket
      |> assign(:params, params)
      |> apply_action(socket.assigns.live_action, params)
    }
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Summary Drafts")
  end

  defp apply_action(socket, :show, _params) do
    socket
    |> assign(:page_title, "View Summary Draft")
  end

  defp apply_action(socket, :done, _params) do
    socket
    |> assign(:page_title, "Success")
  end

  defp apply_action(socket, :confirm, _params) do
    socket
    |> assign(:page_title, "Confirm Changes")
  end

  defp apply_action(socket, action, _params) do
    socket
    |> assign(:page_title, "Summary Drafts - #{action}")
  end

  @impl true
  def handle_info(data, socket) do
    handle_info_switch(socket, data)
  end

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "asc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {:update_record} ->
        save(socket)

      {_record, _info} ->
        send(self(), {:get_list, socket.assigns.params})

        {
          :noreply,
          assign(socket, :live_action, :index)
          |> apply_action(:index, %{})
        }
    end
  end

  @impl true
  def handle_event(target, value, socket) do
    handle_event_switch(target, value, socket)
  end

  def handle_event_switch(target, value, socket) do
    case target do
      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      "refresh_table" ->
        send(self(), {:get_list, socket.assigns.params})

        {:noreply, assign(socket, :data_loader, true)}

      "validate_input" ->
        {:noreply, assign_form(socket, SummaryDrafts.change_summary_draft(value))}

      "update_rich_text" ->
        validate(socket, value)

      "edit" ->
        edit(socket)

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :live_action, :show)
        }

      "continue" ->
        {
          :noreply,
          assign(socket, :live_action, :confirm)
          |> assign(:confirmation_model_text, "Are you sure you want to save these changes?")
          |> assign(:confirmation_model_params, value["licence_drafts"])
          |> assign(:confirmation_model_agree, "update_record")
        }

      "view_entry" ->
        view_summary_draft(socket, value)

      "update_record" ->
        if connected?(socket), do: send(self(), {:update_record})
        {:noreply, assign(socket, :confirmation_model_icon, "loading")}

      _ ->
        {:noreply, socket}
    end
  end

  defp view_summary_draft(socket, value) do
    changeset =
      SummaryDrafts.change_summary_draft(%{"template" => value["data"] || ""})
      |> Map.put(:action, :validate)

    socket =
      assign(socket, :selected_item, value["id"])
      |> assign(:selected_item_name, value["name"])
      |> assign(:selected_info, %{
        head: :text,
        data: "",
        id: "#{value["id"]}",
        full: true
      })
      |> assign(:live_action, :show)
      |> assign(:changeset, changeset)
      |> assign(edit_form: true)
      |> assign(status_checker: value["status"])
      |> push_event("rich_text_status", %{id: "editor", status: false})
      |> push_event("rich_text_data", %{id: "editor", value: value["data"]})

    {:noreply, socket}
  end

  defp list(socket, params) do
    {
      :noreply,
      assign(
        socket,
        :data,
        SummaryDraftService.index(LivePageControl.create_table_params(socket, params))
      )
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end

  defp edit(%{assigns: assigns} = socket) do
    socket =
      if Audit.page_access("licence_drafts-edit", assigns.permissions) do
        draft = SummaryDrafts.get_licence_drafts_preload!(assigns.selected_item)

        Task.start(fn ->
          Audit.system_log_live(
            socket,
            "Enabled edit #{draft.license.name} field",
            "Access",
            %{id: assigns.selected_item},
            "Summary Drafts Configuration",
            socket.assigns.current_user.id
          )
        end)

        assign(socket, edit_form: false)
        |> assign(draft: draft)
        |> push_event("rich_text_status", %{id: "editor", status: true})
      else
        Task.start(fn ->
          Audit.system_log_live(
            socket,
            "Enable licence drafts edit field denied",
            "Access Denied",
            %{id: assigns.selected_item},
            "Summary Drafts Configuration",
            socket.assigns.current_user.id
          )
        end)

        LiveFunctions.sweet_alert(
          socket,
          "You do not have permission to edit this template",
          "error"
        )
      end

    {:noreply, socket}
  end

  defp validate(socket, %{"value" => value}) do
    changeset =
      socket.assigns.draft
      |> SummaryDrafts.change_summary_draft1(%{
        "template" => value
      })
      |> Map.put(:action, :validate)

    {
      :noreply,
      assign(socket, :changeset, changeset)
    }
  end

  defp save(%{assigns: assigns} = socket) do
    record = assigns.draft

    case record |> SummaryDrafts.update_draft(socket.assigns.changeset.changes, socket) do
      {:ok, updated_draft} ->
        Process.send_after(self(), :get_list, 1000)

        message = "#{record.license.name} template was successfully updated"

        {:noreply,
         socket
         |> LiveFunctions.sweet_alert(message, "success")
         |> assign(selected_info: nil)
         |> assign(edit_form: true)
         |> assign(:live_action, :done)
         |> assign(display_message: true)
         |> assign(display_message_text: message)
         |> assign(:confirmation_model_icon, "exclamation_circle")}

      {:error, error} ->
        {:noreply, LiveFunctions.sweet_alert(socket, error, "error")}
    end
  end
end
