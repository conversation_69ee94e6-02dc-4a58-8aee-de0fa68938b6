defmodule App.Licenses.NoticeOfChange do
  use Ecto.Schema
  import Ecto.Changeset

  @columns [
    :data,
    :user_id,
    :company_id,
    :license_id,
    :status
  ]

  schema "notice_of_change" do
    field :data, :map, default: %{}
    field :status, :integer, default: 0

    belongs_to :user, App.Accounts.User
    belongs_to :company, App.Accounts.Company
    belongs_to :license, App.Licenses.License

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(notice_of_change, attrs) do
    notice_of_change
    |> cast(attrs, @columns)
    |> validate_required([:user_id, :company_id, :license_id])
  end
end
