<div class="min-h-screen bg-gray-50 py-8">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header Card -->
    <div class="rounded-lg p-6 mb-6">
      <div class="text-center">
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 mb-4">
          <svg
            class="h-6 w-6 text-indigo-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
            />
          </svg>
        </div>

        <h1 class="text-2xl font-bold text-gray-900 mb-2">
          Add Company Representative
        </h1>

        <p class="text-gray-600 max-w-md mx-auto">
          Search for a representative by NRC number and attach them to the company.
        </p>
      </div>
    </div>
    <!-- Search Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div class="mb-4">
        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
          <svg
            class="h-5 w-5 text-gray-500 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
          Search Representative
        </h2>

        <p class="text-sm text-gray-500 mt-1">Enter the NRC number to find a representative</p>
      </div>

      <.form for={@search_form} phx-submit="search" class="space-y-4">
        <div class="relative">
          <.input
            field={@search_form[:nrc]}
            type="text"
            label={raw(~c"NRC (******/**/*) <span class='text-rose-500'>*</span>")}
            placeholder="e.g., 123456/78/9"
            phx-hook="nrcInputHook"
            pattern="\d{6}/\d{2}/\d"
            class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          />
        </div>

        <div class="flex justify-center">
          <.button
            type="submit"
            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-brand-100  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
          >
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
            Search Representative
          </.button>
        </div>
      </.form>
    </div>
    <!-- Loading State -->
    <div :if={@searching} class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-6">
      <div class="text-center">
        <div class="inline-flex items-center">
          <svg
            class="animate-spin h-8 w-8 text-indigo-600 mr-3"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            >
            </circle>

            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            >
            </path>
          </svg>
          <span class="text-lg text-gray-700 font-medium">Searching for representative...</span>
        </div>
      </div>
    </div>
    <!-- Search Results -->
    <div
      :if={@search_results && length(@search_results) > 0}
      class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"
    >
      <div class="mb-4">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
          <svg
            class="h-5 w-5 text-green-500 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          Search Results
        </h3>

        <p class="text-sm text-gray-500">
          Found <%= length(@search_results) %> representative(s)
        </p>
      </div>

      <div :if={@searched && @search_results && length(@search_results) > 0} class="space-y-3">
        <%= for representative <- @search_results do %>
          <%= if representative.user.company_id do %>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-6">
              <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 mb-4">
                  <svg
                    class="h-8 w-8 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>

                <h3 class="text-lg font-medium text-gray-900 mb-2">
                  Representative already attached to a Company
                </h3>
              </div>
            </div>
          <% else %>
            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-indigo-300 transition-colors duration-200">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div class="flex-shrink-0">
                    <div class="h-12 w-12 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center shadow-lg">
                      <span class="text-white font-bold text-lg">
                        <%= String.first(representative.user.first_name) %> <%= String.first(
                          representative.user.last_name
                        ) %>
                      </span>
                    </div>
                  </div>

                  <div class="flex-1 min-w-0">
                    <h4 class="text-lg font-semibold text-gray-900 truncate">
                      <%= representative.record_name %>
                    </h4>

                    <div class="mt-1 space-y-1">
                      <p class="text-sm text-gray-600 flex items-center">
                        <svg
                          class="h-4 w-4 text-gray-400 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V4a2 2 0 114 0v2m-4 0a2 2 0 104 0m-2 5v6m0 0V9m0 4h.01"
                          />
                        </svg>
                        NRC: <%= representative.data["national_id"] %>
                      </p>

                      <p
                        :if={representative.user.email}
                        class="text-sm text-gray-600 flex items-center"
                      >
                        <svg
                          class="h-4 w-4 text-gray-400 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
                          />
                        </svg>
                        <%= representative.user.email %>
                      </p>

                      <p
                        :if={representative.user.mobile}
                        class="text-sm text-gray-600 flex items-center"
                      >
                        <svg
                          class="h-4 w-4 text-gray-400 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                          />
                        </svg>
                        <%= representative.user.mobile %>
                      </p>
                    </div>
                  </div>
                </div>

                <div class="flex-shrink-0">
                  <.button
                    phx-click="attach"
                    phx-value-id={representative.id}
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-brand-100 transition-all duration-200 hover:shadow-md"
                  >
                    <svg
                      class="-ml-1 mr-2 h-4 w-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                    Attach
                  </.button>
                </div>
              </div>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>
    <!-- No Results -->
    <div
      :if={@searched && @search_results && length(@search_results) == 0}
      class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-6"
    >
      <div class="text-center">
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 mb-4">
          <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>

        <h3 class="text-lg font-medium text-gray-900 mb-2">No representatives found</h3>

        <p class="text-gray-500 max-w-sm mx-auto">
          No representative found with the NRC number "<span class="font-medium"><%= @search_query %></span>".
          Please check the NRC number and try again.
        </p>
      </div>
    </div>
  </div>
</div>
<!-- Confirmation Modal -->
<Model.confirmation_model
  :if={@live_action == :confirm}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
