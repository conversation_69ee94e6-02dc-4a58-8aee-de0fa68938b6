defmodule AppWeb.Login.LoaderComponent do
  use AppWeb, :live_component

  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gradient-to-br from-teal-50 to-teal-100 flex items-center justify-center px-4 py-8">
      <!-- Navbar Skeleton -->
      <.skeleton_navbar />
      <div class="w-full max-w-md bg-white shadow-2xl rounded-2xl overflow-hidden animate-pulse">
        <div class="bg-gray-200 p-6 flex flex-col items-center">
          <div class="h-16 w-32 bg-gray-300 mb-4 rounded"></div>
          <div class="h-6 w-48 bg-gray-300 mb-2 rounded"></div>
          <div class="h-4 w-36 bg-gray-300 rounded"></div>
        </div>

        <div class="p-6 space-y-4">
          <div>
            <div class="h-4 w-24 bg-gray-200 mb-2 rounded"></div>
            <div class="h-10 w-full bg-gray-200 rounded"></div>
          </div>

          <div>
            <div class="h-4 w-24 bg-gray-200 mb-2 rounded"></div>
            <div class="h-10 w-full bg-gray-200 rounded"></div>
          </div>

          <div class="flex justify-between">
            <div class="flex items-center">
              <div class="h-4 w-4 bg-gray-200 rounded mr-2"></div>
              <div class="h-4 w-24 bg-gray-200 rounded"></div>
            </div>
            <div class="h-4 w-24 bg-gray-200 rounded"></div>
          </div>

          <div class="h-10 w-full bg-gray-300 rounded"></div>

          <div class="text-center">
            <div class="h-4 w-48 bg-gray-200 mx-auto rounded"></div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
