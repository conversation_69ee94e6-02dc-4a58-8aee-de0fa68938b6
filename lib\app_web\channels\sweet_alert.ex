defmodule AppWeb.SweetAlertChannel do
  use AppWeb, :channel
  @moduledoc false

  @impl true
  def join("sweet_alert:" <> user_id, payload, socket) do
    {:ok, assign(socket, :channel, "toast:#{user_id}")}
  end

  # Channels can be used in a request/response fashion
  # by sending replies to requests from the client
  @impl true
  def handle_in("ping", payload, socket) do
    {:reply, {:ok, payload}, socket}
  end

  @impl true
  def handle_in("success", payload, socket) do
    Phoenix.PubSub.broadcast(App.PubSub, socket.assigns.channel, payload)
    {:noreply, socket}
  end

  @impl true
  def handle_in("error", payload, socket) do
    Phoenix.PubSub.broadcast(App.PubSub, socket.assigns.channel, payload)
    {:noreply, socket}
  end

  # It is also common to receive messages from the client and
  # broadcast to everyone in the current topic (sweet_alert:lobby).
  @impl true
  def handle_in("shout", payload, socket) do
    broadcast(socket, "shout", payload)
    {:noreply, socket}
  end

  # Add authorization logic here as required.
  defp authorized?(_payload) do
    true
  end
end
