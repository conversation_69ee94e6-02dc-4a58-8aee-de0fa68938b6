defmodule AppWeb.Dashboard.ClientLandingLive do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})
  on_mount({AppWeb.UserAuth, :ensure_authenticated})

  alias App.Licenses

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-amber-50 p-6">
      <div class="max-w-7xl mx-auto">
        <!-- Dashboard Header -->
        <div class="sm:flex sm:items-center mb-10">
          <div class="sm:flex-auto">
            <h1 class="text-3xl font-bold mb-6 text-blue-700">Dashboard</h1>
          </div>

          <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none" phx-update="ignore" id="time2">
            <span phx-hook="LocalTime" id="time" class="text-lg text-amber-600 font-medium"></span>
          </div>
        </div>
        <!-- Client Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg rounded-xl p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-blue-100 text-sm font-medium">My Applications</p>

                <p class="text-3xl font-bold"><%= @total_applications %></p>
              </div>

              <div class="bg-blue-400 rounded-full p-3">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                  <path
                    fill-rule="evenodd"
                    d="M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>

          <div class="bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg rounded-xl p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-green-100 text-sm font-medium">Approved</p>

                <p class="text-3xl font-bold"><%= @approved_count %></p>
              </div>

              <div class="bg-green-400 rounded-full p-3">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>

          <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white shadow-lg rounded-xl p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-yellow-100 text-sm font-medium">Pending</p>

                <p class="text-3xl font-bold"><%= @submitted_count %></p>
              </div>

              <div class="bg-yellow-400 rounded-full p-3">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>

          <div class="bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg rounded-xl p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-red-100 text-sm font-medium">Returned</p>

                <p class="text-3xl font-bold"><%= @declined_count %></p>
              </div>

              <div class="bg-red-400 rounded-full p-3">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
        <!-- Quick Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <div class="bg-white/90 backdrop-blur shadow-xl rounded-lg p-6 border border-blue-100">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>

            <div class="space-y-3">
              <.link
                navigate={~p"/apply"}
                class="flex items-center p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
              >
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
                <span class="text-sm font-medium text-gray-700">New Application</span>
              </.link>

              <.link
                navigate={~p"/client/applications/draft"}
                class="flex items-center p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <div class="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z" />
                    <path
                      fill-rule="evenodd"
                      d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
                <span class="text-sm font-medium text-gray-700">View Drafts</span>
              </.link>
            </div>
          </div>

          <div class="bg-white/90 backdrop-blur shadow-xl rounded-lg p-6 border border-blue-100">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Application Status</h3>

            <div class="space-y-3">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Draft</span>
                <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">
                  <%= @draft_count %>
                </span>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Submitted</span>
                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                  <%= @submitted_count %>
                </span>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Approved</span>
                <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                  <%= @approved_count %>
                </span>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Returned</span>
                <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium">
                  <%= @declined_count %>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      user_id = socket.assigns.current_user.id

      draft_count = Licenses.count_user_applications_by_status(user_id, "draft")
      submitted_count = Licenses.count_user_applications_by_status(user_id, "submitted")
      approved_count = Licenses.count_user_applications_by_status(user_id, "approved")
      declined_count = Licenses.count_user_applications_by_status(user_id, "declined")

      total_applications = draft_count + submitted_count + approved_count + declined_count

      {:ok,
       assign(socket,
         total_applications: total_applications,
         draft_count: draft_count,
         submitted_count: submitted_count,
         approved_count: approved_count,
         declined_count: declined_count
       )}
    else
      {:ok,
       assign(socket,
         total_applications: 0,
         draft_count: 0,
         submitted_count: 0,
         approved_count: 0,
         declined_count: 0
       )}
    end
  end
end
