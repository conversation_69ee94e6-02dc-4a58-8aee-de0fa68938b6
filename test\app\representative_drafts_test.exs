defmodule App.RepresentativeDraftsTest do
  use App.DataCase

  alias App.RepresentativeDrafts

  describe "representative_draft" do
    alias App.RepresentativeDrafts.RepresentativeDraft

    import App.RepresentativeDraftsFixtures

    @invalid_attrs %{template: nil}

    test "list_representative_draft/0 returns all representative_draft" do
      representative_draft = representative_draft_fixture()
      assert RepresentativeDrafts.list_representative_draft() == [representative_draft]
    end

    test "get_representative_draft!/1 returns the representative_draft with given id" do
      representative_draft = representative_draft_fixture()

      assert RepresentativeDrafts.get_representative_draft!(representative_draft.id) ==
               representative_draft
    end

    test "create_representative_draft/1 with valid data creates a representative_draft" do
      valid_attrs = %{template: "some template"}

      assert {:ok, %RepresentativeDraft{} = representative_draft} =
               RepresentativeDrafts.create_representative_draft(valid_attrs)

      assert representative_draft.template == "some template"
    end

    test "create_representative_draft/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} =
               RepresentativeDrafts.create_representative_draft(@invalid_attrs)
    end

    test "update_representative_draft/2 with valid data updates the representative_draft" do
      representative_draft = representative_draft_fixture()
      update_attrs = %{template: "some updated template"}

      assert {:ok, %RepresentativeDraft{} = representative_draft} =
               RepresentativeDrafts.update_representative_draft(
                 representative_draft,
                 update_attrs
               )

      assert representative_draft.template == "some updated template"
    end

    test "update_representative_draft/2 with invalid data returns error changeset" do
      representative_draft = representative_draft_fixture()

      assert {:error, %Ecto.Changeset{}} =
               RepresentativeDrafts.update_representative_draft(
                 representative_draft,
                 @invalid_attrs
               )

      assert representative_draft ==
               RepresentativeDrafts.get_representative_draft!(representative_draft.id)
    end

    test "delete_representative_draft/1 deletes the representative_draft" do
      representative_draft = representative_draft_fixture()

      assert {:ok, %RepresentativeDraft{}} =
               RepresentativeDrafts.delete_representative_draft(representative_draft)

      assert_raise Ecto.NoResultsError, fn ->
        RepresentativeDrafts.get_representative_draft!(representative_draft.id)
      end
    end

    test "change_representative_draft/1 returns a representative_draft changeset" do
      representative_draft = representative_draft_fixture()

      assert %Ecto.Changeset{} =
               RepresentativeDrafts.change_representative_draft(representative_draft)
    end
  end
end
