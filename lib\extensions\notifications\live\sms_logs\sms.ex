defmodule AppWeb.Auth.LogsLive.Sms do
  @moduledoc false
  use AppWeb, :live_view
  alias Notification.Service.Logs.LogsSms, as: TableQuery
  alias Notification.Notification.SmsLogs, as: Sms
  alias Notify.NotifyRepo, as: <PERSON><PERSON>
  alias App.Accounts
  alias Notification.MessagesDrafts

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("message_records-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"SMS Logs", "Accessed SMS Logs Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> assign(params: params)
        |> assign(:clients, Accounts.get_clients!())
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(session_id: session["live_socket_id"])
        |> assign(showFilter: false)
        |> assign(form: LiveFunctions.filter_form(params))
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Sms Trails", "Accessed Sms Trails Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_list, params})

    {:noreply,
     socket
     |> assign(:params, params)
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "SMS Logs")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {:change_record_status, params} ->
        update(params, socket)

      {:search_records, params} ->
        send(self(), {:get_list, %{"filter" => params}})
        noreply(socket)

      {AppWeb.Component.DateFilter, {:filtered, data}} ->
        send(self(), {:get_list, %{"filter" => data}})

        {
          :noreply,
          socket
          |> assign(:live_action, :index)
          |> assign(:page_title, "Listing SMS Logs")
        }
    end
  end

  defp update(%{"id" => id}, socket) do
    sms = get_sms!(id)

    update_data = %{status: "PENDING"}
    {:ok, _} = update_sms(sms, update_data)
    send(self(), {:get_list, %{}})

    {
      :noreply,
      apply_action(socket, :index, %{})
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> assign(:live_action, :index)
    }
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, socket) do
    case target do
      "change_record_status" ->
        LiveFunctions.change_record(value, socket, :change_record_status)

      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      "filter_change" ->
        assign(socket, form: LiveFunctions.filter_form(value["filter"]))
        |> noreply()

      "refresh_table" ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :data_loader, true)
        |> noreply()

      "filter" ->
        value = if socket.assigns.showFilter == true, do: false, else: true

        assign(socket, :showFilter, value)
        |> noreply()

      "reset_filter" ->
        socket
        |> assign(form: LiveFunctions.filter_form(%{}))
        |> noreply()

      "search" ->
        send(self(), {:get_list, value})

        assign(socket, :data_loader, true)
        |> noreply()

      "export" ->
        LiveFunctions.export_records(
          socket,
          value,
          "sms_logs_service",
          socket.assigns.params
        )

      "close_model" ->
        socket =
          socket
          |> assign(:new_edit_modal, false)
          |> assign(:view_modal, false)
          |> assign(:live_action, :index)
          |> apply_action(:index, value)

        {:noreply, socket}

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :live_action, :index)
          |> assign(:message, nil)
        }

      "view_message" ->
        {
          :noreply,
          assign(socket, :message, value["message"])
          |> assign(:live_action, :view_message)
        }

      "view_details" ->
        {
          :noreply,
          assign(socket, :details, MessagesDrafts.get_message!(value["id"]))
          |> assign(:page_title, "Extra Details")
          |> assign(:live_action, :view)
        }

      "update_status" ->
        {
          :noreply,
          assign(socket, :live_action, :confirm)
          |> assign(:confirmation_model_text, "Are you sure you want to resend message?")
          |> assign(:confirmation_model_params, value)
          |> assign(:confirmation_model_agree, "change_record_status")
        }

      _ ->
        {:noreply, socket}
    end
  end

  defp list(%{assigns: assigns} = socket, params) do
    data =
      LivePageControl.create_table_params(socket, params)
      |> Map.merge(%{"session_id" => assigns.session_id})

    {
      :noreply,
      assign(socket, :data, TableQuery.index(data, assigns))
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end

  def get_sms!(id), do: Repo.get!(Sms, id)

  defp update_sms(%Sms{} = sms, attrs) do
    sms
    |> Sms.changeset(attrs)
    |> Repo.update()
  end

  def create_sms(attrs \\ %{}) do
    %Sms{}
    |> Sms.changeset(attrs)
    |> Repo.insert()
  end
end
