defmodule App.AccountsTest do
  use App.DataCase

  alias App.Accounts

  describe "auth_keys" do
    alias App.Accounts.ApiKey

    import App.AccountsFixtures

    @invalid_attrs %{key: nil}

    test "list_auth_keys/0 returns all auth_keys" do
      api_key = api_key_fixture()
      assert Accounts.list_auth_keys() == [api_key]
    end

    test "get_api_key!/1 returns the api_key with given id" do
      api_key = api_key_fixture()
      assert Accounts.get_api_key!(api_key.id) == api_key
    end

    test "create_api_key/1 with valid data creates a api_key" do
      valid_attrs = %{key: "some key"}

      assert {:ok, %ApiKey{} = api_key} = Accounts.create_api_key(valid_attrs)
      assert api_key.key == "some key"
    end

    test "create_api_key/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Accounts.create_api_key(@invalid_attrs)
    end

    test "update_api_key/2 with valid data updates the api_key" do
      api_key = api_key_fixture()
      update_attrs = %{key: "some updated key"}

      assert {:ok, %ApiKey{} = api_key} = Accounts.update_api_key(api_key, update_attrs)
      assert api_key.key == "some updated key"
    end

    test "update_api_key/2 with invalid data returns error changeset" do
      api_key = api_key_fixture()
      assert {:error, %Ecto.Changeset{}} = Accounts.update_api_key(api_key, @invalid_attrs)
      assert api_key == Accounts.get_api_key!(api_key.id)
    end

    test "delete_api_key/1 deletes the api_key" do
      api_key = api_key_fixture()
      assert {:ok, %ApiKey{}} = Accounts.delete_api_key(api_key)
      assert_raise Ecto.NoResultsError, fn -> Accounts.get_api_key!(api_key.id) end
    end

    test "change_api_key/1 returns a api_key changeset" do
      api_key = api_key_fixture()
      assert %Ecto.Changeset{} = Accounts.change_api_key(api_key)
    end
  end

  describe "devices" do
    alias App.Accounts.Devices

    import App.AccountsFixtures

    @invalid_attrs %{name: nil, plateform: nil, operating_system: nil, last_activity: nil}

    test "list_devices/0 returns all devices" do
      devices = devices_fixture()
      assert Accounts.list_devices() == [devices]
    end

    test "get_devices!/1 returns the devices with given id" do
      devices = devices_fixture()
      assert Accounts.get_devices!(devices.id) == devices
    end

    test "create_devices/1 with valid data creates a devices" do
      valid_attrs = %{
        name: "some name",
        plateform: "some plateform",
        operating_system: "some operating_system",
        last_activity: ~N[2024-09-19 04:20:00]
      }

      assert {:ok, %Devices{} = devices} = Accounts.create_devices(valid_attrs)
      assert devices.name == "some name"
      assert devices.plateform == "some plateform"
      assert devices.operating_system == "some operating_system"
      assert devices.last_activity == ~N[2024-09-19 04:20:00]
    end

    test "create_devices/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Accounts.create_devices(@invalid_attrs)
    end

    test "update_devices/2 with valid data updates the devices" do
      devices = devices_fixture()

      update_attrs = %{
        name: "some updated name",
        plateform: "some updated plateform",
        operating_system: "some updated operating_system",
        last_activity: ~N[2024-09-20 04:20:00]
      }

      assert {:ok, %Devices{} = devices} = Accounts.update_devices(devices, update_attrs)
      assert devices.name == "some updated name"
      assert devices.plateform == "some updated plateform"
      assert devices.operating_system == "some updated operating_system"
      assert devices.last_activity == ~N[2024-09-20 04:20:00]
    end

    test "update_devices/2 with invalid data returns error changeset" do
      devices = devices_fixture()
      assert {:error, %Ecto.Changeset{}} = Accounts.update_devices(devices, @invalid_attrs)
      assert devices == Accounts.get_devices!(devices.id)
    end

    test "delete_devices/1 deletes the devices" do
      devices = devices_fixture()
      assert {:ok, %Devices{}} = Accounts.delete_devices(devices)
      assert_raise Ecto.NoResultsError, fn -> Accounts.get_devices!(devices.id) end
    end

    test "change_devices/1 returns a devices changeset" do
      devices = devices_fixture()
      assert %Ecto.Changeset{} = Accounts.change_devices(devices)
    end
  end

  describe "tbl_admin_contacts" do
    alias App.Accounts.AdminContacts

    import App.AccountsFixtures

    @invalid_attrs %{name: nil, email: nil, mobile: nil}

    test "list_tbl_admin_contacts/0 returns all tbl_admin_contacts" do
      user = user_fixture()
      assert Accounts.list_tbl_admin_contacts() == [user]
    end

    test "get_user!/1 returns the user with given id" do
      user = user_fixture()
      assert Accounts.get_user!(user.id) == user
    end

    test "create_user/1 with valid data creates a user" do
      valid_attrs = %{name: "some name", email: "some email", mobile: "some mobile"}

      assert {:ok, %AdminContacts{} = user} = Accounts.create_user(valid_attrs)
      assert user.name == "some name"
      assert user.email == "some email"
      assert user.mobile == "some mobile"
    end

    test "create_user/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Accounts.create_user(@invalid_attrs)
    end

    test "update_user/2 with valid data updates the user" do
      user = user_fixture()

      update_attrs = %{
        name: "some updated name",
        email: "some updated email",
        mobile: "some updated mobile"
      }

      assert {:ok, %AdminContacts{} = user} = Accounts.update_user(user, update_attrs)
      assert user.name == "some updated name"
      assert user.email == "some updated email"
      assert user.mobile == "some updated mobile"
    end

    test "update_user/2 with invalid data returns error changeset" do
      user = user_fixture()
      assert {:error, %Ecto.Changeset{}} = Accounts.update_user(user, @invalid_attrs)
      assert user == Accounts.get_user!(user.id)
    end

    test "delete_user/1 deletes the user" do
      user = user_fixture()
      assert {:ok, %User{}} = Accounts.delete_user(user)
      assert_raise Ecto.NoResultsError, fn -> Accounts.get_user!(user.id) end
    end

    test "change_user/1 returns a user changeset" do
      user = user_fixture()
      assert %Ecto.Changeset{} = Accounts.change_user(user)
    end
  end
end
