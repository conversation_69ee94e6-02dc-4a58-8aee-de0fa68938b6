defmodule Reports.Context do
  @moduledoc """
  The Reports context.
  """

  import Ecto.Query, warn: false

  alias Statistics.Logs.{
    MonthlyStatistic,
    PeriodicStatistic,
    SenderMonthlyStatistic,
    SenderPeriodicStatistic
  }

  alias Statistics.StatRepo
  alias App.Accounts

  def periodic_statistics(id, date) do
    PeriodicStatistic
    |> join(
      :right,
      [c],
      day in fragment(
        "select generate_series(to_date(?, 'YYYY-MM-DD'),
      to_date(?, 'YYYY-MM-DD') + INTERVAL '1 MONTH - 1 day',
      '1 day')::date AS d",
        ^"#{date}",
        ^"#{date}"
      ),
      on: day.d == fragment("date(?)", c.submitted_at)
    )
    |> compose_periodic_query(id)
    |> handle_periodic_get()
    |> StatRepo.all()
  end

  defp compose_periodic_query(query, client_id) do
    query
    |> group_by([c, day], [day.d, c.status, c.client_id])
    |> order_by([_c, day], day.d)
    |> having([c, _day], c.client_id == ^client_id)
    |> having([c, _day], c.status != ^"PENDING")
    |> or_having([c, _day], is_nil(c.status))
    |> or_having([c, _day], is_nil(c.client_id))
  end

  defp handle_periodic_get(query) do
    query
    |> select(
      [c, day],
      %{
        day: fragment("to_char(?, 'YYYY-MM-DD')", day.d),
        status:
          fragment(
            "CASE WHEN ? != ? AND ? != ?
        AND ? != ? AND ? != ?
        AND ? != ? THEN ?
        ELSE ? END as status",
            c.status,
            "SENT",
            c.status,
            "PENDING",
            c.status,
            "FAILED",
            c.status,
            "Invalid phone number",
            c.status,
            "DELIVERED",
            "SYSTEM_FAILURE",
            c.status
          ),
        count: fragment("coalesce(?, 0)", sum(c.count)),
        client_id: c.client_id,
        serial: fragment("ROW_NUMBER () OVER (ORDER BY ?)", day.d)
      }
    )
  end

  defp handle_prd_get(query) do
    query
    |> select(
      [c, day],
      %{
        day: fragment("to_char(?, 'YYYY-MM-DD')", day.d),
        status:
          fragment(
            "CASE WHEN ? != ? AND ? != ? AND ? != ?
        AND ? != ? AND ? != ? THEN ? ELSE ? END as status",
            c.status,
            "SENT",
            c.status,
            "PENDING",
            c.status,
            "FAILED",
            c.status,
            "Invalid phone number",
            c.status,
            "DELIVERED",
            "SYSTEM_FAILURE",
            c.status
          ),
        count: fragment("coalesce(?, 0)", sum(c.count)),
        serial: fragment("ROW_NUMBER () OVER (ORDER BY ?)", day.d)
      }
    )
  end

  def periodic_stats(id, year, month) do
    period = "#{year}-#{month}-01"

    PeriodicStatistic
    |> compose_prd_query(period)
    |> having(
      [c, _day],
      (c.status != ^"PENDING" and
         c.status != ^"SYSTEM_FAILURE" and
         c.client_id == ^id) or
        is_nil(c.status)
    )
    |> handle_prd_get()
    |> StatRepo.all()
  end

  def periodic_stats(year, month) do
    period = "#{year}-#{month}-01"

    PeriodicStatistic
    |> compose_prd_query(period)
    |> having(
      [c, _day],
      (c.status != ^"PENDING" and
         c.status != ^"SYSTEM_FAILURE") or is_nil(c.status)
    )
    |> handle_prd_get()
    |> StatRepo.all()
  end

  defp compose_prd_query(struct, period) do
    struct
    |> join(
      :right,
      [c],
      day in fragment(
        "select
      generate_series(to_date(?, 'YYYY-MM-DD'),
      to_date(?, 'YYYY-MM-DD') + INTERVAL '1 MONTH - 1 day',
      '1 day')::date AS d",
        ^period,
        ^period
      ),
      on: day.d == fragment("date(?)", c.submitted_at)
    )
    |> group_by([c, day], [day.d, c.status, c.client_id])
    |> order_by([_c, day], day.d)
  end

  def dashboard_results do
    PeriodicStatistic
    |> compose_dashboard()
    |> handle_get_dashboard()
    |> StatRepo.all()
  end

  defp compose_dashboard(struct) do
    struct
    |> join(
      :right,
      [c],
      day in fragment("""
        select generate_series(
          date_trunc('month', now()),
          date_trunc('MONTH', now()) + INTERVAL '1 MONTH - 1 day',
          '1 day'
        )::date AS d
      """),
      on: day.d == fragment("date(?)", c.submitted_at)
      # Corrected the `on:` clause
    )
    |> group_by([c, day], [day.d, c.status, c.courier_type, c.client_id])
    |> order_by([_c, day], day.d)
  end

  defp handle_get_dashboard(query) do
    query
    |> select(
      [c, day],
      %{
        day: fragment("to_char(?, 'YYYY-MM-DD')", day.d),
        status:
          fragment(
            "CASE WHEN ? != ?
        AND ? != ?
        AND ? != ?
        AND ? != ?
        AND ? != ? THEN ?
        ELSE ? END as status",
            c.status,
            "SENT",
            c.status,
            "PENDING",
            c.status,
            "Invalid phone number",
            c.status,
            "FAILED",
            c.status,
            "DELIVERED",
            "SYSTEM_FAILURE",
            c.status
          ),
        traffic: c.courier_type,
        count: sum(c.count)
      }
    )
  end

  def dashboard() do
    PeriodicStatistic
    |> join(
      :right,
      [c],
      day in fragment("""
        select generate_series(
          date_trunc('month', now()),
          date_trunc('MONTH', now()) + INTERVAL '1 MONTH - 1 day',
          '1 day'
        )::date AS d
      """),
      on: day.d == fragment("date(?)", c.submitted_at)
    )
    |> select(
      [c, day],
      %{
        day: fragment("to_char(?, 'YYYY-MM-DD')", day.d)
      }
    )
    |> StatRepo.all()
  end

  def traffic_by_year(client_id, year) do
    MonthlyStatistic
    |> compose_yrl_query()
    |> having([m, _mon], m.client_id == ^client_id and m.year == ^year)
    |> order_by([_m, mon], mon.d)
    |> handle_yrl_get()
    |> StatRepo.all()
  end

  def traffic_by_year(year) do
    MonthlyStatistic
    |> compose_yrl_query()
    |> having([m, _mon], m.year == ^year)
    |> order_by([_m, mon], mon.d)
    |> handle_yrl_get()
    |> StatRepo.all()
  end

  def post_paid_report_rest(client_id, prd) do
    MonthlyStatistic
    |> where([b], b.status in ["SENT", "FAILED", "DELIVERED"])
    |> where([b], b.client_id == ^client_id and b.month == ^prd.month and b.year == ^prd.year)
    |> group_by([b], [b.source])
    |> select([b], %{
      "source" => b.source,
      "total" => sum(b.count)
    })
    |> StatRepo.all()
  end

  def sender_pre_paid_report_rest(client_id, sender_id, prd) do
    SenderPeriodicStatistic
    |> where(
      [b],
      b.client_id == ^client_id and b.sender_id == ^sender_id and
        b.status in ["SENT", "FAILED", "DELIVERED"]
    )
    |> handle_report_where(prd)
    |> group_by([b], [b.sender_id])
    |> select([b], %{
      "sender" => b.sender_id,
      "total" => sum(b.count)
    })
    |> StatRepo.all()
  end

  defp handle_report_where(query, prd) when not is_nil(prd) do
    query
    |> where(
      [b],
      fragment("Extract(year from ?) = ?", b.submitted_at, ^prd.year) and
        fragment("Extract(month from ?) = ?", b.submitted_at, ^prd.month)
    )
  end

  defp handle_report_where(query, _), do: query

  def sender_post_paid_report_rest(client_id, sender_id, prd) do
    SenderMonthlyStatistic
    |> where([b], b.status in ["SENT", "FAILED", "DELIVERED"])
    |> where(
      [b],
      b.client_id == ^client_id and b.sender_id == ^sender_id and b.month == ^prd.month and
        b.year == ^prd.year
    )
    |> group_by([b], [b.source])
    |> select([b], %{
      "source" => b.source,
      "total" => sum(b.count)
    })
    |> StatRepo.all()
  end

  def compose_yrl_query(struct) do
    struct
    |> join(:right, [m], mon in fragment("select generate_series(1,12) d"), on: m.month == mon.d)
    |> group_by([m, mon], [mon.d, m.client_id, m.year, m.status])
  end

  def handle_yrl_get(query) do
    query
    |> select([m, mon], %{
      "client_id" => m.client_id,
      "month" => fragment("to_char(to_timestamp (d::text, 'MM'), 'TMMonth')"),
      "month_num" => mon.d,
      "year" => m.year,
      "status" => fragment("CASE WHEN ? != 'SENT'
        AND ? != 'PENDING'
        AND ? != 'Invalid phone number'
        AND ? != 'FAILED'
        AND ? != 'DELIVERED' THEN 'SYSTEM_FAILURE'
        ELSE ? END", m.status, m.status, m.status, m.status, m.status, m.status),
      "count" => fragment("coalesce(sum(?), 0)", m.count)
    })
  end

  # ------------------------------------ logs by service provider ------------#
  def statistics_by_provider(year, month) do
    PeriodicStatistic
    |> where(
      [s],
      fragment("extract(month from ?) = ?", s.submitted_at, ^month) and
        fragment("extract(year from ?) = ?", s.submitted_at, ^year)
    )
    |> group_by([s], [s.courier, s.status, s.submitted_at])
    |> order_by([s], [s.courier])
    |> select([s], %{
      count: sum(s.count),
      courier: s.courier,
      status: s.status,
      period: fragment("to_char(?, 'Month, YYYY')", s.submitted_at)
    })
    |> StatRepo.all()
  end

  # ------------------------------------ logs by client ----------------------#

  def statistics_by_client(year, month) do
    PeriodicStatistic
    |> where(
      [s, _c],
      fragment("extract(month from ?) = ?", s.submitted_at, ^month) and
        fragment("extract(year from ?) = ?", s.submitted_at, ^year)
    )
    |> handle_get_stats()
    |> StatRepo.all()
    |> Enum.map(&Map.put(&1, :client, Accounts.get_client_name_by_id(&1.client)))
  end

  defp handle_get_stats(query) do
    query
    |> group_by([s], [s.client_id, s.status, s.submitted_at])
    |> order_by([s], [s.client_id])
    |> select([s], %{
      count: sum(s.count),
      client: s.client_id,
      status: s.status,
      period: fragment("to_char(?, 'Month, YYYY')", s.submitted_at)
    })
  end

  def dashboard_stats(status_list \\ [], client_id \\ nil, courier_type \\ nil) do
    yesterday = Timex.shift(Timex.today(), days: -1)
    beginning_of_week = Date.beginning_of_month(yesterday)
    end_of_week = Date.end_of_month(yesterday)

    {_, start_date} = NaiveDateTime.new(Date.add(beginning_of_week, -1), ~T[22:00:00])
    {_, end_date} = NaiveDateTime.new(end_of_week, ~T[21:59:59])

    %{
      "start_date" => start_date,
      "end_date" => end_date,
      "client_id" => client_id,
      "status_list" => status_list,
      "courier_type" => courier_type
    }
    |> Enum.reduce(PeriodicStatistic, fn
      {"client_id", value}, query when not is_nil(value) and value != "" ->
        where(query, [s], s.client_id == ^value)

      {"courier_type", value}, query when not is_nil(value) and value != "" ->
        where(query, [s], s.courier_type == ^value)

      {"status_list", value}, query when value != [] ->
        where(query, [s], s.status in ^value)

      {"start_date", value}, query when not is_nil(value) ->
        where(query, [s], s.submitted_at >= ^value)

      {"end_date", value}, query when not is_nil(value) ->
        where(query, [s], s.submitted_at <= ^value)

      {_, _}, query ->
        # Not a where parameter
        query
    end)
    |> StatRepo.aggregate(:sum, :count) || 0
  end

  def dashboard_chart_stats(client_id \\ nil) do
    result =
      PeriodicStatistic
      |> join(
        :right,
        [c],
        day in fragment("""
        select generate_series(
          date_trunc('month', now()),
          date_trunc('MONTH', now()) + INTERVAL '1 MONTH - 1 day',
          '1 day'
        )::date AS d
        """),
        on: day.d == fragment("date(?)", c.submitted_at)
      )
      |> group_by([c, day], [day.d, c.status, c.client_id])
      |> order_by([_c, day], day.d)
      |> (fn query ->
            if client_id do
              having(query, [c, _day], c.client_id == ^client_id)
              |> or_having([c, _day], is_nil(c.client_id))
            else
              query
            end
          end).()
      |> select([c, day], %{
        day: fragment("to_char(?, 'YYYY-MM-DD')", day.d),
        status:
          fragment(
            "CASE WHEN ? != ?
        AND ? != ?
        AND ? != ?
        AND ? != ?
        AND ? != ? THEN ?
        ELSE ? END as status",
            c.status,
            "SENT",
            c.status,
            "PENDING",
            c.status,
            "Invalid phone number",
            c.status,
            "FAILED",
            c.status,
            "DELIVERED",
            "SYSTEM_FAILURE",
            c.status
          ),
        count: sum(c.count)
      })
      |> StatRepo.all()

    results = Enum.group_by(result, & &1.day)

    sent =
      get_status_value1(
        results,
        if(client_id,
          do: ["SENT", "DELIVERED"],
          else: ["SENT"]
        )
      )

    delivered =
      get_status_value1(
        results,
        if(client_id,
          do: ["SENT", "DELIVERED"],
          else: ["DELIVERED"]
        )
      )

    failed = get_status_value1(results, ["FAILED", "SYSTEM_FAILURE"])
    invalid = get_status_value1(results, ["Invalid phone number"])

    %{
      "results" => %{
        "datasets" => [
          %{
            "name" => "Delivered",
            "data" => delivered,
            "type" => "bar"
          },
          %{
            "name" => "Failed",
            "data" => failed,
            "type" => "column"
          },
          %{
            "name" => "Sent",
            "data" => sent,
            "type" => "line"
          },
          %{
            "name" => "Invalid",
            "data" => invalid,
            "type" => "line"
          }
        ],
        "categories" => Enum.map(results, fn {key, _} -> key end),
        "colors" => ["#22c55e", "#ef4444", "#3b82f6", "#a855f7"]
      }
    }
  end

  def get_status_value1(values, status) do
    Enum.map(values, fn {_, values} ->
      result = Enum.filter(values, &(&1.status in status))

      with false <- Enum.empty?(result) do
        Enum.reduce(result, &%{&1 | count: &1.count + &2.count}).count
      else
        _ -> 0
      end
    end)
  end

  def get_status_value(values, status) do
    result = Enum.filter(values, &(&1.status == status or is_nil(&1.status)))

    with false <- Enum.empty?(result) do
      Enum.reduce(result, &%{&1 | count: &1.count + &2.count}).count
    else
      _ -> 0
    end
  end
end
