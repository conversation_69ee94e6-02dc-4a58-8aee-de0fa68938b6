defmodule App.PrcnTest do
  use App.DataCase

  alias App.Prcn

  describe "prcn_index" do
    alias App.Prcn.PrcnIndex

    import App.PrcnFixtures

    @invalid_attrs %{depot: nil, crop: nil, num: nil}

    test "list_prcn_index/0 returns all prcn_index" do
      prcn_index = prcn_index_fixture()
      assert Prcn.list_prcn_index() == [prcn_index]
    end

    test "get_prcn_index!/1 returns the prcn_index with given id" do
      prcn_index = prcn_index_fixture()
      assert Prcn.get_prcn_index!(prcn_index.id) == prcn_index
    end

    test "create_prcn_index/1 with valid data creates a prcn_index" do
      valid_attrs = %{depot: "some depot", crop: "some crop", num: "some num"}

      assert {:ok, %PrcnIndex{} = prcn_index} = Prcn.create_prcn_index(valid_attrs)
      assert prcn_index.depot == "some depot"
      assert prcn_index.crop == "some crop"
      assert prcn_index.num == "some num"
    end

    test "create_prcn_index/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Prcn.create_prcn_index(@invalid_attrs)
    end

    test "update_prcn_index/2 with valid data updates the prcn_index" do
      prcn_index = prcn_index_fixture()

      update_attrs = %{
        depot: "some updated depot",
        crop: "some updated crop",
        num: "some updated num"
      }

      assert {:ok, %PrcnIndex{} = prcn_index} = Prcn.update_prcn_index(prcn_index, update_attrs)
      assert prcn_index.depot == "some updated depot"
      assert prcn_index.crop == "some updated crop"
      assert prcn_index.num == "some updated num"
    end

    test "update_prcn_index/2 with invalid data returns error changeset" do
      prcn_index = prcn_index_fixture()
      assert {:error, %Ecto.Changeset{}} = Prcn.update_prcn_index(prcn_index, @invalid_attrs)
      assert prcn_index == Prcn.get_prcn_index!(prcn_index.id)
    end

    test "delete_prcn_index/1 deletes the prcn_index" do
      prcn_index = prcn_index_fixture()
      assert {:ok, %PrcnIndex{}} = Prcn.delete_prcn_index(prcn_index)
      assert_raise Ecto.NoResultsError, fn -> Prcn.get_prcn_index!(prcn_index.id) end
    end

    test "change_prcn_index/1 returns a prcn_index changeset" do
      prcn_index = prcn_index_fixture()
      assert %Ecto.Changeset{} = Prcn.change_prcn_index(prcn_index)
    end
  end
end
