defmodule App.AccountsFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `App.Accounts` context.
  """

  @doc """
  Generate a api_key.
  """
  def api_key_fixture(attrs \\ %{}) do
    {:ok, api_key} =
      attrs
      |> Enum.into(%{
        key: "some key"
      })
      |> App.Accounts.create_api_key()

    api_key
  end

  @doc """
  Generate a devices.
  """
  def devices_fixture(attrs \\ %{}) do
    {:ok, devices} =
      attrs
      |> Enum.into(%{
        last_activity: ~N[2024-09-19 04:20:00],
        name: "some name",
        operating_system: "some operating_system",
        plateform: "some plateform"
      })
      |> App.Accounts.create_devices()

    devices
  end

  @doc """
  Generate a user.
  """
  def user_fixture(attrs \\ %{}) do
    {:ok, user} =
      attrs
      |> Enum.into(%{
        email: "some email",
        mobile: "some mobile",
        name: "some name"
      })
      |> App.Accounts.create_user()

    user
  end
end
