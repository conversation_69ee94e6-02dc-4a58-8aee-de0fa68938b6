defmodule App.Service.Export.CsvSmppServicesServices do
  @moduledoc false
  alias App.Service.Table.ServiceSmpp
  alias App.Service.Export.Functions

  @headers [
    "SERVICE NAME",
    "HOST",
    "SYSTEM ID",
    "MOBILE REGEX",
    "STATUS"
  ]

  def index(assigns, payload) do
    ServiceSmpp.export(assigns, payload)
    |> Stream.map(
      &[
        &1.service_name,
        &1.host,
        &1.system_id,
        &1.mobile_regex,
        Functions.table_numeric_status(&1.status)
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Smpp Services"],
              ["", "", "", ""],
              @headers,
              ["", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
