defmodule App.Service.Export.CsvSessionLogsService do
  @moduledoc false
  alias App.Service.Logs.LogsSession

  @headers [
    "DATE",
    "USERNAME",
    "PORTAL",
    "SESSION",
    "DESCRIPTION"
  ]

  def index(payload) do
    LogsSession.export(payload)
    |> Stream.map(
      &[
        Calendar.strftime(NaiveDateTime.add(&1.inserted_at, 7200, :second), "%d %B %Y %H:%M:%S"),
        &1.user_id,
        &1.portal,
        &1.status,
        &1.description
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Session Logs"],
              ["", "", "", "", ""],
              @headers,
              ["", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
