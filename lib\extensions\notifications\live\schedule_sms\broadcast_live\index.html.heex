<div class="px-4 sm:px-6 lg:px-8 mt-8 flow-root">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="p-6 text-center rounded-md">
      <div class="flex items-center justify-between">
        <button
          onclick="window.history.back()"
          class="text-gray-600 hover:text-gray-800 focus:outline-none rounded-full p-3 border border-brand-10"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>

        <h1 class="text-3xl font-bold tracking-tight text-gray-900 flex-1 text-center">
          Schedule Broadcast SMS
        </h1>

        <div class="w-6"></div>
        <!-- Placeholder for alignment -->
      </div>

      <p class="mt-2 text-gray-600">
        Automate bulk SMS messaging by setting a date and time for delivery
      </p>
    </div>
    <!-- Progress Steps -->
    <div class="mb-8">
      <.live_component
        module={AppWeb.Components.FormNav.WizardFormNav}
        menu_list={@menu_list}
        current_position={@process}
        id="Navigation"
      />
    </div>
    <!-- Main Content -->
    <div class="bg-white rounded-xl shadow-sm">
      <%= if @process == 1 do %>
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 p-6">
          <!-- Form Section -->
          <div class="lg:col-span-2">
            <.form
              for={@form}
              id="broadcast-form"
              phx-change="validate"
              phx-submit="save"
              class="space-y-6"
            >
              <!-- File Upload -->
              <div class="mt-6">
                <span>Upload file <span class="text-red-500">*</span></span>
                <label
                  phx-drop-target={@uploads.file.ref}
                  class="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer"
                >
                  <%= if @uploads.file.entries == [] do %>
                    <div class="space-y-2">
                      <svg
                        class="mx-auto h-12 w-12 text-gray-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        />
                      </svg>

                      <div class="text-sm text-gray-600">
                        <span class="font-medium text-blue-600">Click to upload</span>
                      </div>

                      <p class="text-xs text-gray-500">CSV, XLSX files only</p>
                    </div>
                  <% else %>
                    <div class="space-y-4">
                      <%= for entry <- @uploads.file.entries do %>
                        <div class="relative flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div class="flex items-center space-x-4">
                            <div class="flex-shrink-0">
                              <svg
                                class="h-8 w-8 text-gray-400"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="2"
                                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                />
                              </svg>
                            </div>

                            <div class="flex-1 min-w-0">
                              <p class="text-sm font-medium text-gray-900 truncate">
                                <%= entry.client_name %>
                              </p>

                              <div class="mt-1 relative pt-1">
                                <div class="overflow-hidden h-2 text-xs flex rounded bg-blue-200">
                                  <div
                                    style={"width: #{entry.progress}%"}
                                    class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-500"
                                  >
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <button
                            type="button"
                            phx-click="cancel-entry"
                            phx-value-ref={entry.ref}
                            class="ml-4 flex-shrink-0 text-red-600 hover:text-red-700"
                          >
                            <svg
                              class="h-5 w-5"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"
                              />
                            </svg>
                          </button>
                        </div>

                        <%= for err <- upload_errors(@uploads.file, entry) do %>
                          <p class="mt-2 text-sm text-red-600">
                            <%= LiveFunctions.image_error_to_string(err) %>
                          </p>
                        <% end %>
                      <% end %>
                    </div>
                  <% end %>
                  <.live_file_input upload={@uploads.file} class="sr-only" />
                </label>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <.input
                  field={@form[:sender_id]}
                  type="select"
                  prompt="Select Sender"
                  options={@chose_sender}
                  required
                  label={raw(~c"Sender <span class='text-red-500'>*</span>")}
                  class="w-full rounded-lg border-gray-300"
                />
                <.input
                  field={@form[:source]}
                  type="text"
                  placeholder="Enter Message Source. e.g Acc Department"
                  label={raw(~c"Source <span class='text-red-500'>*</span>")}
                  class="w-full rounded-lg border-gray-300"
                />
                <.input
                  field={@form[:date]}
                  type="date"
                  label={raw(~c"Schedule Date <span class='text-red-500'>*</span>")}
                  value={@min_date}
                  min={@min_date}
                  required
                  wrapper_class="space-y-2"
                  input_class="w-full p-3 border border-gray-300 rounded-lg"
                />
                <.input
                  field={@form[:time]}
                  type="time"
                  value={
                    {_, {h, m, _s}} = :calendar.local_time()
                    "#{h}:#{m}"
                  }
                  label={raw(~c"Schedule Time <span class='text-red-500'>*</span>")}
                  required
                  class="w-full rounded-lg border-gray-300"
                />
              </div>

              <div>
                <.input
                  field={@form[:message]}
                  type="textarea"
                  placeholder="Enter Your Message"
                  required
                  label={raw(~c"Message <span class='text-red-500'>*</span>")}
                  phx-hook="messageCounter"
                  class="w-full rounded-lg border-gray-300"
                  rows="4"
                />
                <div phx-update="ignore" id="message-counter" class="mt-2 text-sm text-gray-600">
                  <span class="count">0</span>
                  message(s) • <span class="remainder">160</span>
                  characters remaining
                </div>
              </div>
              <!-- Action Buttons -->
              <div class="flex justify-end space-x-4 pt-6">
                <%= if !@loader do %>
                  <div class="flex space-x-3">
                    <.button
                      type="button"
                      onclick="history.back()"
                      class="px-6 py-2.5 rounded-lg transition-colors"
                    >
                      Back
                    </.button>

                    <.button
                      type="submit"
                      phx-disable-with="Sending..."
                      class="px-6 py-2.5 rounded-lg shadow-sm transition-all"
                    >
                      Schedule SMS
                    </.button>
                  </div>
                <% else %>
                  <.button
                    type="button"
                    class="px-6 py-2.5 rounded-lg shadow-sm transition-all"
                    disabled
                  >
                    Schedule...
                  </.button>
                <% end %>
              </div>
            </.form>
          </div>
          <!-- File Layout Guide -->
          <div class="lg:col-span-1 border-l border-gray-200 p-4">
            <div class="bg-gray-50 rounded-lg p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">File Layout</h3>

              <div class="bg-white rounded-lg p-4 mb-4">
                <div class="flex items-center space-x-4">
                  <div class="font-medium text-gray-900">Column 1</div>

                  <div class="text-gray-600">Mobile Number (e.g. 26097***)</div>
                </div>
              </div>

              <.button type="button" class="w-full flex items-center justify-center">
                <a href="/sample/broadcast_sample.xlsx" download class="flex items-center">
                  <svg
                    class="h-5 w-5 mr-2"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  Download Sample
                </a>
              </.button>
            </div>
          </div>
        </div>
      <% end %>

      <%= if @process == 0 do %>
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div class="bg-white p-8 rounded-lg shadow-xl max-w-md w-full text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto">
            </div>

            <h2 class="mt-4 text-xl font-semibold text-gray-900"><%= @loading_status %></h2>

            <p class="mt-2 text-gray-600">
              This may take a few seconds, please don't close this page.
            </p>
          </div>
        </div>
      <% end %>

      <%= if @process == 2 do %>
        <div class="p-6 space-y-6">
          <h3 class="text-lg font-semibold text-gray-900">Validation Status</h3>

          <Card.extract_validation
            valid_record={NumberF.currency(@data.success, "", 0)}
            invalid_record={NumberF.currency(@data.failed, "", 0)}
            total_record={NumberF.currency(@data.total, "", 0)}
          />
          <div class="flex justify-end space-x-4 mt-6">
            <%= if !@loader and (@data.success == @data.total) do %>
              <.button type="button" phx-click="cancel_upload">
                Cancel
              </.button>

              <.button type="button" phx-click="proceed_file">
                Proceed
              </.button>
            <% end %>

            <%= if !@loader and (@data.success != @data.total) do %>
              <.button type="button" phx-click="cancel_upload">
                Cancel
              </.button>
            <% end %>

            <%= if @loader do %>
              <.button
                type="button"
                disabled
                class="px-4 py-2 text-white bg-blue-600 rounded-lg opacity-75 flex items-center"
              >
                Proceeding
                <div class="ml-2 animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              </.button>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<Model.fullscreen :if={@live_action in [:view]} id="view-modal" show return_to="close_modal">
  <.live_component
    module={AppWeb.UploadedList.ScheduleMessageList.ViewComponent}
    id="view_records_data"
    title={@record_title}
    records={@records}
  />
</Model.fullscreen>
