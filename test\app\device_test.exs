defmodule App.DeviceTest do
  use App.DataCase

  alias App.Device

  describe "api_key" do
    alias App.Device.Devices

    import App.DeviceFixtures

    @invalid_attrs %{code: nil, status: nil}

    test "list_api_key/0 returns all api_key" do
      devices = devices_fixture()
      assert Device.list_api_key() == [devices]
    end

    test "get_devices!/1 returns the devices with given id" do
      devices = devices_fixture()
      assert Device.get_devices!(devices.id) == devices
    end

    test "create_devices/1 with valid data creates a devices" do
      valid_attrs = %{code: "some code", status: 42}

      assert {:ok, %Devices{} = devices} = Device.create_devices(valid_attrs)
      assert devices.code == "some code"
      assert devices.status == 42
    end

    test "create_devices/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Device.create_devices(@invalid_attrs)
    end

    test "update_devices/2 with valid data updates the devices" do
      devices = devices_fixture()
      update_attrs = %{code: "some updated code", status: 43}

      assert {:ok, %Devices{} = devices} = Device.update_devices(devices, update_attrs)
      assert devices.code == "some updated code"
      assert devices.status == 43
    end

    test "update_devices/2 with invalid data returns error changeset" do
      devices = devices_fixture()
      assert {:error, %Ecto.Changeset{}} = Device.update_devices(devices, @invalid_attrs)
      assert devices == Device.get_devices!(devices.id)
    end

    test "delete_devices/1 deletes the devices" do
      devices = devices_fixture()
      assert {:ok, %Devices{}} = Device.delete_devices(devices)
      assert_raise Ecto.NoResultsError, fn -> Device.get_devices!(devices.id) end
    end

    test "change_devices/1 returns a devices changeset" do
      devices = devices_fixture()
      assert %Ecto.Changeset{} = Device.change_devices(devices)
    end
  end
end
