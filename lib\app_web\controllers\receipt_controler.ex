defmodule AppWeb.ReceiptController do
  use AppWeb, :controller

  alias App.{
    Transactions
  }

  def bundle_invoice(conn, params) do
    transaction =
      Transactions.get_transactions_online_by_reference_preload_client(params["reference"])

    render(conn, :bundle_invoice,
      transaction: transaction,
      live_socket_identifier: "#{:os.system_time()}",
      data_loader: false,
      client: transaction.client
    )
  end
end
