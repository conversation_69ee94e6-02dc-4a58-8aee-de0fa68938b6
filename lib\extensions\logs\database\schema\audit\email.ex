defmodule Logs.Audit.Email do
  use Ecto.Schema
  import Ecto.Changeset

  schema "logs_email" do
    field :body, :string
    field :date_sent, :date
    field :date_time, :time
    field :message_id, :string
    field :recipient, :string
    #    field :response, :string
    field :status, :string
    field :subject, :string
    field :module_name, :string
    field :type_use, :string
    field :deleted_at, :naive_datetime

    timestamps()
  end

  @doc false
  def changeset(email, attrs) do
    email
    |> cast(attrs, [
      :body,
      :date_sent,
      :date_time,
      :message_id,
      :recipient,
      :module_name,
      :status,
      :subject
    ])
    |> validate_required([:body, :recipient, :subject])
  end
end
