defmodule AppWeb.Authenticated.SettingsLive.Index do
  use AppWeb, :live_view

  @impl true
  def mount(_params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("settings-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Settings Maintenance", "Accessed Settings Maintenance Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      {:ok, assign(socket, current_tab: "user_profile")}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Settings Maintenance", "Accessed Settings Maintenance Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_event("change_tab", %{"tab" => tab}, socket) do
    {:noreply, assign(socket, current_tab: tab)}
  end
end
