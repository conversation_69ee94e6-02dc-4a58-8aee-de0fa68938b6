defmodule AppWeb.Plug.UserAuth do
  @behaviour Plug
  import Plug.Conn
  alias App.Services.PosUsers.Authentication.Crypto

  alias AppWeb.PosController, as: Callback

  def ensure_authenticates(conn, _params) do
    if Plug.Conn.get_req_header(conn, "token") != [] do
      [auth_token] = Plug.Conn.get_req_header(conn, "token")

      Crypto.decrypt(:auth, auth_token)
      |> case do
        {:ok, id} ->
          if user = App.ContextPos.get_user_by_id(id) do
            conn
            |> assign(:current_user, user)
          else
            Callback.respond(conn, %{message: "Token is Ivalid", status: 700}, :unauthorized)
            |> halt()
          end

        _anyError ->
          Callback.respond(conn, %{message: "Kindly Login", status: 700}, :unauthorized)
          |> halt()
      end
    else
      Callback.respond(conn, %{message: "Missing Header Key (token)", status: 400}, :unauthorized)
      |> halt()
    end
  end
end
