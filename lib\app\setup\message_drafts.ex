defmodule App.SetUp.MessageDrafts do
  @moduledoc false
  alias Notification.Notification.MessageDraft
  alias App.Repo

  # App.SetUp.MessageDrafts.init()

  def init do
    [
      %{
        message: """
        Dear {{client_name}},
         your application has been Submitted.
         Note:
         1.Successful submission does not mean acceptance of application.
         2.The application is only deemed complete and accepted upon approval by a Manager.
        """,
        service_type: "LICENCE_REGISTRATION"
      },
      %{
        message: """
        Dear {{client_name}},
         your license has been {{status}}
        """,
        service_type: "LICENCE_STATUS_UPDATE"
      }
    ]
    |> Enum.each(fn data ->
      Repo.insert!(%MessageDraft{
        message: data[:message],
        service_type: data[:service_type]
      })
    end)
  end
end
