defmodule AppWeb.Dashboard.TotalClients do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})
  on_mount({AppWeb.UserAuth, :ensure_authenticated})

  alias App.Accounts

  def render(assigns) do
    ~H"""
    <.stats_card
      title="Total Clients"
      icon="hero-user-group"
      stats_value={@total}
      stats_desc="Total Number of Clients"
    />
    """
  end

  def mount(_params, _session, socket) do
    {:ok, assign(socket, :total, Accounts.total_clients())}
  end
end
