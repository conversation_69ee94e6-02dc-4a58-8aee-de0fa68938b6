const Example = {
    mounted() {
        console.log('the element has been added to the DOM and its server LiveView has finished mounting')
    },
    beforeUpdate() {
        console.log('the element is about to be updated in the DOM. Note: any call here must be synchronous as the operation cannot be deferred or cancelled.')
    },
    updated() {
        console.log('the element has been updated in the DOM by the server\n')
    },
    destroyed() {
        console.log('the element has been removed from the page, either by a parent update, or by the parent being removed entirely')
    },
    disconnected() {
        console.log('the element\'s parent LiveView has disconnected from the server')
    },
    reconnected() {
        console.log('the element\'s parent LiveView has reconnected to the server\n')
    }
}