defmodule App.Service.Table.ServiceClientUsers do
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false

  alias App.Accounts.Client
  alias App.Repo

  @pagination [page_size: 10]

  def index(assigns, params) do
    compose_query(params, assigns)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def export(assigns, params) do
    compose_query(params, assigns)
    |> Repo.all()
  end

  def compose_query(params, _assigns) do
    Client
    |> join(:left, [a], b in assoc(a, :user))
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"client_name", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.client_name, ^sanitize_term(value))
        )

      {"client_id", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.client_id, ^sanitize_term(value)))

      {"status", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.status, ^sanitize_term(value)))

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a, b], %{
      id: a.id,
      client_id: a.client_id,
      client_name: a.client_name,
      status: a.status,
      user_name: a.user_name,
      created_by: fragment("CONCAT(?, ' ', ?)", b.first_name, b.last_name),
      payment_type: a.payment_type,
      inserted_at: fragment("TO_CHAR (?, 'DD MON YYYY, HH24:MI:SS')", a.inserted_at),
      updated_at: fragment("TO_CHAR (?, 'DD MON YYYY, HH24:MI:SS')", a.updated_at)
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a],
      fragment("lower(?) LIKE lower(?)", a.client_name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.user_name, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
