defmodule App.Service.Export.CsvAdminAccessService do
  @moduledoc false
  alias App.Service.Table.RoleUsers
  alias App.Service.Export.Functions

  @headers [
    "EMAIL",
    "FIRST NAME",
    "LAST NAME",
    "STATUS",
    "LAST LOGIN DATE"
  ]

  def index(payload) do
    RoleUsers.export(payload)
    |> Stream.map(
      &[
        &1.email,
        &1.first_name,
        &1.first_name,
        &1.status,
        NaiveDateTime.to_string(&1.last_login_date)
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Admin Access Roles Management"],
              ["", "", "", "", ""],
              @headers,
              ["", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
