defmodule App.Repo.Migrations.CreateCompanyTable do
  use Ecto.Migration

  def change do
    create table(:companies) do
      add :name, :string
      add :email, :string
      add :address, :string
      add :incorporation_date, :date
      add :registration_number, :string
      add :mobile, :string
      add :tpin, :string

      add :status, :integer, default: 1
      add :user_id, references(:tbl_users, on_delete: :nothing), null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:companies, [:email])
    create unique_index(:companies, [:tpin])
    create unique_index(:companies, [:mobile])

    alter table(:tbl_users) do
      add :company_id, references(:companies, on_delete: :nothing)
    end
  end
end
