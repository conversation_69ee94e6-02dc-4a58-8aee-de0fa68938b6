<%= if @data_loader == false do %>
  <%= if @get_attention_field==[] do %>
    <.live_component
      module={AppWeb.Registration.NavigationComponent}
      id="navigation"
      steps={@steps}
      current_position={@current_position}
    />
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 bg-white rounded-lg shadow-md mt-8">
      <div class="mb-2 border-b pb-2">
        <h1 class="text-2xl font-bold text-gray-900"><%= @chosen_licence.name %> Registration</h1>

        <p class="mt-2 text-sm text-gray-600">
          Please upload all required documents to proceed with your registration.
        </p>
      </div>

      <div class="p-2">
        <!-- Upload Status Overview -->
        <%!-- <%= if @available_uploads == [] do %> --%>
        <div class="mb-8">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Uploaded Documents</h3>

            <div class="flex items-center text-sm text-gray-500">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                <%= length(
                  Enum.filter(@upload_fields1, fn field ->
                    @record.data && Map.has_key?(@record.data, field.field_name) &&
                      Map.get(@record.data, field.field_name) != nil
                  end)
                ) %> of <%= length(@upload_fields1) %> uploaded
              </span>
            </div>
          </div>
          <!-- Document Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <%= for field <- @upload_fields1 do %>
              <%= if @record.data && Map.has_key?(@record.data, field.field_name) && 
                              Map.get(@record.data, field.field_name) != nil do %>
                <div class="border rounded-lg p-4 transition-all duration-200 hover:shadow-md">
                  <div class="flex items-start justify-between">
                    <div class="flex items-start flex-1">
                      <!-- Uploaded Documents -->
                      <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                        <svg
                          class="h-5 w-5 text-green-600"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>

                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 mb-1">
                          <%= field.field_label %>
                        </p>

                        <p class="text-sm font-medium text-gray-600 truncate">
                          : <%= String.slice(
                            @record.data[field.field_name] |> Path.basename(),
                            0,
                            40
                          ) <>
                            if String.length(@record.data[field.field_name] |> Path.basename()) >
                                 20,
                               do: "...",
                               else: "" %>
                        </p>
                      </div>
                    </div>

                    <%= if @record.data && Map.has_key?(@record.data, field.field_name) && 
                            Map.get(@record.data, field.field_name) != nil do %>
                      <button
                        type="button"
                        phx-click="remove_file"
                        phx-value-field={field.field_name}
                        class="ml-2 p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full transition-colors duration-200"
                        title="Remove file"
                      >
                        <svg class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path
                            fill-rule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </button>
                    <% end %>
                  </div>
                </div>
              <% end %>
            <% end %>
          </div>
        </div>
        <%!-- <% end %> --%>
        <!-- Upload Form -->
        <.simple_form
          for={@upload_form}
          id="upload-form"
          phx-submit="doc_save"
          phx-change="validate_doc"
          class="space-y-8"
        >
          <h2 class="text-lg font-medium text-gray-900 mb-4">Document Uploads</h2>

          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <%= if Enum.empty?(@available_uploads) do %>
              <div class="col-span-full py-8">
                <div class="flex flex-col items-center justify-center text-center text-gray-600 text-sm space-y-2">
                  <svg class="h-6 w-6 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>

                  <span class="font-medium">
                    All required documents have already been uploaded.
                  </span>
                </div>
              </div>
            <% else %>
              <%= for field <- @available_uploads do %>
                <%= if !(@record.data && Map.has_key?(@record.data, field.field_name) && 
                 Map.get(@record.data, field.field_name) != nil) do %>
                  <div class="space-y-3">
                    <label class="block text-sm font-medium text-gray-700">
                      <%= field.field_label %> <span class="text-rose-500">*</span>
                    </label>

                    <div>
                      <AppWeb.DocumentUploadComponent.document_upload_field uploads={
                        @uploads[String.to_atom(field.field_name)]
                      } /> <% field_atom = String.to_atom(field.field_name) %>
                      <%= if error = @upload_form.source.errors[field_atom] do %>
                        <p class="mt-2 text-sm text-red-600">
                          <%= elem(error, 0) %>
                        </p>
                      <% end %>
                    </div>

                    <%= if Map.get(field, :field_description) do %>
                      <p class="text-xs text-gray-500 mt-1"><%= field.field_description %></p>
                    <% end %>
                  </div>
                <% end %>
              <% end %>
            <% end %>
          </div>

          <div class="flex justify-end pt-6 border-t">
            <.button type="button" phx-click="back">
              <b><i class="hero-chevron-double-left position-left"></i></b> back
            </.button>

            <%= if @upload_form.source.valid? do %>
              <.button
                type="submit"
                class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium"
              >
                Next<b><i class="hero-chevron-double-right position-right"></i></b>
              </.button>
            <% end %>
          </div>
        </.simple_form>
      </div>
    </div>
  <% else %>
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 bg-white rounded-lg shadow-md mt-8">
      <div class="mb-8 border-b pb-5">
        <h1 class="text-2xl font-bold text-gray-900">
          <%= @chosen_licence.name %> Registration
        </h1>

        <p class="mt-2 text-sm text-gray-600">
          Please review and correct the highlighted field[s] below.
        </p>
      </div>

      <.simple_form
        for={@upload_form}
        id="upload-form"
        phx-submit="submit_correction"
        phx-change="validate_doc"
        class="space-y-8"
      >
        <h2 class="text-lg font-medium text-gray-900 mb-4">Document Uploads</h2>

        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <%= for field <- @upload_fields1  do %>
            <%= for attention <- @get_attention_field do %>
              <%= if attention.attention_field==field.field_label do %>
                <div class="space-y-3">
                  <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-md">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                          <path
                            fill-rule="evenodd"
                            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>

                      <div class="ml-3">
                        <p class="text-sm text-red-700 font-medium">
                          <%= attention.attention_field %>
                        </p>

                        <p class="mt-1 text-xs text-red-600">
                          <%= attention.reason %>
                        </p>
                      </div>
                    </div>
                  </div>
                  <input type="hidden" name="license_id" value={attention.license_id} />
                  <label class="block text-sm font-medium text-gray-700">
                    <%= field.field_label %> <span class="text-rose-500">*</span>
                  </label>

                  <div>
                    <AppWeb.DocumentUploadComponent.document_upload_field uploads={
                      @uploads[String.to_atom(field.field_name)]
                    } />
                  </div>

                  <%= if Map.get(field, :field_description) do %>
                    <p class="text-xs text-gray-500 mt-1">
                      <%= field.field_description %>
                    </p>
                  <% end %>
                </div>
              <% end %>
            <% end %>
          <% end %>
        </div>

        <div class="flex justify-end pt-6 border-t">
          <.button type="button" phx-click="back">
            <b><i class="hero-chevron-double-left position-left"></i></b> back
          </.button>

          <%= if @upload_form.source.valid? do %>
            <.button
              type="submit"
              class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium"
            >
              Submit<b><i class="hero-chevron-double-right position-right"></i></b>
            </.button>
          <% end %>
        </div>
      </.simple_form>
    </div>
  <% end %>
<% end %>
