defmodule App.Service.Table.PeriodicReports do
  import Ecto.Query, warn: false
  alias Reports.Context

  def index(socket, params) do
    page_number = String.to_integer(Map.get(params, "page", "1"))

    {summary, sub_totals, total, period} = compose_query(socket, params)

    %Scrivener.Page{
      page_number: page_number,
      page_size: 10,
      total_entries: length(summary),
      total_pages: div(length(summary) + 10 - 1, 10),
      entries: paginate_results(summary, sub_totals, total, period, page_number, 10)
    }
  end

  defp paginate_results(summary, sub_totals, total, period, page_number, page_size) do
    start_index = (page_number - 1) * page_size

    summary
    |> Enum.slice(start_index, page_size)
    |> Enum.map(fn entry ->
      %{
        date: entry.date,
        failed: entry.failed,
        sent: entry.sent,
        invalid: entry.invalid,
        delivered: entry.delivered,
        total: entry.failed + entry.sent + entry.invalid + entry.delivered,
        sub_totals: sub_totals,
        overall_total: total,
        period: period
      }
    end)
  end

  defp generate_statistic_report(results) do
    results = Enum.group_by(results, & &1.day)

    for {key, values} <- results do
      %{
        date: key,
        sent: Context.get_status_value(values, "SENT"),
        delivered: Context.get_status_value(values, "DELIVERED"),
        failed: Context.get_status_value(values, "FAILED"),
        invalid: Context.get_status_value(values, "Invalid phone number")
      }
    end
  end

  def export(socket, params) do
    filter = Map.get(params, "filter", %{})

    {results, _} =
      if Map.has_key?(socket.assigns, :client),
        do: stats_results(socket.assigns.current_user, filter),
        else: process_periodic_results(filter)

    results
    |> Enum.reject(&(&1.status in ["PENDING", "SYSTEM_FAILURE"]))
    |> Enum.map(fn result ->
      status_values =
        ~w(FAILED SENT "Invalid phone number" DELIVERED)
        |> Enum.map(&Context.get_status_value([result], &1))
        |> Enum.sum()

      %{
        date: result.day,
        failed: status_values,
        sent: status_values,
        invalid: status_values,
        delivered: status_values,
        total: status_values
      }
    end)
  end

  defp compose_query(socket, %{"filter" => filter}) do
    query_by_user =
      if is_nil(socket.assigns.client),
        do: process_periodic_results(filter),
        else: stats_results(socket.assigns.current_user, filter)

    {results, period} = query_by_user

    results =
      results
      |> Enum.reject(&(&1.status == "PENDING"))
      |> Enum.reject(&(&1.status == "SYSTEM_FAILURE"))

    summary = generate_statistic_report(results)

    {summary, calcu_totals(summary), get_total_traffic(results), period}
  end

  defp get_total_traffic(results) do
    if results == [] do
      0
    else
      Enum.reduce(results, &%{&1 | count: &1.count + &2.count}).count
    end
  end

  defp calcu_totals(results) do
    ~w(sent delivered failed invalid)
    |> Enum.map(fn status ->
      total =
        Enum.reduce(results, 0, fn result, acc ->
          acc + Map.get(result, String.to_atom(status), 0)
        end)

      {status, total}
    end)
    |> Enum.into(%{})
  end

  defp process_periodic_results(%{"year" => year, "month" => month, "id" => id})
       when id != "" do
    period = "#{year}-#{month}-01" |> Date.from_iso8601!()
    results = periodic_results(year, month, id)
    {results, period}
  end

  defp process_periodic_results(%{"year" => year, "month" => month}) do
    period = "#{year}-#{month}-01" |> Date.from_iso8601!()
    results = periodic_results(year, month)
    {results, period}
  end

  defp process_periodic_results(_params) do
    date = Timex.today()
    {Context.periodic_stats(date.year, date.month), date}
  end

  defp periodic_results(year, month, id) do
    Context.periodic_stats(id, year, month)
  end

  defp periodic_results(year, month) do
    Context.periodic_stats(year, month)
  end

  defp stats_results(%{client_id: id}, %{"year" => year, "month" => month}) do
    period = "#{year}-#{month}-01" |> Date.from_iso8601!()

    results = Context.periodic_statistics(id, period)
    {results, period}
  end

  defp stats_results(%{client_id: id}, _) do
    {Context.periodic_statistics(id, "#{%{Timex.today() | day: 1}}"), Date.utc_today()}
  end
end
