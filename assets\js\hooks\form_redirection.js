const FormRedirection = {
    createFormField(name, value) {
        const element = document.createElement("input");
        element.type = "hidden";
        element.value = value;
        element.name = name;
        return element;
    },

    appendFormFields(form, names, values) {
        if (!names || !values) return;

        if (Array.isArray(names) && Array.isArray(values)) {
            if (names.length !== values.length) {
                console.error('Names and values arrays must have the same length');
                return;
            }
            names.forEach((name, index) => {
                form.appendChild(this.createFormField(name, values[index]));
            });
        } else {
            form.appendChild(this.createFormField(names, values));
        }
    },

    destroyed() {
        // Clean up any forms that might be left in the DOM
        const forms = document.querySelectorAll('form[data-temp-redirect]');
        forms.forEach(form => form.remove());
    },

    mounted() {
        this.handleEvent("http_redirect", ({ form_data }) => {
            try {
                const { fields, method, action } = form_data;
                if (!fields || !method || !action) {
                    throw new Error('Missing required form data');
                }

                const form = document.createElement("form");
                form.setAttribute('data-temp-redirect', 'true');
                form.method = method;
                form.action = action;
                
                this.appendFormFields(form, fields.names, fields.values);
                
                document.body.appendChild(form);
                form.submit();
            } catch (error) {
                console.error('Form redirect error:', error);
            }
        });
    }
};

export default FormRedirection;