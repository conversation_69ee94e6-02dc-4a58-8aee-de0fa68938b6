defmodule AppWeb.LicenseLive.FormComponent do
  use AppWeb, :live_component
  alias App.Accounts
  alias App.Roles
  alias App.{Service.Functions.AccessRoleLive, Utilities}
  alias App.Roles.AccessRoles
  alias App.Licenses
  alias App.Licenses.License

  @icons [
    {"Share", "share"},
    {"Shield", "shield"},
    {"Refresh", "refresh"},
    {"circle", "x-circle"},
    {"Office Building", "office-building"},
    {"Location Marker", "location-marker"},
    {"Document Text", "document-text"},
    {"Users", "users"},
    {"Briefcase", "briefcase"},
    {"User", "user"},
    {"Chart Bar", "chart-bar"}
  ]

  alias App.Service.ServiceLicenseMaintenance.Functions

  # Render form
  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.simple_form
        for={@form}
        id="license-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-2 gap-4">
          <.input
            field={@form[:name]}
            type="text"
            placeholder="Enter a name"
            required
            label={raw(~c"Name <span class='text-rose-500'>*</span>")}
          />
          <.input
            field={@form[:type]}
            type="select"
            options={["BUSINESS", "INDIVIDUAL", "BOTH"]}
            label={raw(~c"Type <span class='text-rose-500'>*</span>")}
            required
          />
          <.input
            field={@form[:form_number]}
            type="number"
            label={raw(~c"Form Number <span class='text-rose-500'>*</span>")}
            placeholder="e.g. 1"
            required
          />
          <.input
            field={@form[:security_act_no]}
            type="number"
            label={raw(~c"Security Act No <span class='text-rose-500'>*</span>")}
            placeholder="e.g. 1"
            required
          />
          <.input
            field={@form[:primary_key]}
            type="text"
            label={raw(~c"Primary Key <span class='text-rose-500'>*</span>")}
            placeholder="e.g. company_name"
            required
          />
          <.input
            field={@form[:section]}
            type="number"
            label={raw(~c"Section <span class='text-rose-500'>*</span>")}
            placeholder="e.g. 1"
            required
          />
          <.input
            field={@form[:amount]}
            type="number"
            label={raw(~c"Amount <span class='text-rose-500'>*</span>")}
            placeholder="e.g. 100"
            required
          />
          <.input
            field={@form[:other_fees]}
            type="number"
            label={raw(~c"Compensation Levy <span class='text-rose-500'>*</span>")}
            placeholder="e.g. 100"
            required
          />
          <.input
            field={@form[:color]}
            type="color"
            label={raw(~c"Color <span class='text-rose-500'>*</span>")}
            required
          />
          <.input
            field={@form[:count_down_days]}
            type="number"
            label={raw(~c"Count Down Days <span class='text-rose-500'>*</span>")}
            required
          />
          <.input
            field={@form[:icon]}
            type="select"
            prompt="Select"
            options={@icons}
            label={raw(~c"Icon <span class='text-rose-500'>*</span>")}
            required
          />
          <.input
            field={@form[:role_id]}
            type="select"
            prompt="Select"
            options={@roles}
            label={raw(~c"Licensed User Role <span class='text-rose-500'>*</span>")}
            required
          />
          <.input
            field={@form[:categories_id]}
            type="select"
            prompt="Select"
            options={@categories}
            label={raw(~c"License Category <span class='text-rose-500'>*</span>")}
          />
          <.input
            field={@form[:type]}
            type="select"
            options={["BUSINESS", "INDIVIDUAL", "BOTH"]}
            label={raw(~c"Business <span class='text-rose-500'>*</span>")}
            required
          />
          <.input
            field={@form[:certificate_id]}
            type="select"
            prompt="Select"
            options={@certificates}
            label={raw(~c"License Certificate <span class='text-rose-500'>*</span>")}
          />
          <.input
            field={@form[:associated_license_id]}
            type="select"
            prompt="Select"
            options={@licenses}
            label={raw(~c"Associated License")}
          />
          <div class="col-span-2">
            <.input
              field={@form[:note]}
              type="textarea"
              placeholder="Description"
              label={raw(~c"Description <span class='text-rose-500'>*</span>")}
              required
            />
          </div>
        </div>

        <:actions>
          <div class="align-left">
            <.button type="button" phx-click="close_model">Close</.button>

            <.button type="submit" phx-disable-with="submitting...">Submit</.button>
          </div>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{action: action} = assigns, socket) do
    c_mount(action, assigns, socket)
  end

  defp c_mount(:edit, %{license: license} = assigns, socket) do
    changeset = Licenses.change_license(license)

    socket
    |> assign(assigns)
    |> assign(:icons, @icons)
    |> assign(:categories, Licenses.select_categories())
    |> assign(:roles, Roles.get_license_roles())
    |> assign(:certificates, Utilities.get_certificates!())
    |> assign(:licenses, Licenses.select_license_not_in([license.id]))
    |> assign_form(changeset)
    |> ok()
  end

  defp c_mount(:new, %{license: license} = assigns, socket) do
    changeset = Licenses.change_license(license)

    socket
    |> assign(assigns)
    |> assign(:icons, @icons)
    |> assign(:categories, Licenses.select_categories())
    |> assign(:certificates, Utilities.get_certificates!())
    |> assign(:roles, Roles.get_license_roles())
    |> assign(:licenses, Licenses.select_license_not_in())
    |> assign_form(changeset)
    |> ok()
  end

  @impl true
  def handle_event("validate", %{"license" => params}, socket) do
    changeset =
      socket.assigns.license
      |> Licenses.change_license(params)
      |> Map.put(:action, :validate)

    socket
    |> assign_form(changeset)
    |> noreply()
  end

  def handle_event("save", params, socket) do
    save_user(socket, socket.assigns.action, params)
    |> noreply()
  end

  defp save_user(socket, :edit, params) do
    case Functions.update(socket, socket.assigns.license, params) do
      {:ok, user} ->
        notify_parent({:saved, user, "License Updated Successfully"})
        socket

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset)}
    end
  end

  defp save_user(socket, :new, params) do
    case Functions.create(socket, params) do
      {:ok, license} ->
        notify_parent({:saved, license, "License Created Successfully"})
        socket

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset)}
    end
  end

  defp notify_parent(msg) do
    send(self(), {__MODULE__, msg})
  end
end
