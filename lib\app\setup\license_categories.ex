defmodule App.SetUp.LicenseCategories do
  @moduledoc false
  alias Ecto.Adapters.SQL.Sandbox
  alias App.{Repo}
  alias App.Licenses.{LicenseCategories, License, LicenseSandbox}

  defp run_seeds_db(schema), do: Repo.insert!(schema)

  def init({sec_ex, dealer, inv_adviser, rep, ros, dealer_certificate, rep_certificate}) do
    category_1 =
      %LicenseCategories{
        name: "Authorization of Funds",
        description: "Description for Authorization of Funds",
        color: "#FF5733",
        type: "BOTH",
        status: 1
      }
      |> run_seeds_db()

    category_2 =
      %LicenseCategories{
        name: "SRO",
        description: "SRO Category",
        color: "#334557",
        type: "BOTH",
        status: 1
      }
      |> run_seeds_db()

    category_3 =
      %LicenseCategories{
        name: "Licencing",
        description: "This is a Licencing Category",
        color: "#3357FF",
        type: "BOTH",
        status: 1
      }
      |> run_seeds_db()

    category_4 =
      %LicenseCategories{
        name: "Registration of Securities",
        description: "Description for Registration of Securities",
        color: "#FF33A1",
        type: "BOTH",
        status: 1
      }
      |> run_seeds_db()

    category_5 =
      %LicenseCategories{
        name: "Sandbox",
        description: "Sandbox Category",
        color: "#FF8C33",
        type: "BUSINESS",
        status: 1
      }
      |> run_seeds_db()

    licenses(
      {sec_ex, dealer, inv_adviser, rep, ros},
      {category_1, category_2, category_3, category_4, category_5},
      {dealer_certificate, rep_certificate}
    )
  end

  def licenses(
        {sec_ex, dealer, inv_adviser, rep, ros},
        {category_1, category_2, category_3, category_4, category_5},
        {dealer_certificate, rep_certificate}
      ) do
    IO.inspect("LICENSES")

    new_path = AppWeb.LiveFunctions.static_path("/sample")

    form_1 =
      run_seeds_db(%License{
        form_number: 1,
        name: "Securities Exchange Licence",
        section: 8,
        security_act_no: 354,
        primary_key: "company_name",
        note: """
        Application is hereby made to be licensed as a securities exchange under the Act and the following statements are made in respect thereof:
        Note:

        This application must be accompanied by-

        (a)	the last audited balance sheet and profit and loss account certified as required by rule 6 of the Securities (Licensing and Fees) Rules, 1993;

        (b)	a certified true copy of the rules of the proposed securities exchange;

        (b)	a certified true copy of the applicant's certificate of incorporation; and

        (c)	a certified true copy of the applicant's memorandum and articles of association.
        """,
        role_id: sec_ex.id,
        categories_id: category_3.id,
        status: "A",
        color: "#4F46E5",
        icon: "shield",
        amount: 44444.80,
        type: "BOTH",
        file_path: "#{new_path}/form1.doc"
      })

    form_2 =
      run_seeds_db(%License{
        form_number: 2,
        name: "Dealer Application Licence",
        section: 21,
        security_act_no: 354,
        primary_key: "applicant_name",
        note: """
        Application is hereby made for a Dealer's Licence under the Act and the following statements are made in respect thereof:
        Notes:
        (1) If space is insufficient to provide details, please attach annexure(s). Any such annexure should be identified as such and signed by the signatory to this application.

        (2)	This application must be accompanied by the last audited balance-sheet and profit and loss account certified as required by rule six of the Securities (Licensing and Fees) Rules, 1993.
        """,
        role_id: dealer.id,
        categories_id: category_3.id,
        status: "A",
        color: "#10B981",
        icon: "chart-bar",
        amount: 26666.40,
        other_fees: 4444.80,
        type: "BUSINESS",
        file_path: "#{new_path}/form2.doc",
        certificate_id: dealer_certificate.id
      })

    # form_3 =
    #   run_seeds_db(%License{
    #     form_number: 3,
    #     name: "Individual Investment Licence",
    #     section: 21,
    #     security_act_no: 354,
    #     primary_key: "applicant_name",
    #     note: """
    #     Application is hereby made for an investment adviser's licence under the Act and the following statements are made in respect thereof:
    #     Note:
    #     (1) If space is insufficient to provide details, please attach annexure(s). Any such annexure should be identified as such and signed by the signatory to this application.
    #     (2) This application shall be accompanied by a detailed statement of the applicant's assets and liabilities and shall be signed by the applicant.
    #     """,
    #     role_id: individual_inv.id,
    #     categories_id: category_3.id,
    #     status: "D",
    #     color: "#F59E0B",
    #     icon: "user",
    #     amount: 13333.60,
    #     other_fees: 4444.80,
    #     type: "INDIVIDUAL",
    #     file_path: "#{new_path}/form3.doc"
    #   })

    form_4 =
      run_seeds_db(%License{
        form_number: 4,
        name: "Investment Adviser Licence",
        section: 21,
        security_act_no: 354,
        primary_key: "applicant_name",
        note: """
        Application is hereby made for an investment adviser's licence under the Act and the following statements are made in respect thereof:
        Note:
        (1) If space is insufficient to provide details, please attach annexure(s). Any such annexure should be identified as such and signed by the signatory to this application.
        (2) This application shall be accompanied by a detailed statement of the applicant's assets and liabilities and shall be signed by the applicant.
        """,
        role_id: inv_adviser.id,
        categories_id: category_3.id,
        status: "A",
        color: "#EF4444",
        icon: "briefcase",
        amount: 13333.60,
        other_fees: 4444.80,
        type: "INDIVIDUAL",
        file_path: "#{new_path}/form4.doc"
      })

    form_6 =
      run_seeds_db(%License{
        form_number: 6,
        name: "Representative Licence",
        section: 21,
        security_act_no: 354,
        primary_key: "applicant_name",
        note: """
        Application is hereby made for:
        A dealer's representative's/an investment representative's* licence and the following statements are made in respect of the application.
        """,
        role_id: rep.id,
        categories_id: category_3.id,
        status: "A",
        color: "#8B5CF6",
        icon: "users",
        amount: 5333.6,
        type: "INDIVIDUAL",
        file_path: "#{new_path}/form6.doc",
        certificate_id: rep_certificate.id
      })

    form_8 =
      run_seeds_db(%License{
        form_number: 8,
        name: "Record of Securities",
        section: 21,
        security_act_no: 354,
        primary_key: "applicant_name",
        note: """
        *State how securities acquired or disposed of and if acquired or disposed of on the securities market, give broker's name.
        """,
        role_id: ros.id,
        categories_id: category_3.id,
        status: "A",
        color: "#EC4899",
        icon: "document-text",
        type: "BOTH",
        file_path: "#{new_path}/form8.doc"
      })

    form_9 =
      run_seeds_db(%License{
        form_number: 9,
        name: "Notice of Place of Record",
        section: 21,
        security_act_no: 354,
        primary_key: "applicant_name",
        note: """
        NOTICE OF PLACE AT WHICH RECORD IS TO BE KEPT
        """,
        categories_id: category_3.id,
        status: "A",
        color: "#06B6D4",
        icon: "location-marker",
        require_license: true,
        type: "BUSINESS",
        file_path: "#{new_path}/form9.doc"
      })

    form_10 =
      run_seeds_db(%License{
        form_number: 10,
        name: "Notice of Change of Place of Business",
        section: 21,
        security_act_no: 354,
        primary_key: "licensee_name",
        note: """
        NOTICE OF CHANGE OF PLACE OF BUSINESS AND CHANGE OF PLACE AT WHICH RECORD IS KEPT
        """,
        categories_id: category_3.id,
        status: "A",
        color: "#F97316",
        icon: "office-building",
        require_license: true,
        type: "BUSINESS",
        file_path: "#{new_path}/form10.doc"
      })

    form_11 =
      run_seeds_db(%License{
        form_number: 11,
        name: "Notification of Cessation of Business",
        section: 21,
        security_act_no: 354,
        primary_key: "licensee_name",
        note: """
        NOTIFICATION OF CESSATION OF BUSINESS
        """,
        categories_id: category_3.id,
        status: "A",
        color: "#6366F1",
        icon: "x-circle",
        require_license: true,
        type: "BUSINESS",
        file_path: "#{new_path}/form11.doc"
      })

    form_12 =
      run_seeds_db(%License{
        form_number: 12,
        name: "Notice of Change of Reps Principal",
        section: 21,
        security_act_no: 354,
        primary_key: "licensee_name",
        note: """
        NOTICE OF CHANGE OF REPRESENTATIVE'S PRINCIPAL AND NOTICE OF CHANGE OF PLACE AT WHICH RECORD IS KEPT
        """,
        categories_id: category_3.id,
        status: "A",
        color: "#14B8A6",
        icon: "refresh",
        require_license: true,
        type: "INDIVIDUAL",
        file_path: "#{new_path}/form12.doc"
      })

    run_seeds_db(%License{
      name: "Share Transfer Agent Application Form Final",
      section: 21,
      security_act_no: 354,
      primary_key: "applicant_name",
      note: """
      NOTICE OF CHANGE OF REPRESENTATIVE'S PRINCIPAL AND NOTICE OF CHANGE OF PLACE AT WHICH RECORD IS KEPT
      """,
      role_id: 14,
      categories_id: category_3.id,
      status: "A",
      color: "#8B5CF6",
      type: "BUSINESS",
      icon: "share"
    })

    sandbox =
      run_seeds_db(%License{
        name: "Regulatory Sandbox Framework for Capital Markets",
        primary_key: "participant_name",
        note: """
        Regulatory Sandbox Framework for Capital Markets
        """,
        categories_id: category_5.id,
        status: "A",
        color: "#8B5CF6",
        type: "BUSINESS",
        icon: "office-building"
      })

    form_2
    |> License.changeset(%{associated_license_id: form_6.id})
    |> Repo.update()

    IO.inspect("SANDBOX APPLICATION")

    {form_1, form_2, form_4, form_6, form_8, form_9, form_10, form_11, form_12, sandbox,
     category_3, category_5}
  end
end
