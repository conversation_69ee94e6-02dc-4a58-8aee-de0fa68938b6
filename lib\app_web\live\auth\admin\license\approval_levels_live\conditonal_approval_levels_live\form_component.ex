defmodule AppWeb.ConditionalLevelsLive.FormComponent do
  use AppWeb, :live_component
  alias App.{Utilities, Roles, Licenses}

  alias App.Service.ServiceConditionalLevels.Functions
  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.simple_form
        for={@form}
        id="city-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-2 gap-4">
          <.input
            field={@form[:approval_status]}
            phx-hook="validateIntegerHook"
            type="number"
            min="1"
            placeholder="Enter Approval Level"
            required
            label={raw(~c"Approval Level <span class='text-rose-500'>*</span>")}
          />
          <.input
            field={@form[:categories_id]}
            type="select"
            prompt="--Select Category--"
            options={@category}
            placeholder="Select Category"
            required
            label={raw(~c"Category <span class='text-rose-500'>*</span>")}
          />
          <.input
            field={@form[:department_id]}
            type="select"
            prompt="--Select Department--"
            options={@role_id_data}
            placeholder="Select Department"
            required
            label={raw(~c"Department <span class='text-rose-500'>*</span>")}
          />
          <.input
            field={@form[:role_id]}
            type="select"
            prompt="--Select Role--"
            options={@select_role}
            placeholder="Select Role"
            required
            label={raw(~c"Role <span class='text-rose-500'>*</span>")}
          />
        </div>

        <:actions>
          <div class="align-left">
            <.button type="button" phx-click="close_model">Close</.button>

            <%= if @form.source.valid? do %>
              <.button type="submit" phx-disable-with="submitting...">Submit</.button>
            <% end %>
          </div>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{action: action} = assigns, socket) do
    c_mount(action, assigns, socket)
  end

  def c_mount(:edit, %{record: record} = assigns, socket) do
    {select_role, department_id} = Roles.select_department_role_access_role(record.role_id)

    changeset =
      Utilities.change_conditonal_approval_levels(record, %{
        "department_id" => to_string(department_id)
      })

    socket
    |> assign(assigns)
    |> assign(:select_role, select_role)
    |> assign(:role_id_data, Roles.select_department_role())
    |> assign(:category, Licenses.get_categories!())
    |> assign_form(changeset)
    |> ok()
  end

  def c_mount(:new, %{record: record} = assigns, socket) do
    changeset = Utilities.change_conditonal_approval_levels(record)

    socket
    |> assign(assigns)
    |> assign(:select_role, [])
    |> assign(:role_id_data, Roles.select_department_role())
    |> assign(:category, Licenses.get_categories!())
    |> assign_form(changeset)
    |> ok()
  end

  @impl true
  def handle_event("validate", %{"conditional_levels" => params} = attrs, socket) do
    changeset =
      socket.assigns.record
      |> Utilities.change_conditonal_approval_levels(params)
      |> Map.put(:action, :validate)

    role =
      if attrs["_target"] == ["conditional_levels", "department_id"] do
        Roles.select_access_role_from_department_by(params["department_id"])
      else
        socket.assigns.select_role
      end

    socket
    |> assign_form(changeset)
    |> assign(:select_role, role)
    |> noreply()
  end

  def handle_event("save", %{"conditional_levels" => params}, socket) do
    save_record(socket, socket.assigns.action, params)
    |> noreply()
  end

  defp save_record(socket, :edit, params) do
    case Functions.update(socket, socket.assigns.record, params) |> IO.inspect() do
      {:ok, record} ->
        notify_parent({:saved, record, "Approval Level Updated Successfully"})
        socket

      {:error, %Ecto.Changeset{} = changeset} ->
        assign_form(socket, changeset)
    end
  end

  defp save_record(socket, :new, params) do
    case Functions.create(socket, params) do
      {:ok, record} ->
        notify_parent({:saved, record, "Approval Level Created Successfully"})
        socket

      {:error, %Ecto.Changeset{} = changeset} ->
        assign_form(socket, changeset)
    end
  end

  defp notify_parent(msg) do
    send(self(), {__MODULE__, msg})
  end
end
