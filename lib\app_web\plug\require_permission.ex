defmodule AppWeb.Plugs.RequirePermission do
  import Plug.Conn
  import Phoenix.Controller
  alias App.Roles

  def init(permission_code), do: permission_code

  def call(conn, permission_code) do
    user = conn.assigns[:current_user]

    if user && Roles.user_has_permission?(user.id, user.role_id, permission_code) do
      conn
    else
      conn
      |> put_flash(:error, "You don't have permission to access this page.")
      |> redirect(to: "/")
      |> halt()
    end
  end
end

# Usage in router:
# pipe_through [:browser, :require_authenticated_user, {AppWeb.Plugs.RequirePermission, "users.manage"}]
