defmodule App.LicenseReviews.Comments do
  use Ecto.Schema
  import Ecto.Changeset

  schema "comments" do
    field :comment, :string
    field :level, :integer, default: 0
    belongs_to :user, App.Accounts.User
    belongs_to :application, App.Licenses.LicenseMapping

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(comment, attrs) do
    comment
    |> cast(attrs, [:comment, :level, :user_id, :application_id])
    |> validate_required([:comment, :level, :user_id, :application_id])
  end
end
