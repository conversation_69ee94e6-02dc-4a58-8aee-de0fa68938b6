defmodule Notification.Notification.SmsLogs do
  use AppWeb, :schema
  import Ecto.Changeset
  alias Notification.Repo

  @columns [
    :type,
    :country_code,
    :network_name,
    :country,
    :mobile,
    :message,
    :count,
    :filename,
    :time,
    :status,
    :courier_type,
    :sender,
    :sender_id,
    :source,
    :refcode,
    :courier,
    :client_id,
    :initiator_id,
    :msg_ref_number,
    :remote_ip,
    :sent_at,
    :date,
    :smsc_msg_id,
    :smsc_done_dt,
    :smsc_status,
    :error_code
  ]

  schema "tbl_sms_logs" do
    field(:message, :string)
    field(:mobile, :string)
    field(:status, :string)
    field(:type, :string)
    field(:source, :string, default: "DEFAULT")
    field(:sender, :string)
    field(:courier, :string)
    field(:courier_type, :string)
    field(:refcode, :string)
    field(:count, :integer)
    field(:filename, :string)
    field(:country_code, :string)
    field(:network_name, :string)
    field(:country, :string)
    field(:msg_ref_number, :string)
    field(:remote_ip, :string)
    field(:sent_at, :naive_datetime)
    field(:smsc_msg_id, :string)
    field(:smsc_done_dt, :string)
    field(:smsc_status, :string)
    field(:error_code, :string)

    field(:time, :time, virtual: true)
    field(:date, :date, virtual: true)

    belongs_to(:client, App.Accounts.Client)
    belongs_to(:initiator, App.Accounts.User)
    belongs_to(:sender_ida, App.Accounts.SenderId, foreign_key: :sender_id, type: :id)

    timestamps()
  end

  @doc false
  def changeset(sms_logs, attrs, opts \\ []) do
    sms_logs
    |> cast(attrs, @columns)
    |> validate_required([:mobile, :message, :sender_id, :sender, :client_id])
    |> validate_length(:message, max: 1200, message: " should be 1 - 960 characters")
    |> unique_constraint(:msg_ref_number,
      name: :tbl_sms_logs_msg_ref_number_mobile_index,
      message: "DUBLICATE_MSG_REF"
    )
    |> validate_msg()
    |> validate_recipient
    |> put_ref_number
    |> put_msg_count
    |> put_net_details
    |> validate_phone(opts)
  end

  def send_changeset(sms_logs, attrs, opts \\ []) do
    sms_logs
    |> cast(attrs, @columns)
    |> validate_required([:mobile, :message, :sender_id, :sender, :client_id])
    |> validate_length(:message, max: 1200, message: " should be 1 - 960 characters")
    |> unique_constraint(:msg_ref_number,
      name: :tbl_sms_logs_msg_ref_number_mobile_index,
      message: "DUBLICATE_MSG_REF"
    )
    |> validate_msg()
    |> validate_recipient
    |> put_ref_number
    |> put_msg_count
    |> put_net_details
    |> send_validate_phone(opts)
  end

  defp send_validate_phone(changeset, _opts) do
    changeset
    |> validate_length(:mobile, min: 2, max: 12, message: "should be 12 characters")
    |> validate_format(:mobile, ~r/^[0-9]+$/, message: "must be numbers only")
  end

  defp validate_phone(changeset, _opts) do
    changeset
    |> validate_length(:mobile, min: 2, max: 12, message: "should be 12 characters")
    |> unsafe_validate_unique(:mobile, Notify.NotifyRepo)
    |> unique_constraint(:mobile)
    |> validate_format(:mobile, ~r/^[0-9]+$/, message: "must be numbers only")
  end

  def upload_changeset(sms_logs, attrs) do
    sms_logs
    |> cast(attrs, @columns)
    |> validate_required([:message, :sender_id, :sender, :client_id, :filename])
    |> validate_length(:message, max: 1200, message: " should be 1 - 960 characters")
    |> unique_constraint(:msg_ref_number,
      name: :tbl_sms_logs_msg_ref_number_mobile_index,
      message: "DUBLICATE_MSG_REF"
    )
    |> validate_msg()
    |> put_ref_number
    |> put_msg_count
    |> put_net_details
  end

  def schedule_multicast_changeset(sms_logs, attrs) do
    sms_logs
    |> cast(attrs, @columns)
    |> validate_required([:date, :time, :sender_id, :sender, :client_id, :filename])
    |> unique_constraint(:msg_ref_number,
      name: :tbl_sms_logs_msg_ref_number_mobile_index,
      message: "DUBLICATE_MSG_REF"
    )
    |> put_ref_number
    |> put_msg_count
    |> put_net_details
  end

  def multicast_changeset(sms_logs, attrs) do
    sms_logs
    |> cast(attrs, @columns)
    |> validate_required([:sender_id, :sender, :client_id, :filename])
    |> continue_changeset()
  end

  defp continue_changeset(changeset) do
    changeset
    |> unique_constraint(:msg_ref_number,
      name: :tbl_sms_logs_msg_ref_number_mobile_index,
      message: "DUBLICATE_MSG_REF"
    )
    |> put_ref_number
    |> put_msg_count
    |> put_net_details
  end

  def multicast_changeset(sms_logs, attrs) do
    sms_logs
    |> cast(attrs, @columns)
    |> validate_required([:sender_id, :sender, :client_id, :filename])
    |> validate_length(:message, max: 1200, message: " should be 1 - 960 characters")
    |> unique_constraint(:msg_ref_number,
      name: :tbl_sms_logs_msg_ref_number_mobile_index,
      message: "DUBLICATE_MSG_REF"
    )
    |> validate_msg()
    |> put_ref_number
    |> put_msg_count
    |> put_net_details
  end

  def validate_msg(%Ecto.Changeset{changes: %{message: message}} = changeset) do
    case SmsPartCounter.detect_encoding(message) do
      {:ok, "gsm_7bit"} ->
        changeset

      _ ->
        add_error(changeset, :message, " contains invalid GSM characters")
    end
  end

  def validate_msg(changeset), do: changeset

  defp validate_recipient(%Ecto.Changeset{valid?: true, changes: %{mobile: mobile}} = changeset) do
    {_, resp} = validate_mobile(mobile)

    put_change(
      changeset,
      :country,
      if(Map.has_key?(resp, :country), do: resp.country, else: "Unknown")
    )
    |> put_change(
      :country_code,
      "#{if Map.has_key?(resp, :phone_number), do: resp.phone_number.country_code, else: "Unknown"}"
    )
    |> put_change(:mobile, set_mobile(resp, mobile))
    |> put_change(:status, resp.status)
    |> put_change(:sent_at, NaiveDateTime.truncate(NaiveDateTime.utc_now(), :second))
  end

  defp validate_recipient(changeset), do: changeset

  defp set_mobile(%{phone_number: mobile}, _),
    do: "#{mobile.country_code}#{mobile.national_number}"

  defp set_mobile(_, mobile), do: mobile

  defp put_net_details(changeset) do
    phone = get_field(changeset, :mobile)
    status = get_field(changeset, :status)
    type = get_field(changeset, :type)

    with "PENDING" <- status do
      {net, net_type} = network_details(phone)

      with "SINGLE" <- type do
        put_change(changeset, :status, "SENT")
      else
        _ ->
          status = if net_type == "LOCAL", do: status, else: "Invalid phone number"
          put_change(changeset, :status, status)
      end
      |> put_change(:network_name, net)
      |> put_change(:courier_type, net_type)
    else
      _ -> changeset
    end
  end

  def put_ref_number(changeset) do
    # ref_num = "#{System.monotonic_time()}#{System.unique_integer([:positive])}"
    put_change(changeset, :refcode, Ecto.UUID.generate())
  end

  def put_msg_count(%Ecto.Changeset{changes: %{message: message}} = changeset)
      when message != "" and not is_nil(message) do
    put_change(changeset, :count, msg_count(message))
  end

  def put_msg_count(changeset), do: changeset

  defp network_details(mobile) do
    with "260" <- mobile |> String.slice(0..2) do
      with true <- ~w(26096 26076) |> Enum.member?(mobile |> String.slice(0..4)) do
        {"MTN ZM", "LOCAL"}
      else
        _ ->
          with true <- ~w(26097 26077) |> Enum.member?(mobile |> String.slice(0..4)) do
            {"Airtel ZM", "LOCAL"}
          else
            _ ->
              with "26095" <- mobile |> String.slice(0..4) do
                {"Zamtel", "LOCAL"}
              else
                _ ->
                  {"Unknown", "LOCAL"}
              end
          end
      end
    else
      _ ->
        {"International", "INTL"}
    end
  end

  def msg_count(message) do
    msg_len = String.length(message)
    {int, rem} = Integer.parse("#{msg_len / 160}")

    case rem do
      rem when rem != ".0" ->
        int + 1

      _ ->
        if int == 0, do: 1, else: int
    end
  end

  def validate_mobile(mobile) do
    mobile =
      case String.slice(String.trim("#{mobile}"), 0..2) do
        sub_str when sub_str in ~w(096 076 097 095 077) ->
          "26#{mobile}"

        _ ->
          mobile
      end

    with {:ok, phone_num} <- Phone.parse(mobile) do
      with {:ok, phone_number} <- ExPhoneNumber.parse(mobile, phone_num.a2) do
        with true <- ExPhoneNumber.is_possible_number?(phone_number) do
          status =
            if Map.get(phone_num, :country) == "Unknown",
              do: "Invalid phone number",
              else: "PENDING"

          {:ok, %{country: phone_num.country, phone_number: phone_number, status: status}}
        else
          false -> {:error, %{status: "Invalid phone number"}}
        end
      else
        {:error, _reason} -> {:error, %{status: "Invalid phone number"}}
      end
    else
      {:error, _reason} -> {:error, %{status: "Invalid phone number"}}
    end
  end
end
