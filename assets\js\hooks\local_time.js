export const LocalTime = {
    getFormattedDateTime() {
        const today = new Date();
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const date = `${today.getDate()}`.padStart(2, '0') + ' ' + 
            months[today.getMonth()] + ' ' + 
            today.getFullYear();
        const time = `${today.getHours()}`.padStart(2, '0') + ":" + 
            `${today.getMinutes()}`.padStart(2, '0') + ":" + 
            `${today.getSeconds()}`.padStart(2, '0');
        return `${date} ${time}`;
    },

    updateTime() {
        this.el.innerHTML = this.getFormattedDateTime();
    },

    mounted() {
        this.updateTime();
        this.interval = setInterval(() => this.updateTime(), 1000);
    },

    destroyed() {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
    }
}
