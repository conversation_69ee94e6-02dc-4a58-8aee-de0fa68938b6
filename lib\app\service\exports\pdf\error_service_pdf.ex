defmodule App.Service.Export.PdfErrorServicePdf do
  alias App.Service.Table.ServiceErrors

  alias App.Service.Export.{
    Functions
  }

  def index(assigns, payload) do
    results =
      ServiceErrors.export(assigns, payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "code" => data.code,
          "error_desc" => data.error_desc,
          "created_by" => data.created_by,
          "inserted_at" => data.inserted_at,
          "updated_at" => data.updated_at
        }
      end)
      |> Enum.map(fn data ->
        """
          <tr>
            <td style="text-align: center;">{{ code }}</td>
            <td style="text-align: center;">{{ error_desc }}</td>
            <td style="text-align: center;">{{ created_by }}</td>
            <td style="text-align: center;">{{ inserted_at }}</td>
            <td style="text-align: center;">{{ updated_at }}</td>

          </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/error_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Messaging Errors",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end
