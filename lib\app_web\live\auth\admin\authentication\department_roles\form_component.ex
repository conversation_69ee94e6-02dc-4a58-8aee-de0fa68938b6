defmodule AppWeb.Settings.DepartmentRoleLive.FormComponent do
  #  @moduledoc false
  use AppWeb, :live_component

  alias App.Roles

  alias App.{
    # Roles.DepartmentRoles,
    Service.SystemRoleLive.Function
  }

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.simple_form for={@form} id="document-form" phx-target={@myself} phx-submit="save">
        <.input field={@form[:name]} type="text" placeholder="Role name" label="Name" />
        <.input
          field={@form[:description]}
          type="textarea"
          placeholder="Role description"
          label="Description"
        /> <.input field={@form[:remarks]} type="textarea" placeholder="Remarks" label="Remarks" />
        <:actions>
          <.button phx-disable-with="Saving...">Save</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{struct: struct} = assigns, socket) do
    changeset = Roles.change_department_roles(struct)

    {
      :ok,
      socket
      |> assign(assigns)
      |> assign_form(changeset, :form)
    }
  end

  @impl true
  def handle_event(target, params, socket), do: handle_event_switch(target, params, socket)

  def handle_event_switch(target, params, socket) do
    case target do
      "save" -> save_user(socket, socket.assigns.action, params["department_roles"])
      "validate" -> validate(params, socket)
    end
  end

  def validate(%{"department_roles" => department_roles} = _params, socket) do
    changeset =
      socket.assigns.struct
      |> Roles.change_department_roles(department_roles)

    {:noreply, assign_form(socket, changeset, :form)}
  end

  defp save_user(socket, :new, params) do
    new_params =
      Map.put(params, "deleted", false)
      |> Map.put("editable", true)

    case Function.create_system_roles(socket, new_params) do
      {:ok, message, _maker_checker} ->
        LiveFunctions.notify_parent(message)
        {:noreply, socket}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset, :form)}
    end
  end

  defp save_user(%{assigns: assigns} = socket, :edit, params) do
    case Function.update_department_roles(socket, assigns.struct, params) do
      {:ok, message, _maker_checker} ->
        LiveFunctions.notify_parent(message)
        {:noreply, socket}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset, :form)}
    end
  end
end
