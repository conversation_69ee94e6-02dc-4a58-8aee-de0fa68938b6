defmodule App.Service.Table.LicenceApplications do
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false

  alias App.{
    Licenses,
    Licenses.LicenseMapping
  }

  alias App.{Repo, Utilities}

  @pagination [page_size: 10]

  def index(assigns, params) do
    compose_query(params, assigns)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def export(assigns, params) do
    compose_query(params, assigns)
    |> Repo.all()
  end

  def compose_query(params, assigns) do
    IO.inspect(params, label: "Params in compose_query")

    LicenseMapping
    |> join(:left, [a], b in assoc(a, :license))
    |> where(
      [a],
      a.approval_status == true and a.condition_tracking == false and
        is_nil(a.associated_license_id)
    )
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> check_status(assigns, params["path"])
    |> compose_select()
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"record_name", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.record_name, ^sanitize_term(value))
        )

      {"status", value}, query when byte_size(value) > 0 ->
        where(query, [a], a.status == type(^value, :integer))

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp check_status(query, assigns, status) do
    approval_levels = Utilities.count_approval_levels()
    levels = Utilities.approval_levels()

    approve_level =
      case levels do
        nil -> nil
        _ -> levels
      end

    IO.inspect(approve_level, label: "Approval Level")

    case status do
      "New" ->
        status =
          Licenses.get_license_approval_status_list(
            assigns.role_department,
            assigns.current_user.role_id
          )
          |> Enum.filter(&(!is_nil(&1)))
          |> Enum.uniq()

        where(query, [a], a.status in ^status)

      "Market_Operations" ->
        status =
          Licenses.get_license_approval_status(
            assigns.role_department,
            assigns.current_user.role_id
          )

        where(query, [a], a.status > ^status)

      "Returned_to_Applicant" ->
        subquery = Licenses.get_returned_to_applicant_query(assigns.current_user.id)
        where(query, [a], a.id in subquery(subquery) and a.status == ^(-1))

      "conditional" ->
        where(query, [a], a.approval_status == true and a.condition_tracking == true)

      "Submitted_to_Supervisor" ->
        status_list =
          Licenses.get_license_approval_status_list(
            assigns.role_department,
            assigns.current_user.role_id
          )

        subquery = Licenses.get_approved_application_query(assigns.current_user.id)
        new_status = Enum.map(status_list, &(&1 + 1))

        where(
          query,
          [a],
          a.id in subquery(subquery) and a.status in ^new_status
        )

      "Subordinate" ->
        where(query, [a], a.approval_status == true and not is_nil(a.count_down_start_date))

      "LC" ->
        where(query, [a], a.status == 8 and 6 in ^approve_level)

      "Board" ->
        where(query, [a], a.status == 9 and 17 in ^approve_level)

      "Returned_from_Supervisor" ->
        where(query, [a], a.status >= 8)

      "Processed" ->
        where(
          query,
          [a],
          a.approved == true
        )
    end
  end

  defp compose_select(query) do
    select(query, [a, b], %{
      id: a.id,
      license_id: a.license_id,
      data: a.data,
      licence_name: b.name,
      certificate_id: b.certificate_id,
      record_name: a.record_name,
      revoked: a.revoked,
      show_summary: a.show_summary,
      status: a.status,
      condition_tracking: a.condition_tracking,
      approved: a.approved,
      inserted_at: a.inserted_at,
      updated_at: a.updated_at
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b],
      fragment("lower(?) LIKE lower(?)", a.record_name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", b.name, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
