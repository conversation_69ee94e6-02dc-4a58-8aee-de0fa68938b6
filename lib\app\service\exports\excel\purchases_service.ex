defmodule App.Service.Export.ExcelPurchasesService do
  @moduledoc false

  alias Elixlsx.{Workbook, Sheet}

  alias App.Service.Table.ServicePurchases

  alias App.Service.Export.{
    ExtraData,
    Functions
  }

  def index(assigns, payload) do
    data_call =
      ServicePurchases.export(assigns, payload)
      |> List.flatten()

    dates =
      Enum.map(data_call, & &1.inserted_at)
      |> Enum.filter(&(!is_nil(&1)))

    start_date =
      Functions.empty_check?(payload["start_date"]) ||
        try do
          Enum.max(dates)
        rescue
          _ -> ""
        end

    end_date =
      Functions.empty_check?(payload["end_date"]) ||
        try do
          Enum.min(dates)
        rescue
          _ -> ""
        end

    reports(data_call, start_date, end_date)
    |> content()
  end

  def reports(posts, start_date, end_date) do
    rows =
      posts
      |> Enum.map(&ExtraData.purchase_service_row/1)

    %Workbook{
      sheets: [
        %Sheet{
          name: "Bundle Purchases",
          rows:
            [
              [["Bundle Purchases", align_horizontal: :center, bold: true]],
              [["FROM #{start_date} TO #{end_date}", align_horizontal: :center, bold: true]],
              ["", "", "", "", "", "", ""],
              ExtraData.purchase_service_header()
            ] ++ rows,
          merge_cells: [{"A1", "D1"}, {"A2", "D2"}]
        }
      ]
    }
  end

  def content(data) do
    Elixlsx.write_to_memory(data, "rates_service#{:os.system_time()}.xlsx")
    |> elem(1)
    |> elem(1)
  end
end
