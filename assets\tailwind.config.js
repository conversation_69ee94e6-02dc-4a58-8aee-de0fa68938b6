// See the Tailwind configuration guide for advanced usage
// https://tailwindcss.com/docs/configuration

const plugin = require("tailwindcss/plugin")
const fs = require("fs")
const path = require("path")

module.exports = {
    content: [
        "./js/**/*.js",
        "../lib/app_web.ex",
        "../lib/app_web/**/*.*ex",
        '../deps/live_select/lib/live_select/*.*ex',
    ],
    theme: {
        borderWidth: {
            DEFAULT: '1px',
            '0': '0',
            '2': '2px',
            '3': '3px',
            '4': '4px',
            '5': '5px',
            '6': '6px',
        },
        extend: {
            spacing: {
                '82vh': '82vh',
            },
            height: {
                60: '55vh',
                55: '55vh',
            },
            maxHeight: {
                '90': '90vh',
                'A4': '1123px,'
            },
            maxWidth: {
                'A4': '794px',
            },
            margin: {
                40: '40px',
            },
            keyframes: {
                "background-shine": {
                    from: {
                        backgroundPosition: "0 0"
                    },
                    to: {
                        backgroundPosition: "-200% 0"
                    }
                },
                "fade-in": {
                    "0%": {
                        opacity: 0
                    },
                    "100%": {
                        opacity: 1
                    },
                },
                "fade-out": {
                    "0%": {
                        opacity: 1
                    },
                    "100%": {
                        opacity: 0
                    },
                },
                "fade-in-down": {
                    "0%": {
                        opacity: 0,
                        transform: "translate3d(0, -100%, 0)",
                    },
                    "100%": {
                        opacity: 1,
                        transform: "translate3d(0, 0, 0)",
                    },
                },
                "fade-in-top-left": {
                    "0%": {
                        opacity: 0,
                        transform: "translate3d(-100%, -100%, 0)",
                    },
                    "100%": {
                        opacity: 1,
                        transform: "translate3d(0, 0, 0)",
                    },
                },
                "fade-in-top-right": {
                    "0%": {
                        opacity: 0,
                        transform: "translate3d(100%, -100%, 0)",
                    },
                    "100%": {
                        opacity: 1,
                        transform: "translate3d(0, 0, 0)",
                    },
                },

                "fade-in-bottom-left": {
                    "0%": {
                        opacity: 0,
                        transform: "translate3d(100%, 100%, 0)",
                    },
                    "100%": {
                        opacity: 1,
                        transform: "translate3d(0, 0, 0)",
                    },
                },
                "fade-in-bottom-right": {
                    "0%": {
                        opacity: 0,
                        transform: "translate3d(-100%, 100%, 0)",
                    },
                    "100%": {
                        opacity: 1,
                        transform: "translate3d(0, 0, 0)",
                    },
                },
                "fade-in-bounce-right": {
                    "0%": {
                        opacity: 0,
                        transform: "translate3d(100%, 0%, 0)",
                    },
                    "33%": {
                        opacity: 0.5,
                        transform: "translate3d(0%, 0%, 0)",
                    },
                    "66%": {
                        opacity: 0.7,
                        transform: "translate3d(20%, 0%, 0)",
                    },
                    "100%": {
                        opacity: 1,
                        transform: "translate3d(0, 0, 0)",
                    },
                },
                "fade-in-bounce-left": {
                    "0%": {
                        opacity: 0,
                        transform: "translate3d(-100%, 0%, 0)",
                    },
                    "33%": {
                        opacity: 0.5,
                        transform: "translate3d(0%, 0%, 0)",
                    },
                    "66%": {
                        opacity: 0.7,
                        transform: "translate3d(-20%, 0%, 0)",
                    },
                    "100%": {
                        opacity: 1,
                        transform: "translate3d(0, 0, 0)",
                    },
                },
                "fade-in-bouncedown": {
                    "0%": {
                        opacity: 0,
                        transform: "translate3d(0%, -100%, 0)",
                    },
                    "33%": {
                        opacity: 0.5,
                        transform: "translate3d(0%, 0%, 0)",
                    },
                    "66%": {
                        opacity: 0.7,
                        transform: "translate3d(0%, -20%, 0)",
                    },
                    "100%": {
                        opacity: 1,
                        transform: "translate3d(0, 0, 0)",
                    },
                },
                "fade-in-bounceup": {
                    "0%": {
                        opacity: 0,
                        transform: "translate3d(0%, 100%, 0)",
                    },
                    "33%": {
                        opacity: 0.5,
                        transform: "translate3d(0%, 0%, 0)",
                    },
                    "66%": {
                        opacity: 0.7,
                        transform: "translate3d(0%, 20%, 0)",
                    },
                    "100%": {
                        opacity: 1,
                        transform: "translate3d(0, 0, 0)",
                    },
                },
                "fade-in-left": {
                    "0%": {
                        opacity: 0,
                        transform: "translate3d(-100%, 0, 0)",
                    },
                    "100%": {
                        opacity: 1,
                        transform: "translate3d(0, 0, 0)",
                    },
                },
                "fade-in-right": {
                    "0%": {
                        opacity: 0,
                        transform: "translate3d(100%, 0, 0)",
                    },
                    "100%": {
                        opacity: 1,
                        transform: "translate3d(0, 0, 0)",
                    },
                },
                "fade-in-up": {
                    "0%": {
                        opacity: 0,
                        transform: "translate3d(0, 100%, 0)",
                    },
                    "100%": {
                        opacity: 1,
                        transform: "translate3d(0, 0, 0)",
                    },
                },
                "fade-out-down": {
                    "0%": {
                        opacity: 1,
                    },
                    "100%": {
                        opacity: 0,
                        transform: "translate3d(0, 100%, 0)",
                    },
                },
                "fade-out-left": {
                    "0%": {
                        opacity: 1,
                    },
                    "100%": {
                        opacity: 0,
                        transform: "translate3d(-100%, 0, 0)",
                    },
                },
                "fade-out-top-left": {
                    "0%": {
                        opacity: 1,
                    },
                    "100%": {
                        opacity: 0,
                        transform: "translate3d(-100%, -100%, 0)",
                    },
                },
                "fade-out-top-right": {
                    "0%": {
                        opacity: 1,
                    },
                    "100%": {
                        opacity: 0,
                        transform: "translate3d( 100%, -100%, 0)",
                    },
                },
                "fade-out-right": {
                    "0%": {
                        opacity: 1,
                    },
                    "100%": {
                        opacity: 0,
                        transform: "translate3d(100%, 0, 0)",
                    },
                },
                "fade-out-up": {
                    "0%": {
                        opacity: 1,
                    },
                    "100%": {
                        opacity: 0,
                        transform: "translate3d(0, -100%, 0)",
                    },
                },
                "slide-in-down": {
                    "0%": {
                        visibility: "visible",
                        transform: "translate3d(0, -100%, 0)",
                    },
                    "100%": {
                        transform: "translate3d(0, 0, 0)",
                    },
                },
                "slide-in-left": {
                    "0%": {
                        visibility: "visible",
                        transform: "translate3d(-100%, 0, 0)",
                    },
                    "100%": {
                        transform: "translate3d(0, 0, 0)",
                    },
                },
                "slide-in-right": {
                    "0%": {
                        visibility: "visible",
                        transform: "translate3d(100%, 0, 0)",
                    },
                    "100%": {
                        transform: "translate3d(0, 0, 0)",
                    },
                },
                "slide-in-up": {
                    "0%": {
                        visibility: "visible",
                        transform: "translate3d(0, 100%, 0)",
                    },
                    "100%": {
                        transform: "translate3d(0, 0, 0)",
                    },
                },
                "slide-out-down": {
                    "0%": {
                        transform: "translate3d(0, 0, 0)",
                    },
                    "100%": {
                        visibility: "hidden",
                        transform: "translate3d(0, 100%, 0)",
                    },
                },
                "slide-out-left": {
                    "0%": {
                        transform: "translate3d(0, 0, 0)",
                    },
                    "100%": {
                        visibility: "hidden",
                        transform: "translate3d(-100%, 0, 0)",
                    },
                },
                "slide-out-right": {
                    "0%": {
                        transform: "translate3d(0, 0, 0)",
                    },
                    "100%": {
                        visibility: "hidden",
                        transform: "translate3d(100%, 0, 0)",
                    },
                },
                "slide-out-up": {
                    "0%": {
                        transform: "translate3d(0, 0, 0)",
                    },
                    "100%": {
                        visibility: "hidden",
                        transform: "translate3d(0, -100%, 0)",
                    },
                },
                "slide-down": {
                    "0%": {
                        transform: "translate3d(0, 0, 0)",
                    },
                    "100%": {
                        transform: "translate3d(0, 100%, 0)",
                    },
                },
                "slide-left": {
                    "0%": {
                        transform: "translate3d(0, 0, 0)",
                    },
                    "100%": {
                        transform: "translate3d(-100%, 0, 0)",
                    },
                },
                "slide-right": {
                    "0%": {
                        transform: "translate3d(0, 0, 0)",
                    },
                    "100%": {
                        transform: "translate3d(100%, 0, 0)",
                    },
                },
                "slide-up": {
                    "0%": {
                        transform: "translate3d(0, 0, 0)",
                    },
                    "100%": {
                        transform: "translate3d(0, -100%, 0)",
                    },
                },
                tada: {
                    "0%": {
                        transform: "scale3d(1, 1, 1)",
                    },
                    "10%, 20%": {
                        transform: "scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg)",
                    },
                    "30%, 50%, 70%, 90%": {
                        transform: "scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg)",
                    },
                    "40%, 60%, 80%": {
                        transform: "scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg)",
                    },
                    "100%": {
                        transform: "scale3d(1, 1, 1)",
                    },
                },
                "spinner-grow": {
                    "0%": {
                        transform: "scale(0)",
                    },

                    "100%": {
                        transform: "none",
                        opacity: "1",
                    },
                },
                "placeholder-wave": {
                    "100%": {
                        maskPosition: "-200% 0%"
                    },
                },
                "show-up-clock": {
                    "0%": {
                        opacity: "0",
                        transform: "scale(0.7)",
                    },
                    "100%": {
                        opacity: "1",
                        transform: "scale(1)",
                    },
                },
                "drop-in": {
                    "0%": {
                        opacity: "0",
                        transform: "scale(0)",
                        animationTimingFunction: "cubic-bezier(0.34, 1.61, 0.7, 1)",
                    },
                    "100%": {
                        opacity: "1",
                        transform: "scale(1)",
                    },
                },
                "drop-out": {
                    "0%": {
                        opacity: "1",
                        transform: "scale(1)",
                        animationTimingFunction: "cubic-bezier(0.34, 1.61, 0.7, 1)",
                    },
                    "100%": {
                        opacity: "0",
                        transform: "scale(0)"
                    },
                },
                jiggle: {
                    "0%": {
                        transform: "scale3d(1, 1, 1)"
                    },
                    "30%": {
                        transform: "scale3d(1.25, 0.75, 1)"
                    },
                    "40%": {
                        transform: "scale3d(0.75, 1.25, 1)"
                    },
                    "50%": {
                        transform: "scale3d(1.15, 0.85, 1)"
                    },
                    "65%": {
                        transform: "scale3d(0.95, 1.05, 1)"
                    },
                    "75%": {
                        transform: "scale3d(1.05, 0.95, 1)"
                    },
                    "100%": {
                        transform: "scale3d(1, 1, 1)"
                    },
                },
                flash: {
                    "0%, 50%, 100%": {
                        opacity: "1"
                    },
                    "25%, 75%": {
                        opacity: "0"
                    },
                },
                shake: {
                    "0%, 100%": {
                        transform: "translateX(0)",
                    },
                    "10%, 30%, 50%, 70%, 90%": {
                        transform: "translateX(-10px)",
                    },
                    "20%, 40%, 60%, 80%": {
                        transform: "translateX(10px)",
                    },
                },
                glow: {
                    "0%": {
                        backgroundColor: "#fcfcfd"
                    },
                    "30%": {
                        backgroundColor: "#fff6cd"
                    },
                    "100%": {
                        backgroundColor: "#fcfcfd"
                    },
                },

                wiggle: {
                    "5%": {
                        transform: "rotate(-5deg)"
                    },
                    "20%": {
                        transform: "rotate(5deg)"
                    },
                    "40%": {
                        transform: "rotate(-5deg)"
                    },
                    "80%": {
                        transform: "rotate(5deg)"
                    }
                },
                "step-fade": {
                    "0%": {
                        opacity: 0,
                        transform: "translateY(-10px)",
                    },
                    "100%": {
                        opacity: 1,
                        transform: "translateY(0)",
                    },
                },
                fadeIn: {
                    '0%': { opacity: '0' },
                    '100%': { opacity: '1' }
                },
                slideIn: {
                    '0%': { transform: 'translateX(100%)' },
                    '100%': { transform: 'translateX(0)' }
                },
                slideOut: {
                    '0%': { transform: 'translateX(0)' },
                    '100%': { transform: 'translateX(-100%)' }
                }
            },
            animation: {
                "background-shine": "background-shine 5s linear infinite",
                fadeIn: 'fade-in 1s ease-in-out 0.25s 1',
                fadeOut: 'fade-out 1s ease-out 0.25s 1',
                fadeInDown: 'fade-in-down 1s ease-in 0.25s 1',
                fadeInTopLeft: 'fade-in-top-left 1s ease-out 0.25s 1',
                fadeInTopRight: 'fade-in-top-right 1s ease-out 0.25s 1',
                fadeInBottomLeft: 'fade-in-bottom-left 1s ease-out 0.25s 1',
                fadeInBottomRight: 'fade-in-bottom-right 1s ease-out 0.25s 1',
                fadeInLeft: 'fade-in-left 1s ease-in-out 0.25s 1',
                fadeInBounceDown: 'fade-in-bouncedown 1s ease-in-out 0.25s 1',
                fadeInBounceUp: 'fade-in-bounceup 1s ease-in-out 0.25s 1',
                fadeInBounceRight: 'fade-in-bounce-right 1s ease-in-out 0.25s 1',
                fadeInBounceLeft: 'fade-in-bounce-left 1s ease-in-out 0.25s 1',
                fadeInRight: 'fade-in-right 1s ease-in-out 0.25s 1',
                fadeInUp: 'fade-in-up 1s ease-in-out 0.25s 1',
                fadeOutDown: 'fade-out-down 1s ease-in-out 0.25s 1',
                fadeOutTopLeft: 'fade-out-top-left 1s ease-in-out 0.25s 1',
                fadeOutTopRight: 'fade-out-top-right 1s ease-in-out 0.25s 1',
                fadeOutLeft: 'fade-out-left 1s ease-in-out 0.25s 1',
                fadeOutRight: 'fade-out-right 1s ease-in-out 0.25s 1',
                fadeOutUp: 'fade-out-up 1s ease-in-out 0.25s 1',
                zoomin: 'zoom-in 1s ease-in-out 0.25s 1',
                zoomout: 'zoom-out 1s ease-in-out 0.25s 1',
                tada: 'tada 1s ease-in-out 0.25s 1',
                spinnergrow: 'spinner-grow 1s ease-in-out 0.25s 1',
                placeholderwave: 'placeholder-wave 1s ease-in-out 0.25s 1',
                showupclock: 'show-up-clock 1s ease-in-out 0.25s 1',
                dropin: 'drop-in 0.5s ease-in-out 0.25s 1',
                dropout: 'drop-out 0.5s ease-in-out 0.25s 1',
                flash: 'flash 0.6s ease-in-out 0.25s 1',
                shake: 'shake 0.6s ease-in-out 0.25s 1',
                glow: 'glow 0.6s ease-in-out 0.25s 1',
                "animate-step-fade": 'step-fade 0.5s ease-in-out',
                'fade-in': 'fadeIn 0.5s ease-in-out',
                'slide-in': 'slideIn 0.5s ease-in-out',
                'slide-out': 'slideOut 0.5s ease-in-out'
            },
            colors: {
                brand: {
                    1: '#201E5F',
                    2: '#39A0CC',
                    10: '#4464AD',            
                    200: '#04F280',
                    300: '#4464AD',
                    400: '#292459',
                    500: '#81CCDE'
                },
                transparent: 'transparent',
                current: 'currentColor',
                'black': '#000000',
                'white': '#fff',
                'midnight': '#121063',
                'metal': '#565584',
                'tahiti': '#3ab7bf',
                'silver': '#ecebff',
                'bubble-gum': '#ff77e9',
                'bermuda': '#78dcca',
            },
            typography: theme => ({
                default: {
                    css: {
                        pre: {
                            color: theme("colors.grey.1000"),
                            backgroundColor: theme("colors.grey.100")
                        },
                        "pre code::before": {
                            "padding-left": "unset"
                        },
                        "pre code::after": {
                            "padding-right": "unset"
                        },
                        code: {
                            backgroundColor: theme("colors.grey.100"),
                            color: "#DD1144",
                            fontWeight: "400",
                            "border-radius": "0.25rem"
                        },
                        "code::before": {
                            content: '""',
                            "padding-left": "0.25rem"
                        },
                        "code::after": {
                            content: '""',
                            "padding-right": "0.25rem"
                        }
                    }
                }
            })
        },
    },
    plugins: [
        require("@tailwindcss/forms"),
        require("@tailwindcss/typography"),
        plugin(({addVariant}) => addVariant("phx-no-feedback", [".phx-no-feedback&", ".phx-no-feedback &"])),
        plugin(({addVariant}) => addVariant("phx-click-loading", [".phx-click-loading&", ".phx-click-loading &"])),
        plugin(({addVariant}) => addVariant("phx-submit-loading", [".phx-submit-loading&", ".phx-submit-loading &"])),
        plugin(({addVariant}) => addVariant("phx-change-loading", [".phx-change-loading&", ".phx-change-loading &"])),
        plugin(({addVariant}) => addVariant("phx-page-loading", [".phx-page-loading&", ".phx-page-loading &"])),
        plugin(({addVariant}) => addVariant("drag-item", [".drag-item&", ".drag-item &"])),
        plugin(({addVariant}) => addVariant("drag-ghost", [".drag-ghost&", ".drag-ghost &"])),

        // Embeds Heroicons (https://heroicons.com) into your app.css bundle
        // See your `CoreComponents.icon/1` for more information.
        //
        plugin(function ({matchComponents, theme}) {
            let iconsDir = path.join(__dirname, "../deps/heroicons/optimized")
            let values = {}
            let icons = [
                ["", "/24/outline"],
                ["-solid", "/24/solid"],
                ["-mini", "/20/solid"],
                ["-micro", "/16/solid"]
            ]
            icons.forEach(([suffix, dir]) => {
                fs.readdirSync(path.join(iconsDir, dir)).forEach(file => {
                    let name = path.basename(file, ".svg") + suffix
                    values[name] = {name, fullPath: path.join(iconsDir, dir, file)}
                })
            })
            matchComponents({
                "hero": ({name, fullPath}) => {
                    let content = fs.readFileSync(fullPath).toString().replace(/\r?\n|\r/g, "")
                    let size = theme("spacing.6")
                    if (name.endsWith("-mini")) {
                        size = theme("spacing.5")
                    } else if (name.endsWith("-micro")) {
                        size = theme("spacing.4")
                    }
                    return {
                        [`--hero-${name}`]: `url('data:image/svg+xml;utf8,${content}')`,
                        "-webkit-mask": `var(--hero-${name})`,
                        "mask": `var(--hero-${name})`,
                        "mask-repeat": "no-repeat",
                        "background-color": "currentColor",
                        "vertical-align": "middle",
                        "display": "inline-block",
                        "width": size,
                        "height": size
                    }
                }
            }, {values})
        }),
        function ({addUtilities}) {
            const sweetAlertColors = {
                '[x-cloak]': {
                    display: 'none',
                },
                '.colored-toast.swal2-icon-success': {
                    backgroundColor: '#a5dc86 !important',
                },
                '.colored-toast.swal2-icon-error': {
                    backgroundColor: '#f27474 !important',
                },
                '.colored-toast.swal2-icon-warning': {
                    backgroundColor: '#f8bb86 !important',
                },
                '.colored-toast.swal2-icon-info': {
                    backgroundColor: '#3fc3ee !important',
                },
                '.colored-toast.swal2-icon-question': {
                    backgroundColor: '#87adbd !important',
                },
                '.colored-toast .swal2-title': {
                    color: 'white',
                },
                '.colored-toast .swal2-close': {
                    color: 'white',
                },
                '.colored-toast .swal2-html-container': {
                    color: 'white',
                },
            };
            addUtilities(sweetAlertColors, []);
        },
    ]
}