<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  <.header>
    <%= @page_title %>
    <:subtitle>
      Select permissions that will be available for access roles under this department
    </:subtitle>
    <:actions>
      <.link navigate={~p"/roles/system_roles"} class="text-sm text-gray-600 hover:text-gray-900">
        ← Back to Department Roles
      </.link>
    </:actions>
  </.header>

  <div class="mt-8">
    <!-- Search Bar -->
    <div class="mb-6">
      <div class="relative">
        <input
          type="text"
          phx-keyup="search"
          phx-debounce="300"
          value={@search_query}
          placeholder="Search permissions..."
          class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <svg
          class="absolute right-3 top-3 h-5 w-5 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>
    </div>
    <!-- Permissions List -->
    <div class="space-y-6">
      <div class="grid lg:grid-cols-4 md:grid-cols-3 sm:grid-cols-2 xs:grid-cols-1 gap-4">
        <%= for {service, permissions} <- filter_permissions(@all_permissions, @search_query) do %>
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="bg-gray-50 px-6 py-3 border-b border-gray-200">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">
                  <%= service %>
                </h3>
                <button
                  type="button"
                  phx-click="toggle_service"
                  phx-value-service={service}
                  class="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  <%= if all_selected?(permissions, @selected_permissions) do %>
                    Deselect All
                  <% else %>
                    Select All
                  <% end %>
                </button>
              </div>
            </div>

            <div class="divide-y divide-gray-200">
              <%= for permission <- permissions do %>
                <div class="px-6 py-4 hover:bg-gray-50 transition-colors duration-150">
                  <label class="flex items-start cursor-pointer">
                    <input
                      type="checkbox"
                      phx-click="toggle_permission"
                      phx-value-permission_id={permission.id}
                      checked={MapSet.member?(@selected_permissions, permission.id)}
                      class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <div class="ml-3 flex-1">
                      <div class="text-sm font-medium text-gray-900">
                        <%= permission.description %>
                      </div>
                      <%= if permission.tab do %>
                        <div class="text-sm text-gray-500 mt-1">
                          Tab: <%= permission.tab %>
                        </div>
                      <% end %>
                      <%= if permission.code do %>
                        <div class="text-xs text-gray-400 mt-1 font-mono">
                          <%= permission.code %>
                        </div>
                      <% end %>
                    </div>
                  </label>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
    <!-- Save Button -->
    <div class="mt-8 flex justify-end space-x-4">
      <.link navigate={~p"/roles/system_roles"}>
        <.button variant="secondary">Cancel</.button>
      </.link>
      <.button phx-click="save">
        Save Permissions
      </.button>
    </div>
  </div>
</div>
