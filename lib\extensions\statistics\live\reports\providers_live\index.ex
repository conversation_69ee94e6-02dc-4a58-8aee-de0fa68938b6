defmodule AppWeb.Reports.ProviderReportsLive.Index do
  @moduledoc false
  use AppWeb, :live_view

  alias App.Service.Table.ProviderReports, as: TableQuery

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("reports_view-provider", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Reports", "Accessed Service Provider Reports Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      filter_data = %{
        "year" => nil,
        "month" => nil
      }

      months = [
        {"January", "01"},
        {"February", "02"},
        {"March", "03"},
        {"April", "04"},
        {"May", "05"},
        {"June", "06"},
        {"July", "07"},
        {"August", "08"},
        {"September", "09"},
        {"October", "10"},
        {"November", "11"},
        {"December", "12"}
      ]

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> assign(params: params)
        |> assign(years: 2018..DateTime.utc_now().year)
        |> assign(months: months)
        |> assign(live_socket_id: session["live_socket_id"])
        |> assign(enable_country_form: "hide")
        |> assign(disable_country_form: "hide")
        |> assign(showFilter: false)
        |> assign(form: useform(filter_data))
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Reports", "Accessed Service Provider Reports Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  defp useform(data) do
    to_form(
      data,
      as: "filter"
    )
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_list, params})

    {:noreply,
     socket
     |> assign(:params, params)
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "Service Provider Reports")
    |> assign(:record, nil)
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {:search_records, params} ->
        send(self(), {:get_list, %{"filter" => params}})
        noreply(socket)

      {AppWeb.Component.DateFilter, {:filtered, data}} ->
        send(self(), {:get_list, %{"filter" => data}})

        {
          :noreply,
          socket
          |> assign(:live_action, :index)
          |> assign(:page_title, "Listing Provider Reports")
        }

      {AppWeb.LiveFunctions, message} ->
        send(self(), {:get_list, %{}})

        {
          :noreply,
          socket
          |> put_flash(:info, message)
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, %{assigns: _assigns} = socket) do
    case target do
      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      "refresh_table" ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :data_loader, true)
        |> noreply()

      "filter_change" ->
        assign(socket, form: useform(value["filter"]))
        |> noreply()

      "filter" ->
        value = if socket.assigns.showFilter == true, do: false, else: true

        assign(socket, :showFilter, value)
        |> noreply()

      "reset_filter" ->
        socket
        |> assign(form: useform(%{}))
        |> noreply()

      "search" ->
        send(self(), {:get_list, value})

        noreply(
          socket
          |> assign(checked_ids: [])
          |> assign(param_list: [])
        )

      "export" ->
        LiveFunctions.export_records(
          socket,
          value,
          "provider_reports",
          socket.assigns.params
        )

      "refresh_table" ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :data_loader, true)
        |> noreply()

      _ ->
        {:noreply, socket}
    end
  end

  defp list(%{assigns: _assigns} = socket, params) do
    data = TableQuery.index(socket, LivePageControl.create_table_params(socket, params))

    {
      :noreply,
      assign(socket, :data, data)
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end
end
