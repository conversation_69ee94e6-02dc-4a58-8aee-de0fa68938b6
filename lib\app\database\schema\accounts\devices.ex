defmodule App.Accounts.Devices do
  use Ecto.Schema
  import Ecto.Changeset

  alias App.{Accounts.User}

  schema "devices" do
    field(:device_id, :string)
    field(:name, :string)
    field(:platform, :string)
    field(:status, :integer)
    field(:operating_system, :string)
    field(:last_activity, :naive_datetime)

    belongs_to(:user, User)

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(devices, attrs) do
    devices
    |> cast(attrs, [
      :device_id,
      :name,
      :platform,
      :operating_system,
      :last_activity,
      :user_id,
      :status
    ])
    |> validate_required([:device_id, :name, :platform, :operating_system, :last_activity])
    |> unsafe_validate_unique([:device_id, :user_id], App.Repo)
    |> unique_constraint([:device_id, :user_id])
  end
end
