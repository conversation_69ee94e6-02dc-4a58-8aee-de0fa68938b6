defmodule App.Service.Export.CsvAnnualStatisticalService do
  @moduledoc false
  alias App.Service.Table.YearlyReports
  alias App.Service.Export.Functions

  @headers [
    "MONTH",
    "SENT",
    "DELIVERED",
    "FAILED",
    "INVALID",
    "TOTAL"
  ]

  def index(socket, payload) do
    YearlyReports.export(socket, payload)
    |> Stream.map(
      &[
        &1.month,
        &1.sent,
        &1.delivered,
        &1.failed,
        &1.invalid,
        &1.total
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Yearly Reports"],
              ["", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
