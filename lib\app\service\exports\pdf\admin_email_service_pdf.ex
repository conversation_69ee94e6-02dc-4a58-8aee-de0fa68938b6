defmodule App.Service.Export.AdminEmailServicePdf do
  @moduledoc false

  alias App.Service.Table.Maintenance.ServiceAdminEmail

  alias App.Service.Export.{
    Functions
  }

  def index(payload) do
    results =
      ServiceAdminEmail.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "inserted_at" => Calendar.strftime(data.inserted_at, "%d/%m/%Y"),
          "name" => data.name,
          "email" => data.email,
          "status" => Functions.table_numeric_status(data.status)
        }
      end)
      |> Enum.map(fn data ->
        """
         <tr>
            <td style="text-align: center;">{{ inserted_at }}</td>
            <td style="text-align: center;">{{ name }}</td>
            <td style="text-align: center;">{{ email }}</td>
            <td style="text-align: center;">{{ status }}</td>

         </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/admin_email_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Admin Email",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end
