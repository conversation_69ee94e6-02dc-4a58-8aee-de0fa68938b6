<div class="flex flex-col items-center mt-8">
  <div id="printsssss" class="w-full lg:w-10/12" phx-hook="PrintContent">
    <div class="bg-white shadow-md rounded-lg">
      <%= if @data_loader == false do %>
        <div class="bg-gray-100 p-4 rounded-t-lg flex justify-between items-center">
          <h6 class="text-sm font-medium">
            Invoice for <%= Timex.format!(@transaction.txn_date, "{D} {Mfull}, {YYYY}") %>
          </h6>
          <div class="flex space-x-2">
            <.button
              @click="printDiv.printDiv('toBePrinted');"
              class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-semibold disabled:opacity-50 disabled:pointer-events-none"
            >
              <svg
                class="flex-shrink-0 w-4 h-4"
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <polyline points="6 9 6 2 18 2 18 9" />
                <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2" />
                <rect width="12" height="8" x="6" y="14" />
              </svg>
              Print
            </.button>
          </div>
        </div>
        <div id="toBePrinted">
          <div class="p-4 mt-4">
            <div class="grid grid-cols-3">
              <div class="w-full sm:w-1/2 mb-4 grid col-span-2">
                <div class="text-left h-full mb-5">
                  <a href="#" class="navbar-link" target="_blank">
                    <img src="/images/probase.png" width="95px" height="50px" alt="probase_logo" />
                  </a>
                </div>
                <ul class="list-none text-sm">
                  <li>Plot 2374</li>
                  <li>Kevin Siwale Road, Lusaka</li>
                  <li><EMAIL></li>
                </ul>
              </div>
              <div class="grid col-span-1 text-right">
                <div class="w-full mb-4">
                  <h5 class="font-semibold text-lg">Billing Details</h5>
                  <ul class="list-none text-sm">
                    <li>
                      Invoice Date:
                      <span class="font-semibold">
                        <%= Calendar.strftime(@transaction.txn_date, "%d %B %Y") %>
                      </span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="flex flex-wrap">
              <div class="w-full lg:w-3/4 mb-4"></div>
              <div class="w-full lg:w-1/4 mb-4">
                <span class="text-gray-500">Client Details:</span>
                <ul class="list-none text-sm">
                  <li class="flex justify-between">
                    Account ID:
                    <span>
                      <%= @client.client_id %>
                    </span>
                  </li>
                  <li class="flex justify-between">
                    Client name:
                    <span class="font-semibold">
                      <%= @client.client_name %>
                    </span>
                  </li>

                  <li class="flex justify-between">
                    Account type:
                    <span>
                      <%= @client.payment_type %>
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="overflow-x-auto p-4">
            <table class="min-w-full bg-white text-sm text-left">
              <thead>
                <tr>
                  <th class="py-2 pr-56">Description</th>
                  <th class="py-2">Transaction Date</th>
                  <th class="py-2">Rate</th>
                  <th class="py-2">VAT</th>
                  <th class="py-2">Subtotal</th>
                  <th class="py-2">Bundle</th>
                  <th class="py-2">Total</th>
                </tr>
                <tr>
                  <th colspan="7"><hr class="border-t-2 border-gray-400" /></th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="py-2 pr-8"><%= @transaction.desc %></td>
                  <td class="py-2"><%= Timex.to_date(Timex.now()) %></td>
                  <td class="py-2"><%= NumberF.currency(@transaction.unit_price, "ZMW ") %></td>
                  <td class="py-2"><%= NumberF.currency(@transaction.total_vat, "ZMW ") %></td>
                  <td class="py-2">
                    <%= NumberF.currency(@transaction.amount_aft_vat, "ZMW ") %>
                  </td>
                  <td class="py-2">
                    <%= NumberF.comma_separated(@transaction.total_bundle_load, 0) %>
                  </td>
                  <td class="py-2 font-semibold">
                    <%= NumberF.currency(@transaction.txb_amount, "ZMW ") %>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="p-4">
            <div class="flex flex-wrap">
              <div class="w-full sm:w-7/12 mb-4"></div>
              <div class="w-full sm:w-5/12 mb-4">
                <div class="content-group">
                  <div class="text-right mt-4 space-x-4 no-print">
                    <%!-- <.link navigate="/payment/client">
                        <.button type="button">
                          <b><i class="hero-chevron-double-left position-left"></i></b> back
                        </.button>
                        </.link> --%>
                  </div>
                </div>
              </div>
            </div>
            <p class="text-gray-500">
              Thank you for using our services. Please note that this invoice is only for <%= Timex.format!(
                @transaction.txn_date,
                "{D} {Mfull}, {YYYY}"
              ) %>
            </p>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
