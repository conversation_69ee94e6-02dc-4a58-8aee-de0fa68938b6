defmodule App.Service.Export.Functions do
  @moduledoc false
  alias App.Util.DateCounter
  alias AppWeb.Endpoint

  def empty_check?(data), do: if(data == "", do: nil, else: data)

  def number_function(number) when is_nil(number), do: 0

  def number_function(%Decimal{} = number) do
    Decimal.to_float(number)
    |> number_function()
  end

  def number_function(number) when is_integer(number) or is_binary(number) do
    Decimal.new(number)
    |> Decimal.to_float()
    |> number_function()
  end

  def number_function(number) when is_float(number), do: erlang_float_function(number)

  def erlang_float_function(number) do
    try do
      :erlang.float_to_binary(number, decimals: 2)
    rescue
      _ -> :erlang.float_to_binary(String.to_float(number), decimals: 2)
    end
  end

  def department_roles_management_status(status) do
    case status do
      0 -> "Disabled"
      1 -> "Active"
      _ -> "Unknown"
    end
  end

  def table_numeric_status(status) do
    case status do
      "D" -> "Disabled"
      "A" -> "Active"
      # 3 -> "Inactive"
      # 4 -> "Pending Approval"
      _ -> "Unknown"
    end
  end

  def self_registration_status(status) do
    case status do
      0 -> "Pending approval"
      1 -> "Successfully approved"
      2 -> "Declined"
      _ -> "Unknown"
    end
  end

  def kyc_validation_status(status) do
    case status do
      1 -> "Pending Validation"
      2 -> "Successfully Validated"
      3 -> "Validation Failed"
      _ -> "Unknown"
    end
  end

  def table_boolean_status(status) do
    case status do
      false -> "Disabled"
      true -> "Active"
      _ -> "Unknown"
    end
  end

  def table_card_status(status) do
    case status do
      0 -> "Generated by App"
      1 -> "Assigned to Distributor"
      2 -> "Assigned to End User"
      3 -> "Inactive"
      4 -> "Disabled"
      5 -> "Deleted"
      _ -> "Unknown"
    end
  end

  def table_card_scheme(override_transaction_fee) do
    case override_transaction_fee do
      0 -> "No"
      1 -> "Yes"
      _ -> "Unknown"
    end
  end

  def run_export(
        socket,
        payload,
        message,
        title,
        ext,
        function \\ & &1,
        date \\ App.Util.CustomTime.local_datetime()
      ) do
    try do
      notify(payload, message)

      result =
        case function do
          f when is_function(f, 2) -> f.(socket, payload)
          f when is_function(f, 1) -> f.(payload)
        end

      export_done(result, title, date, ext)
    rescue
      error ->
        IO.inspect(error)
        notify(payload, "#{message} has failed", "error")
        nil
    else
      %{payload: _, fileName: _, extension: _} = result ->
        notify(payload, "#{message} has Completed", "success")
        result

      _ ->
        nil
    end
  end

  def export_done(file_content, title, date, extension) do
    %{
      payload: "#{Base.encode64(file_content)}",
      fileName:
        "#{title} Report #{date.day} #{DateCounter.get_month_words_plus_year(date.month, date.year)}",
      extension: extension
    }
  end

  def notify(payload, message, icon \\ "info") do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: message,
        expression: "toast",
        timer: 8000,
        icon: icon
      }
    )
  end

  def table_station_status(status) do
    case status do
      1 -> "Card Collected"
      0 -> "Card Not Collected"
      false -> "Disabled"
      true -> "Active"
      _ -> "Unknown"
    end
  end
end
