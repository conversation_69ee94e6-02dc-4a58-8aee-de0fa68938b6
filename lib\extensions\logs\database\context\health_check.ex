defmodule Logs.Health do
  @moduledoc """
  The Health context.
  """

  import Ecto.Query, warn: false
  alias Logs.Health.HealthCheck
  alias Logs.Health.HealthCheckLog
  alias Logs.LogRepo
  alias App.CustomContext

  def get_active_health_check() do
    HealthCheck
    |> where([a], a.status == ^1)
    |> LogRepo.all()
  end

  def get_health_by_endpoint_and_name(endpoint, name) do
    HealthCheck
    |> where([a], a.endpoint == ^endpoint and a.name == ^name)
    |> limit(1)
    |> LogRepo.one()
  end

  def update_health_check(health_check, attrs) do
    health_check
    |> HealthCheck.update_changeset(attrs)
    |> LogRepo.update()
    |> CustomContext.notify_subs("ping_endpoint", "ping")
  end

  def create_health_check(attrs) do
    %HealthCheck{}
    |> HealthCheck.changeset(attrs)
    |> LogRepo.insert!()
  end

  def create_health_check_logs(attrs) do
    %HealthCheckLog{}
    |> HealthCheckLog.changeset(attrs)
    |> LogRepo.insert!()
  end
end
