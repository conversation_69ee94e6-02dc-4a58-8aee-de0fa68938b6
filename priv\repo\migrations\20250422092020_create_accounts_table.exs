defmodule App.Repo.Migrations.CreateAccountsTable do
  use Ecto.Migration

  def change do
    create table(:accounts) do
      add :license_amount, :decimal
      add :other_fees, :decimal
      add :amount_paid, :decimal, default: 0
      add :balance, :decimal, default: 0
      add :user_id, references(:tbl_users, on_delete: :nothing), null: false
      add :application_id, references(:user_license_mapping, on_delete: :nothing)
      add :verified, :boolean, default: false
      add :declined, :boolean, default: true

      timestamps()
    end

    alter table(:user_license_mapping) do
      add :account_id, references(:accounts, on_delete: :nothing)
    end
  end
end
