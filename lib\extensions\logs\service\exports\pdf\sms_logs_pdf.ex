defmodule App.Service.Export.SmsLogsServicePdf do
  @moduledoc false

  alias App.Service.Logs.LogsSms
  alias App.Service.Export.Functions

  def index(payload) do
    results =
      LogsSms.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "inserted_at" =>
            Calendar.strftime(
              NaiveDateTime.add(data.inserted_at, 7200, :second),
              "%d %B %Y %H:%M:%S"
            ),
          "date_sent" => Calendar.strftime(data.date_sent, "%d/%m/%Y"),
          "recipient" => data.recipient,
          "message_id" => data.message_id,
          "msg_count" => NumberF.comma_separated(data.msg_count, 0),
          "status" => data.status,
          "massage" => data.message
        }
      end)
      |> Enum.map(fn data ->
        """
         <tr>
            <td>{{ inserted_at }}</td>
            <td>{{ date_sent }}</td>
            <td>{{ recipient }}</td>
            <td>{{ message_id }}</td>
            <td style="text-align: right;">{{ msg_count }}</td>
            <td style="text-align: center;">{{ status }}</td>
            <td>{{ massage }}</td>
         </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/logs/sms_logs_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Sms Logs",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end
