defmodule App.Registration.ApplicationSteps do
  use Ecto.Schema
  import Ecto.Changeset

  schema "license_steps_mapping" do
    field :number, :integer, default: 1
    field :status, :integer, default: 0
    belongs_to :license, App.Licenses.License
    belongs_to :step, App.Registration.Pages

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(license_step, attrs) do
    license_step
    |> cast(attrs, [:license_id, :step_id, :status, :number])
    |> validate_required([:license_id, :step_id, :number])
    |> unsafe_validate_unique([:license_id, :step_id, :number], App.Repo)
    |> unique_constraint([:license_id, :step_id, :number])
  end
end
