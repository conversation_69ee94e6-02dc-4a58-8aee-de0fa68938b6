defmodule App.MerchantTest do
  use App.DataCase

  alias App.Merchant

  describe "merchant_to_product_provider" do
    alias App.Merchant.MerchantToProductProvider

    import App.MerchantFixtures

    @invalid_attrs %{status: nil, provider: nil}

    test "list_merchant_to_product_provider/0 returns all merchant_to_product_provider" do
      merchant_to_product_provider = merchant_to_product_provider_fixture()
      assert Merchant.list_merchant_to_product_provider() == [merchant_to_product_provider]
    end

    test "get_merchant_to_product_provider!/1 returns the merchant_to_product_provider with given id" do
      merchant_to_product_provider = merchant_to_product_provider_fixture()

      assert Merchant.get_merchant_to_product_provider!(merchant_to_product_provider.id) ==
               merchant_to_product_provider
    end

    test "create_merchant_to_product_provider/1 with valid data creates a merchant_to_product_provider" do
      valid_attrs = %{status: 42, provider: "some provider"}

      assert {:ok, %MerchantToProductProvider{} = merchant_to_product_provider} =
               Merchant.create_merchant_to_product_provider(valid_attrs)

      assert merchant_to_product_provider.status == 42
      assert merchant_to_product_provider.provider == "some provider"
    end

    test "create_merchant_to_product_provider/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} =
               Merchant.create_merchant_to_product_provider(@invalid_attrs)
    end

    test "update_merchant_to_product_provider/2 with valid data updates the merchant_to_product_provider" do
      merchant_to_product_provider = merchant_to_product_provider_fixture()
      update_attrs = %{status: 43, provider: "some updated provider"}

      assert {:ok, %MerchantToProductProvider{} = merchant_to_product_provider} =
               Merchant.update_merchant_to_product_provider(
                 merchant_to_product_provider,
                 update_attrs
               )

      assert merchant_to_product_provider.status == 43
      assert merchant_to_product_provider.provider == "some updated provider"
    end

    test "update_merchant_to_product_provider/2 with invalid data returns error changeset" do
      merchant_to_product_provider = merchant_to_product_provider_fixture()

      assert {:error, %Ecto.Changeset{}} =
               Merchant.update_merchant_to_product_provider(
                 merchant_to_product_provider,
                 @invalid_attrs
               )

      assert merchant_to_product_provider ==
               Merchant.get_merchant_to_product_provider!(merchant_to_product_provider.id)
    end

    test "delete_merchant_to_product_provider/1 deletes the merchant_to_product_provider" do
      merchant_to_product_provider = merchant_to_product_provider_fixture()

      assert {:ok, %MerchantToProductProvider{}} =
               Merchant.delete_merchant_to_product_provider(merchant_to_product_provider)

      assert_raise Ecto.NoResultsError, fn ->
        Merchant.get_merchant_to_product_provider!(merchant_to_product_provider.id)
      end
    end

    test "change_merchant_to_product_provider/1 returns a merchant_to_product_provider changeset" do
      merchant_to_product_provider = merchant_to_product_provider_fixture()

      assert %Ecto.Changeset{} =
               Merchant.change_merchant_to_product_provider(merchant_to_product_provider)
    end
  end
end
