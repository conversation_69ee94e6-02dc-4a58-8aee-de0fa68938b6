defmodule App.Service.Export.PdfSmsLogsService do
  @moduledoc false

  alias Notification.Service.Logs.LogsSms

  alias App.Service.Export.Functions

  def index(assigns, payload) do
    results =
      LogsSms.export(assigns, payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "mobile" => data.mobile,
          "source" => data.source,
          "sender_id" => data.sender_id,
          "client" => data.client,
          "client" => data.client,
          "courier" => data.courier,
          "status" => data.status,
          "count" => data.count,
          "updated_at" => Calendar.strftime(data.updated_at, "%d/%m/%Y")
        }
      end)
      |> Enum.map(fn data ->
        """
        <tr>
            <td style="text-align: center;">{{ mobile }}</td>
            <td style="text-align: center;">{{ source }}</td>
            <td style="text-align: center;">{{ sender_id }}</td>
            <td style="text-align: center;">{{ client }}</td>
            <td style="text-align: center;">{{ client }}</td>
            <td style="text-align: center;">{{ courier }}</td>
            <td style="text-align: center;">{{ status }}</td>
            <td style="text-align: center;">{{ count }}</td>
            <td style="text-align: center;">{{ updated_at }}</td>
        </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/sms_log_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Sms Logs (today's trafic only)",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end
