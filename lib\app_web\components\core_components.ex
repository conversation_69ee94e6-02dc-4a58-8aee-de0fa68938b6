defmodule AppWeb.CoreComponents do
  @moduledoc """
  Provides core UI components.

  At first glance, this module may seem daunting, but its goal is to provide
  core building blocks for your application, such as modals, tables, and
  forms. The components consist mostly of markup and are well-documented
  with doc strings and declarative assigns. You may customize and style
  them in any way you want, based on your application growth and needs.

  The default components use Tailwind CSS, a utility-first CSS framework.
  See the [Tailwind CSS documentation](https://tailwindcss.com) to learn
  how to customize them or feel free to swap in another framework altogether.

  Icons are provided by [heroicons](https://heroicons.com). See `icon/1` for usage.
  """

  use Phoenix.Component
  use AppWeb, :verified_routes

  alias Phoenix.LiveView.JS

  alias AppWeb.Component
  import AppWeb.Gettext
  import AppWeb.Helpers.IconHelper

  @doc """
  Renders a modal.

  ## Examples

      <.modal id="confirm-modal">
        This is a modal.
      </.modal>

  JS commands may be passed to the `:on_cancel` to configure
  the closing/cancel event, for example:

      <.modal id="confirm" on_cancel={JS.navigate(~p"/posts")}>
        This is another modal.
      </.modal>

  """
  attr(:id, :string, required: true)
  attr(:show, :boolean, default: false)
  attr(:on_cancel, JS, default: %JS{})
  slot(:inner_block, required: true)

  def modal(assigns) do
    ~H"""
    <div
      id={@id}
      phx-mounted={@show && show_modal(@id)}
      phx-remove={hide_modal(@id)}
      data-cancel={JS.exec(@on_cancel, "phx-remove")}
      class="relative z-50 hidden"
    >
      <div id={"#{@id}-bg"} class="bg-zinc-50/90 fixed inset-0 transition-opacity" aria-hidden="true" />
      <div
        class="fixed inset-0 overflow-y-auto"
        aria-labelledby={"#{@id}-title"}
        aria-describedby={"#{@id}-description"}
        role="dialog"
        aria-modal="true"
        tabindex="0"
      >
        <div class="flex min-h-full items-center justify-center">
          <div class="w-full max-w-3xl p-4 sm:p-6 lg:py-8">
            <.focus_wrap
              id={"#{@id}-container"}
              phx-window-keydown={JS.exec("data-cancel", to: "##{@id}")}
              phx-key="escape"
              phx-click-away={JS.exec("data-cancel", to: "##{@id}")}
              class="shadow-zinc-700/10 ring-zinc-700/10 relative hidden rounded-2xl bg-white p-14 shadow-lg ring-1 transition"
            >
              <div class="absolute top-6 right-5">
                <button
                  phx-click={JS.exec("data-cancel", to: "##{@id}")}
                  type="button"
                  class="-m-3 flex-none p-3 opacity-20 hover:opacity-40"
                  aria-label={gettext("close")}
                >
                  <.icon name="hero-x-mark-solid" class="h-5 w-5" />
                </button>
              </div>

              <div id={"#{@id}-content"}>
                <%= render_slot(@inner_block) %>
              </div>
            </.focus_wrap>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  Renders flash notices.

  ## Examples

      <.flash kind={:info} flash={@flash} />
      <.flash kind={:info} phx-mounted={show("#flash")}>Welcome Back!</.flash>
  """
  attr(:id, :string, doc: "the optional id of flash container")
  attr(:flash, :map, default: %{}, doc: "the map of flash messages to display")
  attr(:title, :string, default: nil)
  attr(:kind, :atom, values: [:info, :error], doc: "used for styling and flash lookup")
  attr(:rest, :global, doc: "the arbitrary HTML attributes to add to the flash container")

  slot(:inner_block, doc: "the optional inner block that renders the flash message")

  def flash(assigns) do
    assigns = assign_new(assigns, :id, fn -> "flash-#{assigns.kind}" end)

    ~H"""
    <div
      :if={msg = render_slot(@inner_block) || Phoenix.Flash.get(@flash, @kind)}
      id={@id}
      phx-click={JS.push("lv:clear-flash", value: %{key: @kind}) |> hide("##{@id}")}
      role="alert"
      class={[
        "fixed top-2 mt-16 right-2 mr-2 w-80 sm:w-96 z-50 rounded-lg p-3 ring-1",
        @kind == :info && "bg-emerald-50 text-emerald-800 ring-emerald-500 fill-cyan-900",
        @kind == :error && "bg-rose-50 text-rose-900 shadow-md ring-rose-500 fill-rose-900"
      ]}
      {@rest}
    >
      <p :if={@title} class="flex items-center gap-1.5 text-sm font-semibold leading-6">
        <.icon :if={@kind == :info} name="hero-information-circle-mini" class="h-4 w-4" />
        <.icon :if={@kind == :error} name="hero-exclamation-circle-mini" class="h-4 w-4" /> <%= @title %>
      </p>

      <p class="mt-2 text-sm leading-5"><%= msg %></p>

      <button type="button" class="group absolute top-1 right-1 p-2" aria-label={gettext("close")}>
        <.icon name="hero-x-mark-solid" class="h-5 w-5 opacity-40 group-hover:opacity-70" />
      </button>
    </div>
    """
  end

  @doc """
  Shows the flash group with standard titles and content.

  ## Examples

      <.flash_group flash={@flash} />
  """
  attr(:flash, :map, required: true, doc: "the map of flash messages")
  attr(:id, :string, default: "flash-group", doc: "the optional id of flash container")

  def flash_group(assigns) do
    ~H"""
    <div id={@id}>
      <.flash kind={:info} title={gettext("Success!")} flash={@flash} />
      <.flash kind={:error} title={gettext("Error!")} flash={@flash} />
      <.flash
        id="client-error"
        kind={:error}
        title={gettext("We can't find the internet")}
        phx-disconnected={show(".phx-client-error #client-error")}
        phx-connected={hide("#client-error")}
        hidden
      >
        <%= gettext("Attempting to reconnect") %>
        <.icon name="hero-arrow-path" class="ml-1 h-3 w-3 animate-spin" />
      </.flash>

      <.flash
        id="server-error"
        kind={:error}
        title={gettext("Something went wrong!")}
        phx-disconnected={show(".phx-server-error #server-error")}
        phx-connected={hide("#server-error")}
        hidden
      >
        <%= gettext("Hang in there while we get back on track") %>
        <.icon name="hero-arrow-path" class="ml-1 h-3 w-3 animate-spin" />
      </.flash>
    </div>
    """
  end

  @doc """
  Renders a simple form.

  ## Examples

      <.simple_form for={@form} phx-change="validate" phx-submit="save">
        <.input field={@form[:email]} label="Email"/>
        <.input field={@form[:username]} label="Username" />
        <:actions>
          <.button>Save</.button>
        </:actions>
      </.simple_form>
  """
  attr(:for, :any, required: true, doc: "the datastructure for the form")
  attr(:as, :any, default: nil, doc: "the server side parameter to collect all input under")

  attr(:rest, :global,
    include: ~w(autocomplete name rel action enctype method novalidate target multipart),
    doc: "the arbitrary HTML attributes to apply to the form tag"
  )

  slot(:inner_block, required: true)
  slot(:actions, doc: "the slot for form actions, such as a submit button")

  def simple_form(assigns) do
    ~H"""
    <.form :let={f} for={@for} as={@as} {@rest}>
      <div class="space-y-8">
        <%= render_slot(@inner_block, f) %>
        <div :for={action <- @actions} class="mt-2 flex items-center justify-between gap-6">
          <%= render_slot(action, f) %>
        </div>
      </div>
    </.form>
    """
  end

  @doc """
  Renders a button.

  ## Examples

      <.button>Send!</.button>
      <.button phx-click="go" class="ml-2">Send!</.button>
  """
  attr(:type, :string, default: nil)
  attr(:class, :string, default: nil)
  attr(:rest, :global, include: ~w(disabled form name value))
  slot(:inner_block, required: true)

  def button(assigns) do
    ~H"""
    <button
      type={@type}
      class={[
        "phx-submit-loading:opacity-75 transition ease-in-out delay-150 duration-300 rounded hover:border-brand-10 bg-brand-10 hover:bg-transparent border-2 hover:text-brand-10 py-2 px-3",
        "text-sm font-semibold leading-6 text-white active:text-white/80",
        @class
      ]}
      {@rest}
    >
      <%= render_slot(@inner_block) %>
    </button>
    """
  end

  attr(:name, :string, default: "shield")
  attr(:class, :string, default: "h-5 w-5")

  def licence_icon(assigns) do
    ~H"""
    <%= case @name do %>
      <% "shield" -> %>
        <svg xmlns="http://www.w3.org/2000/svg" class={@class} viewBox="0 0 20 20" fill="currentColor">
          <path
            fill-rule="evenodd"
            d="M10 1.944A11.954 11.954 0 012.166 5C2.056 5.649 2 6.319 2 7c0 5.225 3.34 9.67 8 11.317C14.66 16.67 18 12.225 18 7c0-.682-.057-1.35-.166-2.001A11.954 11.954 0 0110 1.944zM11 14a1 1 0 11-2 0 1 1 0 012 0z"
            clip-rule="evenodd"
          />
        </svg>
      <% "chart-bar" -> %>
        <svg xmlns="http://www.w3.org/2000/svg" class={@class} viewBox="0 0 20 20" fill="currentColor">
          <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
        </svg>
      <% "user" -> %>
        <svg xmlns="http://www.w3.org/2000/svg" class={@class} viewBox="0 0 20 20" fill="currentColor">
          <path
            fill-rule="evenodd"
            d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
            clip-rule="evenodd"
          />
        </svg>
      <% "briefcase" -> %>
        <svg xmlns="http://www.w3.org/2000/svg" class={@class} viewBox="0 0 20 20" fill="currentColor">
          <path
            fill-rule="evenodd"
            d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
            clip-rule="evenodd"
          />
          <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
        </svg>
      <% "users" -> %>
        <svg xmlns="http://www.w3.org/2000/svg" class={@class} viewBox="0 0 20 20" fill="currentColor">
          <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
        </svg>
      <% "document-text" -> %>
        <svg xmlns="http://www.w3.org/2000/svg" class={@class} viewBox="0 0 20 20" fill="currentColor">
          <path
            fill-rule="evenodd"
            d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
            clip-rule="evenodd"
          />
        </svg>
      <% "location-marker" -> %>
        <svg xmlns="http://www.w3.org/2000/svg" class={@class} viewBox="0 0 20 20" fill="currentColor">
          <path
            fill-rule="evenodd"
            d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
            clip-rule="evenodd"
          />
        </svg>
      <% "office-building" -> %>
        <svg xmlns="http://www.w3.org/2000/svg" class={@class} viewBox="0 0 20 20" fill="currentColor">
          <path
            fill-rule="evenodd"
            d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 01-1 1h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z"
            clip-rule="evenodd"
          />
        </svg>
      <% "x-circle" -> %>
        <svg xmlns="http://www.w3.org/2000/svg" class={@class} viewBox="0 0 20 20" fill="currentColor">
          <path
            fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
            clip-rule="evenodd"
          />
        </svg>
      <% "refresh" -> %>
        <svg xmlns="http://www.w3.org/2000/svg" class={@class} viewBox="0 0 20 20" fill="currentColor">
          <path
            fill-rule="evenodd"
            d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
            clip-rule="evenodd"
          />
        </svg>
      <% "share" -> %>
        <svg xmlns="http://www.w3.org/2000/svg" class={@class} viewBox="0 0 20 20" fill="currentColor">
          <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
        </svg>
      <% _ -> %>
        <svg xmlns="http://www.w3.org/2000/svg" class={@class} viewBox="0 0 20 20" fill="currentColor">
          <path
            fill-rule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
            clip-rule="evenodd"
          />
        </svg>
    <% end %>
    """
  end

  @doc """
      Menu button, Renders a menu item
  """
  attr(:text, :string, default: "")
  attr(:link, :string, default: nil)
  attr(:method, :string, default: nil)
  attr(:icon, :string, default: nil)
  attr(:active, :boolean, default: false)
  attr(:permission, :boolean, default: true)

  def menu_item(assigns) do
    ~H"""
    <.link
      :if={@permission}
      navigate={@link}
      method={@method}
      class={"group rounded #{if(@active, do: "!text-white")} flex items-center gap-4 p-3 transition-all duration-400 text-sm font-medium leading-6 text-white hover:text-black hover:bg-white/10"}
      style={if(@active, do: "background-color: #49586A", else: "")}
    >
      <.icon :if={@icon} name={@icon} class="text-white group-hover:text-black" />
      <span class="text-white group-hover:text-black"><%= @text %></span>
    </.link>
    """
  end

  @doc """
      Menu button dropdown, Renders a menu item
  """
  attr(:text, :string, default: "")
  attr(:items, :list, default: [])
  attr(:method, :string, default: nil)
  attr(:icon, :string, default: nil)
  attr(:permission, :boolean, default: true)
  attr(:active, :boolean, default: false)

  def dropdown_menu_item(assigns) do
    ~H"""
    <div
      :if={Enum.filter(@items, &(&1.permission == true)) |> Enum.at(0)}
      x-data="{open : false}"
      class="item transition-all duration-300 ease-in-out text-white"
    >
      <div
        x-on:click="open = !open"
        phx-hook="menuDropDown"
        id={to_string(:os.system_time())}
        class={"group #{
            if(Enum.filter(@items, & &1.active == true) |> Enum.at(0), do: "!bg-white/10 border-l-4 border-white !text-white")
            } sub-btn flex flex-nowrap justify-between items-center gap-4 p-3 dropdown outline-gray-200 transition-all duration-400 text-sm font-medium leading-6 text-white hover:text-black rounded hover:bg-white/10"}
      >
        <span class="flex items-center gap-4 text-white group-hover:text-black">
          <.icon name={@icon} class="text-white group-hover:text-black" />
          <span class="text-white group-hover:text-black"><%= @text %></span>
        </span>
        <.icon
          name="hero-chevron-down"
          class="w-4 h-4 dropdown rotate text-white group-hover:text-black"
        />
      </div>

      <div
        x-show="open"
        x-transition
        x-transition.scale.origin.top
        class="sub-menu flex flex-col gap-2 border-l-2 border-white/30 text-white"
        style="background-color: #4464AD;"
      >
        <.link
          :for={item <- @items}
          :if={item.permission}
          navigate={item.link || "#"}
          class={"group #{if(item.active, do: "!text-white")} sub-item w-full text-sm font-normal p-2 flex items-center gap-4 text-white hover:text-black hover:bg-white/10"}
          style={if(item.active, do: "background-color: #49586A", else: "")}
        >
          <.icon_tag
            :if={item["icon"] !== nil}
            name={item.icon}
            class="text-white group-hover:text-black"
          /> <span class="text-white group-hover:text-black"><%= item.text %></span>
        </.link>
      </div>
    </div>
    """
  end

  @doc type: :component

  attr(:field, :any,
    required: true,
    doc: "a Phoenix.HTML.FormField struct identifying the form's field"
  )

  attr(:id, :string,
    doc:
      ~S(an id to assign to the component. If none is provided, `#{form_name}_#{field}_live_select_component` will be used)
  )

  attr(:mode, :atom,
    values: [:single, :tags, :quick_tags],
    default: Component.default_opts()[:mode],
    doc:
      "either `:single` (for single selection), `:tags` (for multiple selection using tags), or `:quick_tags` (multiple selection but tags can be selected/deselected in quick succession)"
  )

  attr(:options, :list,
    doc:
      ~s(initial available options to select from. Note that, after the initial rendering of the component, options can only be updated using `Phoenix.LiveView.send_update/3` - See the "Options" section for details)
  )

  attr(:value, :any, doc: "used to manually set a selection - overrides any values from the form.
  Must be a single element in `:single` mode, or a list of elements in `:tags` mode.")

  attr(:max_selectable, :integer,
    default: Component.default_opts()[:max_selectable],
    doc: "limits the maximum number of selectable elements. `0` means unlimited"
  )

  attr(:user_defined_options, :boolean,
    default: Component.default_opts()[:user_defined_options],
    doc: "if `true`, hitting enter will always add the text entered by the user to the selection"
  )

  attr(:allow_clear, :boolean,
    doc:
      ~s(if `true`, when in single mode, display a "x" button in the input field to clear the selection)
  )

  attr(:disabled, :boolean, doc: "set this to `true` to disable the input field")

  attr(:placeholder, :string, doc: "placeholder text for the input field")

  attr(:label, :string, doc: "label for the input field")
  attr(:name, :any)

  attr(:debounce, :integer,
    default: Component.default_opts()[:debounce],
    doc:
      ~S(number of milliseconds to wait after the last keystroke before triggering a "live_select_change" event)
  )

  attr(:value_mapper, :any,
    doc:
      ~s(function used to map the values from a form to LiveSelect options. Important: the output of this function should be JSON-encodable)
  )

  attr(:update_min_len, :integer,
    default: Component.default_opts()[:update_min_len],
    doc:
      "the minimum length of text in the text input field that will trigger an update of the dropdown."
  )

  attr(:style, :atom,
    values: [:tailwind, :daisyui, :none],
    default: Component.default_opts()[:style],
    doc:
      "one of `:tailwind`, `:daisyui` or `:none`. See the [Styling section](styling.html) for details"
  )

  slot(:option,
    doc:
      "optional slot that renders an option in the dropdown. The option's data is available via `:let`"
  )

  slot(:tag, doc: "optional slot that renders a tag. The option's data is available via `:let`")

  slot(:clear_button, doc: "optional slot to render a custom clear button")

  attr(:"phx-target", :any,
    doc: "Optional target for events. Usually the same target as the form's"
  )

  attr(:"phx-blur", :string,
    doc:
      "Event to emit when the text input loses focus. The component id will be sent in the event's params"
  )

  attr(:"phx-focus", :string,
    doc:
      "Event to emit when the text input receives focus. The component id will be sent in the event's params"
  )

  @styling_options ~w(active_option_class available_option_class clear_button_class clear_button_extra_class clear_tag_button_class clear_tag_button_extra_class container_class container_extra_class dropdown_class dropdown_extra_class option_class option_extra_class text_input_class text_input_extra_class text_input_selected_class selected_option_class tag_class tag_extra_class tags_container_class tags_container_extra_class)a

  for attr_name <- @styling_options do
    Phoenix.Component.Declarative.__attr__!(
      __MODULE__,
      attr_name,
      :any,
      [doc: false],
      __ENV__.line,
      __ENV__.file
    )
  end

  def live_select(assigns) do
    assigns =
      assigns
      |> update(:field, fn
        %Phoenix.HTML.FormField{} = field, _ ->
          field

        field, %{form: form} ->
          IO.warn(
            "instead of passing separate form and field attributes, pass a single field attribute of type Phoenix.HTML.FormField"
          )

          to_form(form)[field]

        _, _ ->
          raise "if you pass field as atom or string, you also have to pass a form"
      end)
      |> assign_new(:id, fn %{field: field} ->
        "#{field.form.name}_#{field.field}_live_select_component"
      end)
      |> assign(module: AppWeb.Component)

    ~H"""
    <div>
      <.label for={@id} class="text-gray-600"><%= @label %></.label>
      <.live_component {assigns} />
    </div>
    """
  end

  def decode(selection) do
    json = Phoenix.json_library()

    case selection do
      nil -> []
      "" -> nil
      selection when is_list(selection) -> Enum.map(selection, &json.decode!/1)
      selection -> json.decode!(selection)
    end
  end

  @doc """
      Renders a card for stats
  """

  attr :title, :string, default: nil
  attr :icon, :string, default: nil
  attr :stats_value, :string, default: nil
  attr :value_prefix, :string, default: nil
  attr :stats_desc, :string, default: nil

  def stats_card(assigns) do
    ~H"""
    <div class="p-6 hover:shadow-xl hover:border-brand-10/40 transition-all duration-300 ease-in-out border-brand-10/30 space-y-2 rounded-lg bg-white border w-full h-full">
      <span class="flex justify-between items-center text-brand-1 font-medium">
        <%= @title %>
        <.icon name={@icon || ""} class="w-8 h-8 p-2 text-white bg-brand-10 rounded-full" />
      </span>

      <div class="flex items-center justify-between gap-8">
        <div class="space-y-3">
          <h1 class="text-3xl font-semibold text-brand-1">
            <%= @stats_value %>
          </h1>

          <p class="text-sm text-gray-600"><%= @stats_desc %></p>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  Renders an input with label and error messages.

  A `Phoenix.HTML.FormField` may be passed as argument,
  which is used to retrieve the input name, id, and values.
  Otherwise all attributes may be passed explicitly.

  ## Types

  This function accepts all HTML input types, considering that:

    * You may also set `type="select"` to render a `<select>` tag

    * `type="checkbox"` is used exclusively to render boolean values

    * For live file uploads, see `Phoenix.Component.live_file_input/1`

  See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input
  for more information.

  ## Examples

      <.input field={@form[:email]} type="email" />
      <.input name="my-input" errors={["oh no!"]} />
  """
  attr(:id, :any, default: nil)
  attr(:class, :string, default: "")
  attr(:name, :any)
  attr(:label, :string, default: nil)
  attr(:label_class, :string, default: "text-zinc-600")
  attr(:fill_class, :string, default: "")
  attr(:value, :any)

  attr(:type, :string,
    default: "text",
    values:
      ~w(checkbox color date datetime-local email file hidden month number password range radio search select tel text mobile textarea time url week)
  )

  attr(:field, Phoenix.HTML.FormField,
    doc: "a form field struct retrieved from the form, for example: @form[:email]"
  )

  attr(:errors, :list, default: [])
  attr(:checked, :boolean, doc: "the checked flag for checkbox inputs")
  attr(:prompt, :string, default: nil, doc: "the prompt for select inputs")
  attr(:options, :list, doc: "the options to pass to Phoenix.HTML.Form.options_for_select/2")
  attr(:multiple, :boolean, default: false, doc: "the multiple flag for select inputs")

  attr(:rest, :global,
    include: ~w(accept autocomplete capture cols disabled form list max maxlength min minlength
                multiple pattern placeholder readonly required rows size step)
  )

  slot(:inner_block)

  def input(%{field: %Phoenix.HTML.FormField{} = field} = assigns) do
    assigns
    |> assign(field: nil, id: assigns.id || field.id)
    |> assign(:errors, Enum.map(field.errors, &translate_error(&1)))
    |> assign_new(:name, fn -> if assigns.multiple, do: field.name <> "[]", else: field.name end)
    |> assign_new(:value, fn -> field.value end)
    |> input()
  end

  def input(%{type: "checkbox"} = assigns) do
    assigns =
      assign_new(assigns, :checked, fn ->
        Phoenix.HTML.Form.normalize_value("checkbox", assigns[:value])
      end)

    ~H"""
    <div phx-feedback-for={@name}>
      <label class={"flex items-center gap-4 text-sm leading-6 #{@label_class}"}>
        <input type="hidden" name={@name} value="false" />
        <input
          type="checkbox"
          id={@id}
          name={@name}
          value="true"
          checked={@checked}
          class="rounded border-zinc-300 text-zinc-900 focus:ring-0"
          {@rest}
        /> <%= @label %>
      </label>

      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  def input(%{type: "select"} = assigns) do
    ~H"""
    <div phx-feedback-for={@name}>
      <.label for={@id} class={@label_class}><%= @label %></.label>

      <select
        id={@id}
        name={@name}
        class="mt-2 block w-full rounded-md border border-gray-300 bg-white shadow-sm focus:border-zinc-400 focus:ring-0 sm:text-sm"
        multiple={@multiple}
        {@rest}
      >
        <option :if={@prompt} value=""><%= @prompt %></option>
        <%= Phoenix.HTML.Form.options_for_select(@options, @value) %>
      </select>

      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  def input(%{type: "textarea"} = assigns) do
    ~H"""
    <div phx-feedback-for={@name}>
      <.label for={@id} class={@label_class}><%= @label %></.label>
      <textarea
        id={@id}
        name={@name}
        class={[
          "mt-2 block w-full rounded-lg text-zinc-900 focus:ring-0 sm:text-sm sm:leading-6",
          "min-h-[6rem] phx-no-feedback:border-zinc-300 phx-no-feedback:focus:border-zinc-400",
          @errors == [] && "border-zinc-300 focus:border-zinc-400",
          @errors != [] && "border-rose-400 focus:border-rose-400"
        ]}
        {@rest}
      ><%= Phoenix.HTML.Form.normalize_value("textarea", @value) %></textarea>
      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  def input(%{type: "mobile"} = assigns) do
    ~H"""
    <div phx-feedback-for={@name}>
      <.label for={@id} class={@label_class}><%= @label %></.label>

      <div class="relative">
        <div class="absolute sm:text-sm sm:leading-6 inset-y-0 start-0 top-0 flex items-center  ps-3.5 pr-2 rounded-l-lg pointer-events-none bg-gray-200">
          260
        </div>

        <input
          type="tel"
          name={@name}
          id={@id}
          value={Phoenix.HTML.Form.normalize_value(@type, @value)}
          class={[
            "mt-2 block w-full ps-12 p-2 rounded-lg text-zinc-900 outline outline-0 sm:text-sm sm:leading-6 border border-zinc-300 focus:border-zinc-400",
            @errors == [] && "border-zinc-300 focus:border-zinc-400",
            @errors != [] && "border-rose-400 focus:border-rose-400"
          ]}
          oninput="this.value = this.value.replace(/[^0-9]/g, '')"
          {@rest}
        />
      </div>

      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  def input(%{type: "color"} = assigns) do
    ~H"""
    <div phx-feedback-for={@name} class={@fill_class}>
      <.label for={@id} class={@label_class}><%= @label %></.label>

      <input
        type="color"
        name={@name}
        id={@id}
        value={Phoenix.HTML.Form.normalize_value(@type, @value)}
        class={[
          "mt-2 block w-full ps-12 p-2 rounded-lg text-zinc-900 outline outline-0 sm:text-sm sm:leading-6 border border-zinc-300 focus:border-zinc-400",
          @errors == [] && "border-zinc-300 focus:border-zinc-400",
          @errors != [] && "border-rose-400 focus:border-rose-400"
        ]}
        oninput="this.value = this.value.replace(/[^0-9]/g, '')"
        {@rest}
      />
      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  # All other inputs text, datetime-local, url, password, etc. are handled here...
  def input(assigns) do
    ~H"""
    <div phx-feedback-for={@name} class={@fill_class}>
      <.label for={@id} class={@label_class}><%= @label %></.label>

      <input
        type={@type}
        name={@name}
        id={@id}
        value={Phoenix.HTML.Form.normalize_value(@type, @value)}
        class={[
          " mt-2 block w-full rounded-lg text-zinc-900 focus:ring-0 sm:text-sm sm:leading-6",
          "phx-no-feedback:border-zinc-300 phx-no-feedback:focus:border-zinc-400",
          @errors == [] && "border-zinc-300 focus:border-zinc-400",
          @errors != [] && "border-rose-400 focus:border-rose-400"
        ]}
        {@rest}
      />
      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  attr(:id, :any, default: nil)
  attr(:name, :any)
  attr(:label, :string, default: nil)
  attr(:icon_start, :string, default: nil)
  attr(:icon_end, :string, default: nil)
  attr(:value, :any)

  attr(:type, :string,
    default: "text",
    values: ~w(checkbox color date datetime-local email file hidden month number password
               range radio search select tel text textarea time url week)
  )

  attr(:field, Phoenix.HTML.FormField,
    doc: "a form field struct retrieved from the form, for example: @form[:email]"
  )

  attr(:errors, :list, default: [])
  attr(:checked, :boolean, doc: "the checked flag for checkbox inputs")
  attr(:prompt, :string, default: nil, doc: "the prompt for select inputs")
  attr(:options, :list, doc: "the options to pass to Phoenix.HTML.Form.options_for_select/2")
  attr(:multiple, :boolean, default: false, doc: "the multiple flag for select inputs")

  attr(:rest, :global,
    include: ~w(accept autocomplete capture cols disabled form list max maxlength min minlength
                multiple pattern placeholder readonly required rows size step)
  )

  slot(:inner_block)

  def input_form_with_clickable_icon(%{field: %Phoenix.HTML.FormField{} = field} = assigns) do
    assigns
    |> assign(field: nil, id: assigns.id || field.id)
    |> assign(:errors, Enum.map(field.errors, &translate_error(&1)))
    |> assign_new(:name, fn -> if assigns.multiple, do: field.name <> "[]", else: field.name end)
    |> assign_new(:value, fn -> field.value end)
    |> input_form_with_clickable_icon()
  end

  def input_form_with_clickable_icon(assigns) do
    ~H"""
    <div phx-feedback-for={@name}>
      <.label for={@id}><%= @label %></.label>

      <div class={"flex items-center relative #{if(@label !== nil, do: "mt-2")}"}>
        <.icon
          :if={@icon_start}
          name={@icon_start}
          class="absolute left-2  text-fg-3 peer-focus:text-brand-1"
        />
        <div class="absolute right-2 text-fg-3 cursor-pointer" phx-click="toggle_show_password">
          <.icon :if={@icon_end} name={@icon_end} class="peer-focus:text-brand-1" />
        </div>

        <input
          type={@type}
          name={@name}
          id={@id}
          value={Phoenix.HTML.Form.normalize_value(@type, @value)}
          class={[
            "block w-full rounded-lg text-zinc-900 focus:ring-0 sm:text-sm sm:leading-6",
            "phx-no-feedback:border-zinc-300 phx-no-feedback:focus:border-zinc-400",
            @errors == [] && "border-zinc-300 focus:border-zinc-400",
            @errors != [] && "border-rose-400 focus:border-rose-400",
            if(@icon_start, do: "pl-10"),
            if(@icon_end, do: "pr-10")
          ]}
          {@rest}
        />
      </div>

      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  @doc """
  Renders a label.
  """
  attr(:for, :string, default: nil)
  attr(:class, :string, default: "text-zinc-800")
  slot(:inner_block, required: true)

  def label(assigns) do
    ~H"""
    <label for={@for} class={"block text-sm font-semibold leading-6 #{@class}"}>
      <%= render_slot(@inner_block) %>
    </label>
    """
  end

  @doc """
  Generates a generic error message.
  """
  slot(:inner_block, required: true)

  def error(assigns) do
    ~H"""
    <p class="mt-3 flex gap-3 text-sm leading-6 text-rose-600 phx-no-feedback:hidden">
      <.icon name="hero-exclamation-circle-mini" class="mt-0.5 h-5 w-5 flex-none" /> <%= render_slot(
        @inner_block
      ) %>
    </p>
    """
  end

  @doc """
  Renders a header with title.
  """
  attr(:class, :string, default: nil)

  slot(:inner_block, required: true)
  slot(:subtitle)
  slot(:actions)

  def header(assigns) do
    ~H"""
    <header class={[@actions != [] && "flex items-center justify-between gap-6", @class]}>
      <div>
        <h1 class="text-lg font-semibold leading-8 text-zinc-800">
          <%= render_slot(@inner_block) %>
        </h1>

        <p :if={@subtitle != []} class="mt-2 text-sm leading-6 text-zinc-600">
          <%= render_slot(@subtitle) %>
        </p>
      </div>

      <div class="flex-none"><%= render_slot(@actions) %></div>
    </header>
    """
  end

  @doc ~S"""
  Renders a table with generic styling.

  ## Examples

      <.table id="users" rows={@users}>
        <:col :let={user} label="id"><%= user.id %></:col>
        <:col :let={user} label="username"><%= user.username %></:col>
      </.table>
  """
  attr(:id, :string, required: true)
  attr(:rows, :list, required: true)
  attr(:row_id, :any, default: nil, doc: "the function for generating the row id")
  attr(:row_click, :any, default: nil, doc: "the function for handling phx-click on each row")

  attr(:row_item, :any,
    default: &Function.identity/1,
    doc: "the function for mapping each row before calling the :col and :action slots"
  )

  slot :col, required: true do
    attr(:label, :string)
  end

  slot(:action, doc: "the slot for showing user actions in the last table column")

  def table(assigns) do
    assigns =
      with %{rows: %Phoenix.LiveView.LiveStream{}} <- assigns do
        assign(assigns, row_id: assigns.row_id || fn {id, _item} -> id end)
      end

    ~H"""
    <div class="overflow-y-auto px-4 sm:overflow-visible sm:px-0">
      <table class="w-[40rem] mt-11 sm:w-full">
        <thead class="text-sm text-left leading-6 text-zinc-500">
          <tr>
            <th :for={col <- @col} class="p-0 pb-4 pr-6 font-normal"><%= col[:label] %></th>

            <th :if={@action != []} class="relative p-0 pb-4">
              <span class="sr-only"><%= gettext("Actions") %></span>
            </th>
          </tr>
        </thead>

        <tbody
          id={@id}
          phx-update={match?(%Phoenix.LiveView.LiveStream{}, @rows) && "stream"}
          class="relative divide-y divide-zinc-100 border-t border-zinc-200 text-sm leading-6 text-zinc-700"
        >
          <tr :for={row <- @rows} id={@row_id && @row_id.(row)} class="group hover:bg-zinc-50">
            <td
              :for={{col, i} <- Enum.with_index(@col)}
              phx-click={@row_click && @row_click.(row)}
              class={["relative p-0", @row_click && "hover:cursor-pointer"]}
            >
              <div class="block py-4 pr-6">
                <span class="absolute -inset-y-px right-0 -left-4 group-hover:bg-zinc-50 sm:rounded-l-xl" />
                <span class={["relative", i == 0 && "font-semibold text-zinc-900"]}>
                  <%= render_slot(col, @row_item.(row)) %>
                </span>
              </div>
            </td>

            <td :if={@action != []} class="relative w-14 p-0">
              <div class="relative whitespace-nowrap py-4 text-right text-sm font-medium">
                <span class="absolute -inset-y-px -right-4 left-0 group-hover:bg-zinc-50 sm:rounded-r-xl" />
                <span
                  :for={action <- @action}
                  class="relative ml-4 font-semibold leading-6 text-zinc-900 hover:text-zinc-700"
                >
                  <%= render_slot(action, @row_item.(row)) %>
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    """
  end

  @doc """
  Renders a data list.

  ## Examples

      <.list>
        <:item title="Title"><%= @post.title %></:item>
        <:item title="Views"><%= @post.views %></:item>
      </.list>
  """
  slot :item, required: true do
    attr(:title, :string, required: true)
  end

  def list(assigns) do
    ~H"""
    <div class="mt-14">
      <dl class="-my-4 divide-y divide-zinc-100">
        <div :for={item <- @item} class="flex gap-4 py-4 text-sm leading-6 sm:gap-8">
          <dt class="w-1/4 flex-none text-zinc-500"><%= item.title %></dt>

          <dd class="text-zinc-700"><%= render_slot(item) %></dd>
        </div>
      </dl>
    </div>
    """
  end

  @doc """
  Renders a back navigation link.

  ## Examples

      <.back navigate={~p"/posts"}>Back to posts</.back>
  """
  attr(:navigate, :any, required: true)
  slot(:inner_block, required: true)

  def back(assigns) do
    ~H"""
    <div class="mt-16">
      <.link
        navigate={@navigate}
        class="text-sm font-semibold leading-6 text-zinc-900 hover:text-zinc-700"
      >
        <.icon name="hero-arrow-left-solid" class="h-3 w-3" /> <%= render_slot(@inner_block) %>
      </.link>
    </div>
    """
  end

  @doc """
  Renders a [Heroicon](https://heroicons.com).

  Heroicons come in three styles – outline, solid, and mini.
  By default, the outline style is used, but solid and mini may
  be applied by using the `-solid` and `-mini` suffix.

  You can customize the size and colors of the icons by setting
  width, height, and background color classes.

  Icons are extracted from the `deps/heroicons` directory and bundled within
  your compiled app.css by the plugin in your `assets/tailwind.config.js`.

  ## Examples

      <.icon name="hero-x-mark-solid" />
      <.icon name="hero-arrow-path" class="ml-1 w-3 h-3 animate-spin" />
  """
  attr(:name, :string, required: true)
  attr(:class, :string, default: nil)

  def icon(%{name: "hero-" <> _} = assigns) do
    ~H"""
    <span class={[@name, @class]} />
    """
  end

  def icon(assigns) do
    ~H"""
    <span class="" class={["px-3 py-2 rounded-lg flex items-center justify-center ", @class]}>
      <%= icon(@name, class: @class) %>
    </span>
    """
  end

  @doc """
  Renders a simple form.

  ## Examples

      <.simple_form_no_style for={@form} phx-change="validate" phx-submit="save">
        <.input field={@form[:email]} label="Email"/>
        <.input field={@form[:username]} label="Username" />
        <:actions>
          <.button>Save</.button>
        </:actions>
      </.simple_form_no_style>
  """
  attr(:for, :any, required: true, doc: "the datastructure for the form")
  attr(:as, :any, default: nil, doc: "the server side parameter to collect all input under")

  attr(:class, :string, default: "")
  attr(:form_class, :string, default: "")

  attr(:rest, :global,
    include: ~w(autocomplete name rel action enctype method novalidate target),
    doc: "the arbitrary HTML attributes to apply to the form tag"
  )

  slot(:inner_block, required: true)
  slot(:actions, doc: "the slot for form actions, such as a submit button")

  def simple_form_login(assigns) do
    ~H"""
    <.form :let={f} for={@for} as={@as} {@rest} class={@form_class}>
      <div class={@class}>
        <%= render_slot(@inner_block, f) %>
        <div :for={action <- @actions}>
          <%= render_slot(action, f) %>
        </div>
      </div>
    </.form>
    """
  end

  @doc """
  Renders an input with label and error messages.

  A `Phoenix.HTML.FormField` may be passed as argument,
  which is used to retrieve the input name, id, and values.
  Otherwise all attributes may be passed explicitly.

  ## Types

  This function accepts all HTML input types, considering that:

    * You may also set `type="select"` to render a `<select>` tag

    * `type="checkbox"` is used exclusively to render boolean values

    * For live file uploads, see `Phoenix.Component.live_file_input/1`

  See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input
  for more information.

  ## Examples

      <.input_form field={@form[:email]} type="email" />
      <.input_form name="my-input" errors={["oh no!"]} />
  """
  attr(:id, :any, default: nil)
  attr(:name, :any)
  attr(:label, :string, default: nil)
  attr(:value, :any)
  attr(:class, :string, default: "")
  attr(:label_class, :string, default: "text-gray-600")

  attr(:type, :string,
    default: "text",
    values:
      ~w(checkbox color date datetime-local email file hidden month number password range radio search select tel text textarea time url week)
  )

  attr(:field, Phoenix.HTML.FormField,
    doc: "a form field struct retrieved from the form, for example: @form[:email]"
  )

  attr(:errors, :list, default: [])
  attr(:checked, :boolean, doc: "the checked flag for checkbox inputs")
  attr(:prompt, :string, default: nil, doc: "the prompt for select inputs")
  attr(:options, :list, doc: "the options to pass to Phoenix.HTML.Form.options_for_select/2")
  attr(:multiple, :boolean, default: false, doc: "the multiple flag for select inputs")

  attr(:rest, :global,
    include: ~w(accept autocomplete capture cols disabled form list max maxlength min minlength
                multiple pattern placeholder readonly required rows size step)
  )

  slot(:inner_block)

  def input_form(%{field: %Phoenix.HTML.FormField{} = field} = assigns) do
    assigns
    |> assign(field: nil, id: assigns.id || field.id)
    |> assign(:errors, Enum.map(field.errors, &translate_error(&1)))
    |> assign_new(:name, fn -> if assigns.multiple, do: field.name <> "[]", else: field.name end)
    |> assign_new(:value, fn -> field.value end)
    |> input_form()
  end

  def input_form(assigns) do
    ~H"""
    <div phx-feedback-for={@name} class="w-full">
      <label for={@id} class={"block mb-2 text-sm font-medium #{@label_class}"}><%= @label %></label>
      <input
        type={@type}
        name={@name}
        id={@id}
        value={Phoenix.HTML.Form.normalize_value(@type, @value)}
        class={[
          "#{@class} block w-full px-4 py-2 mt-2 text-gray-700 placeholder-gray-400 bg-white border border-gray-200 rounded-lg focus:border-brand-10 focus:ring-brand-10 focus:outline-none focus:ring focus:ring-opacity-40",
          @errors != [] && "border-rose-400 focus:border-rose-400"
        ]}
        {@rest}
      />
      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  @doc """
  Renders an input with label and error messages.

  A `Phoenix.HTML.FormField` may be passed as argument,
  which is used to retrieve the input name, id, and values.
  Otherwise all attributes may be passed explicitly.

  ## Types

  This function accepts all HTML input types, considering that:

    * You may also set `type="select"` to render a `<select>` tag

    * `type="checkbox"` is used exclusively to render boolean values

    * For live file uploads, see `Phoenix.Component.live_file_input/1`

  See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input
  for more information.

  ## Examples

      <.ns_input_form field={@form[:email]} type="email" />
      <.ns_input_form name="my-input" errors={["oh no!"]} />
  """
  attr(:id, :any, default: nil)
  attr(:name, :any)
  attr(:label, :string, default: nil)
  attr(:value, :any)
  attr(:class, :string, default: "")
  attr(:label_class, :string, default: "text-gray-600")

  attr(:type, :string,
    default: "text",
    values:
      ~w(checkbox color date datetime-local email file hidden month number password range radio search select tel text textarea time url week)
  )

  attr(:field, Phoenix.HTML.FormField,
    doc: "a form field struct retrieved from the form, for example: @form[:email]"
  )

  attr(:errors, :list, default: [])
  attr(:checked, :boolean, doc: "the checked flag for checkbox inputs")
  attr(:prompt, :string, default: nil, doc: "the prompt for select inputs")
  attr(:options, :list, doc: "the options to pass to Phoenix.HTML.Form.options_for_select/2")
  attr(:multiple, :boolean, default: false, doc: "the multiple flag for select inputs")

  attr(:rest, :global,
    include: ~w(accept autocomplete capture cols disabled form list max maxlength min minlength
                multiple pattern placeholder readonly required rows size step)
  )

  slot(:inner_block)

  def ns_input_form(%{field: %Phoenix.HTML.FormField{} = field} = assigns) do
    assigns
    |> assign(field: nil, id: assigns.id || field.id)
    |> assign(:errors, Enum.map(field.errors, &translate_error(&1)))
    |> assign_new(:name, fn -> if assigns.multiple, do: field.name <> "[]", else: field.name end)
    |> assign_new(:value, fn -> field.value end)
    |> ns_input_form()
  end

  def ns_input_form(assigns) do
    ~H"""
    <div phx-feedback-for={@name} class="mb-4">
      <label for={@id} class={@label_class}><%= @label %></label>
      <input
        type={@type}
        name={@name}
        id={@id}
        value={Phoenix.HTML.Form.normalize_value(@type, @value)}
        class={["#{@class}", @errors != [] && "border-rose-400 focus:border-rose-400"]}
        {@rest}
      />
      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  def assign_form(socket, %Ecto.Changeset{} = changeset, key) do
    assign(socket, key, to_form(changeset))
  end

  @doc """
  Renders a button.
    ## Examples

  <.button_no_style>Send!</.button>
                          <.button_no_style phx-click="go" class="ml-2">Send!</.button>
  """
  attr(:type, :string, default: nil)
  attr(:class, :string, default: nil)
  attr(:rest, :global, include: ~w(disabled form name value))

  slot(:inner_block, required: true)

  def button_no_style(assigns) do
    ~H"""
    <button
      type={@type}
      class={[
        "phx-submit-loading:opacity-75 rounded-lg",
        "text-sm font-semibold leading-6",
        @class
      ]}
      {@rest}
    >
      <%= render_slot(@inner_block) %>
    </button>
    """
  end

  def icon_tag(assigns) do
    ~H"""
    <span></span>
    """
  end

  def assign_form(socket, %Ecto.Changeset{} = changeset, key) do
    assign(socket, key, to_form(changeset))
  end

  def assign_form(socket, %Ecto.Changeset{} = changeset) do
    assign(socket, :form, to_form(changeset))
  end

  ## JS Commands

  def show(js \\ %JS{}, selector) do
    JS.show(js,
      to: selector,
      transition:
        {"transition-all transform ease-out duration-300",
         "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95",
         "opacity-100 translate-y-0 sm:scale-100"}
    )
  end

  def hide(js \\ %JS{}, selector) do
    JS.hide(js,
      to: selector,
      time: 200,
      transition:
        {"transition-all transform ease-in duration-200",
         "opacity-100 translate-y-0 sm:scale-100",
         "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"}
    )
  end

  def show_modal(js \\ %JS{}, id) when is_binary(id) do
    js
    |> JS.show(to: "##{id}")
    |> JS.show(
      to: "##{id}-bg",
      transition: {"transition-all transform ease-out duration-300", "opacity-0", "opacity-100"}
    )
    |> show("##{id}-container")
    |> JS.add_class("overflow-hidden", to: "body")
    |> JS.focus_first(to: "##{id}-content")
  end

  def hide_modal(js \\ %JS{}, id) do
    js
    |> JS.hide(
      to: "##{id}-bg",
      transition: {"transition-all transform ease-in duration-200", "opacity-100", "opacity-0"}
    )
    |> hide("##{id}-container")
    |> JS.hide(to: "##{id}", transition: {"block", "block", "hidden"})
    |> JS.remove_class("overflow-hidden", to: "body")
    |> JS.pop_focus()
  end

  @doc """
  Translates an error message using gettext.
  """
  def translate_error({msg, opts}) do
    # When using gettext, we typically pass the strings we want
    # to translate as a static argument:
    #
    #     # Translate the number of files with plural rules
    #     dngettext("errors", "1 file", "%{count} files", count)
    #
    # However the error messages in our forms and APIs are generated
    # dynamically, so we need to translate them by calling Gettext
    # with our gettext backend as first argument. Translations are
    # available in the errors.po file (as we use the "errors" domain).
    if count = opts[:count] do
      Gettext.dngettext(AppWeb.Gettext, "errors", msg, msg, count, opts)
    else
      Gettext.dgettext(AppWeb.Gettext, "errors", msg, opts)
    end
  end

  @doc """
  Translates the errors for a field from a keyword list of errors.
  """
  def translate_errors(errors, field) when is_list(errors) do
    for {^field, {msg, opts}} <- errors, do: translate_error({msg, opts})
  end

  def get_ext(name) do
    String.split(name, ".")
    |> Enum.at(-1)
    |> String.downcase()
  end

  def get_file_image(name) do
    case get_ext(name) do
      "csv" -> ~p"/images/files/csv.png"
      "xlsx" -> ~p"/images/files/xlsx.png"
      _ -> ~p"/images/files/unsupported_file.png"
    end
  end

  @doc """
  Progress indicator with animations
  """
  attr(:data, :list, default: [])
  attr(:current, :integer, default: 1)

  def progress_indicator(assigns) do
    inactive_style = "opacity-40"

    ~H"""
    <div class="md:flex-col flex justify-evenly md:justify-start flex-row pb-5">
      <div
        :for={{data, index} <- Enum.with_index(@data)}
        class={"#{if(index < length(@data) -1, do: "flex-1")} items-start flex gap-8 min-h-42 cursor-pointer md:hover:bg-white/[5%] md:px-4 rounded transition-all duration-300 ease-in-out transform hover:scale-105"}
      >
        <div class="flex flex-1 md:flex-initial w-full sm:w-fit flex-row md:flex-col md:h-full items-center">
          <.icon
            name={data.icon || "hero-star"}
            class={[
              "w-8 h-8 p-2 border-4 border-white rounded-full transition-all duration-300 ease-in-out transform hover:rotate-12",
              if(@current < index, do: inactive_style),
              if(@current == index, do: "animate-bounce")
            ]}
          />
          <span
            :if={index < length(@data) - 1}
            class={[
              "w-full h-px md:h-full md:w-px bg-white my-2 flex-1 transition-all duration-500",
              if(@current < index, do: inactive_style),
              if(@current == index, do: "animate-pulse")
            ]}
          >
          </span>
        </div>

        <div class="pb-5 hidden md:flex flex-col transition-all duration-300 ease-in-out transform">
          <p class={[
            "font-medium text-lg transition-all duration-300",
            if(@current < index, do: inactive_style),
            if(@current == index, do: "scale-105 translate-x-1")
          ]}>
            <%= data.title %>
          </p>

          <p class={[
            "text-white/80 pt-2 transition-all duration-300",
            if(@current < index, do: inactive_style),
            if(@current == index, do: "translate-x-2")
          ]}>
            <%= data.desc %>
          </p>
        </div>
      </div>
    </div>
    """
  end

  def transition(from, to) do
    %{
      id: Ecto.UUID.generate(),
      "phx-hook": "transition",
      "data-transition-from": from,
      "data-transition-to": to
    }
  end

  @doc """
  """
  slot(:auth)
  slot(:brand)
  attr(:class, :string, default: nil)

  def top_bar(assigns) do
    ~H"""
    <nav class={["flex sticky top-0 z-20 p-4 items-center bg-white justify-between w-full"]}>
      <nav
        x-data="{open : false}"
        class={["relative flex items-center justify-between w-full", @class]}
      >
        <%= render_slot(@brand) %>
        <div
          x-show="open"
          @click.away="open = false"
          @click="open = false"
          x-transition
          x-transition.scale.origin.top
          class="flex flex-col z-[100] md:hidden absolute top-full inset-x-0 w-full !bg-white p-4 border-y"
        >
          <.link
            :for={menu <- @menus}
            navigate={menu.link}
            class="p-2 border-b-2 border-white hover:border-brand-2 hover:text-brand-2"
          >
            <%= menu.name %>
          </.link>
        </div>

        <div class="flex gap-4 items-center justify-end">
          <div class="md:flex gap-4 items-center hidden">
            <.link
              :for={menu <- @menus}
              navigate={menu.link}
              class="p-2  border-b-2 border-white hover:border-brand-2 hover:text-brand-2"
            >
              <%= menu.name %>
            </.link>
          </div>
          <%= render_slot(@auth) %>
          <button x-on:click="open = !open" class="!bg-white !text-fg-1 p-2 md:hidden">
            <.icon name="hero-bars-3" class="md:hidden text-xl" />
          </button>
        </div>
      </nav>
    </nav>
    """
  end

  def skeleton_navbar(assigns) do
    ~H"""
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50 animate-pulse">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <!-- Logo Placeholder -->
          <div class="flex items-center">
            <div class="w-24 h-8 bg-gray-300 rounded"></div>
          </div>
          <!-- Desktop Navigation Placeholders -->
          <div class="hidden md:flex items-center space-x-4 lg:space-x-8">
            <div class="w-16 h-6 bg-gray-300 rounded"></div>

            <div class="w-16 h-6 bg-gray-300 rounded"></div>

            <div class="w-16 h-6 bg-gray-300 rounded"></div>
          </div>
          <!-- Mobile Menu Button Placeholder -->
          <div class="md:hidden flex items-center">
            <div class="w-6 h-6 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
      <!-- Mobile Navigation Placeholder -->
      <div class="md:hidden transition-all duration-300 ease-in-out overflow-hidden max-h-64">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white shadow-lg">
          <div class="w-full h-8 bg-gray-300 rounded"></div>

          <div class="w-full h-8 bg-gray-300 rounded"></div>

          <div class="w-full h-8 bg-gray-300 rounded"></div>
        </div>
      </div>
    </nav>
    """
  end

  @doc """
      Renders a top bar with default routes
  """

  attr(:menu_open, :boolean, default: false)
  attr(:login_url, :string, required: true)
  attr(:registration_url, :string, required: true)
  attr(:current_page, :string, default: "HOME")
  attr(:img_src, :string, default: "/images/pbs-logo.png")
  attr(:rest, :global, default: %{})

  def navbar(assigns) do
    ~H"""
    <nav {@rest} class="bg-white shadow-lg fixed w-full top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <.link navigate={~p"/"}>
              <img src={@img_src} alt="Probase Logo" class="h-8 w-auto sm:h-10" />
            </.link>
          </div>

          <div class="hidden md:flex items-center space-x-4 lg:space-x-8">
            <.link
              :if={@current_page != "HOME"}
              navigate={~p"/"}
              class="text-sm lg:text-base text-gray-600 hover:text-brand-1"
            >
              Home
            </.link>

            <.link
              :if={@current_page != "LOGIN"}
              navigate={@login_url}
              class="text-sm lg:text-base text-gray-600 hover:text-brand-1"
            >
              Login
            </.link>

            <.link
              :if={@current_page != "SELF_REG"}
              navigate={@registration_url}
              class="text-sm lg:text-base bg-brand-1 text-white px-3 py-2 lg:px-4 lg:py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              Sign Up
            </.link>
          </div>
        </div>
      </div>
    </nav>
    """
  end

  def empty_state(assigns) do
    ~H"""
    <div class="flex flex-col items-center justify-center p-8 bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="rounded-full bg-blue-100 p-4 mb-4">
        <.licence_icon name="document-search" class="h-10 w-10 text-blue-600" />
      </div>

      <h3 class="text-lg font-semibold text-gray-900 mb-1">No Licenses Available</h3>

      <p class="text-gray-500 text-center mb-6">
        There are currently no license forms available in the system.
      </p>

      <button
        phx-click="refresh"
        class="px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors duration-300 flex items-center"
      >
        <.licence_icon name="refresh" class="h-4 w-4 mr-2" /> Refresh List
      </button>
    </div>
    """
  end

  def default_top_bar(assigns) do
    ~H"""
    <.top_bar
      class="bg-white max-w-7xl mx-auto"
      menus={[
        %{:name => "Home", :link => "/"},
        %{:name => "Features", :link => "/home#services"},
        %{:name => "Contact us", :link => "/home#contact-us"},
        %{:name => "How It Works", :link => "/how-to"}
      ]}
    >
      <:brand>
        <div class="flex gap-2 items-center">
          <img src="/images/pbs-logo.png" class="h-8 w-auto sm:h-10" />
        </div>
      </:brand>

      <:auth>
        <.link navigate="/users/log_in" class="btn px-4 py-2"> Sign in </.link>
      </:auth>
    </.top_bar>
    """
  end

  """

  <div class="space-y-2">
    <!--Button trigger extra large modal-->
    <button
      type="button"
      class="inline-block rounded bg-primary px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-primary-3 transition duration-150 ease-in-out hover:bg-primary-accent-300 hover:shadow-primary-2 focus:bg-primary-accent-300 focus:shadow-primary-2 focus:outline-none focus:ring-0 active:bg-primary-600 active:shadow-primary-2 dark:shadow-black/30 dark:hover:shadow-dark-strong dark:focus:shadow-dark-strong dark:active:shadow-dark-strong"
      data-twe-toggle="modal"
      data-twe-target="#exampleModalXl"
      data-twe-ripple-init
      data-twe-ripple-color="light">
      Extra large modal
    </button>

    <!--Button trigger large modal-->
    <button
      type="button"
      class="inline-block rounded bg-primary px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-primary-3 transition duration-150 ease-in-out hover:bg-primary-accent-300 hover:shadow-primary-2 focus:bg-primary-accent-300 focus:shadow-primary-2 focus:outline-none focus:ring-0 active:bg-primary-600 active:shadow-primary-2 dark:shadow-black/30 dark:hover:shadow-dark-strong dark:focus:shadow-dark-strong dark:active:shadow-dark-strong"
      data-twe-toggle="modal"
      data-twe-target="#exampleModalLg"
      data-twe-ripple-init
      data-twe-ripple-color="light">
      Large modal
    </button>

    <!--Button trigger small modal-->
    <button
      type="button"
      class="inline-block rounded bg-primary px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-primary-3 transition duration-150 ease-in-out hover:bg-primary-accent-300 hover:shadow-primary-2 focus:bg-primary-accent-300 focus:shadow-primary-2 focus:outline-none focus:ring-0 active:bg-primary-600 active:shadow-primary-2 dark:shadow-black/30 dark:hover:shadow-dark-strong dark:focus:shadow-dark-strong dark:active:shadow-dark-strong"
      data-twe-toggle="modal"
      data-twe-target="#exampleModalSm"
      data-twe-ripple-init
      data-twe-ripple-color="light">
      Small modal
    </button>
  </div>

  <!--Extra large modal-->
  <div
    data-twe-modal-init
    class="fixed left-0 top-0 z-[1055] hidden h-full w-full overflow-y-auto overflow-x-hidden outline-none"
    id="exampleModalXl"
    tabindex="-1"
    aria-labelledby="exampleModalXlLabel"
    aria-modal="true"
    role="dialog">
    <div
      data-twe-modal-dialog-ref
      class="pointer-events-none relative w-auto translate-y-[-50px] opacity-0 transition-all duration-300 ease-in-out min-[576px]:mx-auto min-[576px]:mt-7 min-[576px]:max-w-[500px] min-[992px]:max-w-[800px] min-[1200px]:max-w-[1140px]">
      <div
        class="pointer-events-auto relative flex w-full flex-col rounded-md border-none bg-white bg-clip-padding text-current shadow-4 outline-none dark:bg-surface-dark">
        <div
          class="flex flex-shrink-0 items-center justify-between rounded-t-md border-b-2 border-neutral-100 p-4 dark:border-white/10">
          <!-- Modal title -->
          <h5
            class="text-xl font-medium leading-normal text-surface dark:text-white"
            id="exampleModalXlLabel">
            Extra large modal
          </h5>
          <!-- Close button -->
          <button
            type="button"
            class="box-content rounded-none border-none text-neutral-500 hover:text-neutral-800 hover:no-underline focus:text-neutral-800 focus:opacity-100 focus:shadow-none focus:outline-none dark:text-neutral-400 dark:hover:text-neutral-300 dark:focus:text-neutral-300"
            data-twe-modal-dismiss
            aria-label="Close">
            <span class="[&>svg]:h-6 [&>svg]:w-6">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="currentColor"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M6 18L18 6M6 6l12 12" />
              </svg>
            </span>
          </button>
        </div>

        <!-- Modal body -->
        <div class="relative p-4">...</div>
      </div>
    </div>
  </div>
  """
end
