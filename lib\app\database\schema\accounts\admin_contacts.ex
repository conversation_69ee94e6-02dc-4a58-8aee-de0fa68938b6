defmodule App.Accounts.AdminContacts do
  use Ecto.Schema
  import Ecto.Changeset

  schema "tbl_admin_contacts" do
    field :name, :string
    field :email, :string
    field :mobile, :string
    field :status, :string

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(user, attrs) do
    user
    |> cast(attrs, [:name, :email, :mobile, :status])
    |> validate_required([:name, :email, :mobile])
  end
end
