<!-- Notifications page -->
<div class="px-4 sm:px-6 lg:px-8 py-8 max-w-7xl mx-auto">
  <!-- Header -->
  <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
    <h1 class="text-2xl font-bold text-gray-900">Your Notifications</h1>
    <!-- Optional filter/search controls could go here -->
    <div class="flex space-x-2">
      <%= if !Enum.empty?(@user_notifications) do %>
        <button
          phx-click="read_all"
          class="px-3 py-2 bg-brand-10 text-white rounded-md text-sm font-medium hover:bg-indigo-700 transition-colors flex items-center gap-2"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M3 19v-8.93a2 2 0 01.89-1.664l7-4.666a2 2 0 012.22 0l7 4.666A2 2 0 0121 10.07V19M3 19a2 2 0 002 2h14a2 2 0 002-2M3 19l6.75-4.5M21 19l-6.75-4.5M3 10l6.75 4.5M21 10l-6.75 4.5m0 0l-1.14.76a2 2 0 01-2.22 0l-1.14-.76"
            />
          </svg>
          Mark All as Read
        </button>
      <% end %>
    </div>
  </div>
  <!-- Notifications Container -->
  <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200">
    <!-- Header -->
    <div class="bg-gradient-to-r from-brand-10 to-indigo-700 text-white p-4">
      <h2 class="text-lg font-bold text-center">Notifications Center</h2>
    </div>

    <%= if !Enum.empty?(@user_notifications) do %>
      <!-- Table Header - Only visible on desktop -->
      <div class="hidden sm:grid bg-gray-50 text-gray-600 py-3 px-6 grid-cols-6 border-b border-gray-200">
        <div class="col-span-3 font-medium">Message</div>

        <div class="col-span-2 font-medium">Type</div>

        <div class="col-span-1 text-center font-medium">Status</div>
      </div>
      <!-- Notification Items -->
      <div class="divide-y divide-gray-200">
        <%= for notification <- @user_notifications do %>
          <!-- Desktop View - Grid Layout -->
          <div class="hover:bg-gray-50 transition-colors duration-150 hidden sm:block">
            <.link
              phx-click="read_notification"
              phx-value-id={notification.id}
              phx-value-url={notification.page_url}
              phx-value-status="true"
              class="grid grid-cols-6 items-center p-4 text-sm text-gray-700 gap-4"
              role="menuitem"
              tabindex="-1"
              id={"user-menu-item-desktop-#{notification.id}"}
            >
              <!-- Message -->
              <div class="col-span-3">
                <div class="flex items-start gap-3">
                  <!-- Notification Icon -->
                  <div class="flex-shrink-0 mt-1">
                    <%= if String.contains?(String.downcase(notification.type || ""), "alert") do %>
                      <div class="p-2 bg-red-100 text-red-600 rounded-full">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                    <% else %>
                      <div class="p-2 bg-blue-100 text-blue-600 rounded-full">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </div>
                    <% end %>
                  </div>

                  <div>
                    <h3 class="font-semibold text-gray-900">
                      <%= notification.message %>
                    </h3>

                    <p class="text-xs text-gray-500 mt-1">
                      <%= Calendar.strftime(
                        NaiveDateTime.add(notification.inserted_at, 7200, :second),
                        "%d %B %Y %H:%M"
                      ) %>
                    </p>
                  </div>
                </div>
              </div>
              <!-- Type -->
              <div class="col-span-2">
                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <%= notification.type %>
                </span>
              </div>
              <!-- Status -->
              <div class="text-center col-span-1">
                <%= if notification.status do %>
                  <div class="text-green-500 flex items-center justify-center gap-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>Seen</span>
                  </div>
                <% else %>
                  <div class="text-brand-10 flex items-center justify-center gap-2 hover:text-indigo-800">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      class="h-5 w-5"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                      />
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                      />
                    </svg>
                    <span>View</span>
                  </div>
                <% end %>
              </div>
            </.link>
          </div>
          <!-- Mobile View - Card Layout -->
          <div class="block sm:hidden hover:bg-gray-50 transition-colors duration-150">
            <.link
              phx-click="read_notification"
              phx-value-id={notification.id}
              phx-value-url={notification.page_url}
              phx-value-status="true"
              class="block p-4 text-sm text-gray-700"
              role="menuitem"
              tabindex="-1"
              id={"user-menu-item-mobile-#{notification.id}"}
            >
              <div class="flex items-start gap-3 mb-3">
                <!-- Notification Icon -->
                <div class="flex-shrink-0 mt-1">
                  <%= if String.contains?(String.downcase(notification.type || ""), "alert") do %>
                    <div class="p-2 bg-red-100 text-red-600 rounded-full">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </div>
                  <% else %>
                    <div class="p-2 bg-blue-100 text-blue-600 rounded-full">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                  <% end %>
                </div>

                <div class="flex-1">
                  <h3 class="font-semibold text-gray-900">
                    <%= notification.message %>
                  </h3>

                  <p class="text-xs text-gray-500 mt-1">
                    <%= Calendar.strftime(
                      NaiveDateTime.add(notification.inserted_at, 7200, :second),
                      "%d %B %Y %H:%M"
                    ) %>
                  </p>
                </div>
              </div>

              <div class="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
                <!-- Type -->
                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <%= notification.type %>
                </span>
                <!-- Status -->
                <%= if notification.status do %>
                  <div class="text-green-500 flex items-center gap-1 text-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>Seen</span>
                  </div>
                <% else %>
                  <div class="text-brand-10 flex items-center gap-1 text-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      class="h-4 w-4"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                      />
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                      />
                    </svg>
                    <span>View</span>
                  </div>
                <% end %>
              </div>
            </.link>
          </div>
        <% end %>
      </div>
    <% else %>
      <!-- Empty State -->
      <div class="flex flex-col items-center justify-center py-16 px-4">
        <div class="bg-gray-100 p-6 rounded-full mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.437L4 17h5m6 0a2 2 0 11-4 0h4z"
            />
          </svg>
        </div>

        <h2 class="text-xl text-gray-700 font-semibold mb-2">
          No Notifications
        </h2>

        <p class="text-gray-500 text-center max-w-sm">
          You're all caught up! We'll notify you when there's something new.
        </p>
      </div>
    <% end %>
    <!-- Pagination -->
    <div class="p-4 border-t border-gray-200 bg-gray-50">
      <.live_component
        module={PaginationComponent}
        id="PaginationComponentT4"
        params={@params}
        pagination_data={@user_notifications}
      />
    </div>
  </div>
</div>
