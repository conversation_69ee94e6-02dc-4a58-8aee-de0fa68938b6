defmodule App.Service.Table.DepartmentRoles do
  @moduledoc false

  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false
  alias App.{Roles, Repo}

  @pagination [page_size: 10]

  def index(params) do
    Roles.get_department_roles_query()
    |> compose_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def export(params) do
    Roles.get_department_roles_query()
    |> compose_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> Repo.all()
  end

  defp compose_query(query, search_params) do
    join(query, :left, [a], b in assoc(a, :created_by_user))
    |> join(:left, [a, b], c in assoc(a, :updated_by_user))
    |> handle_filter(search_params)
    |> compose_card_select()
  end

  defp handle_filter(query, nil), do: query

  defp handle_filter(query, params) do
    Enum.reduce(
      params,
      query,
      fn
        {"isearch", value}, query when byte_size(value) > 0 ->
          isearch_filter(query, sanitize_term(value))

        {"status", value}, query when byte_size(value) > 0 ->
          where(query, [a], a.status == ^value)

        {"name", value}, query when byte_size(value) > 0 ->
          where(query, [a], fragment("lower(?) LIKE lower(?)", a.name, ^value))

        {"start_date", value}, query when byte_size(value) > 0 ->
          where(
            query,
            [a],
            fragment(
              "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
              a.inserted_at,
              ^"#{value} 00:00:00"
            )
          )

        {"end_date", value}, query when byte_size(value) > 0 ->
          where(
            query,
            [a],
            fragment(
              "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
              a.inserted_at,
              ^"#{value} 23:59:59"
            )
          )

        {_, _}, query ->
          # Not a where parameter
          query
      end
    )
  end

  defp compose_card_select(query) do
    select(
      query,
      [a, b, c, d],
      %{
        inserted_at: a.inserted_at,
        name: a.name,
        description: a.description,
        created_by: b.email,
        permissions:
          fragment(
            "select count(id) from department_role_permissions where department_role_id=?",
            a.id
          ),
        updated_by: c.email,
        id: a.id,
        editable: a.editable,
        status: a.status,
        system_gen: a.system_gen,
        access_roles:
          fragment("select count(id) from access_roles where department_role_id = ?", a.id)
      }
    )
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      fragment("lower(?) LIKE lower(?)", a.name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.description, ^search_term) or
        fragment("lower(?) LIKE lower(?)", b.email, ^search_term) or
        fragment("lower(?) LIKE lower(?)", c.email, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
