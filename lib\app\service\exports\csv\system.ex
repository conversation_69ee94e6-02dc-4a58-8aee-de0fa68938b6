defmodule App.Service.Export.SystemCsv do
  @moduledoc false
  alias App.Service.Logs.SystemLogs

  @headers [
    "DATE",
    "USERNAME",
    "SERVICE",
    "ACTION",
    "IP ADDRESS",
    "DESCRIPTION"
  ]

  def system_service(payload) do
    SystemLogs.export(payload)
    |> Stream.map(
      &[
        &1.inserted_at,
        &1.user,
        &1.service,
        &1.action,
        &1.ip_address,
        &1.description
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["System Service"],
              ["", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
