defmodule App.Licenses.LicenseFields do
  use Ecto.Schema
  import Ecto.Changeset

  schema "license_fields" do
    field :field_name, :string

    field :field_type, :string
    field :field_label, :string
    field :reason, :string
    field :field_options, {:array, :string}
    field :field_dependents, {:array, :string}
    field :field_description, :string
    field :field_validations, :map
    field :dependent_selection, :string
    field :required, :boolean, default: true

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(license_fields, attrs) do
    license_fields
    |> cast(attrs, [
      :field_name,
      :reason,
      :field_type,
      :field_label,
      :field_options,
      :field_dependents,
      :field_description,
      :dependent_selection,
      :required,
      :field_validations
    ])
    |> validate_required([:field_name, :field_type, :field_label])
  end
end
