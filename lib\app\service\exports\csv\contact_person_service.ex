defmodule App.Service.Export.CsvContactPersonService do
  @moduledoc false
  alias App.Service.Table.Contact

  alias App.Service.Export.Functions

  @headers [
    "FIRST NAME",
    "LAST NAME",
    "MOBILE NUMBER",
    "EMAIL",
    "CLIENT",
    "CREATED AT",
    "LAST MODIFIED",
    "STATUS"
  ]

  def index(payload) do
    Contact.export(payload)
    |> Stream.map(
      &[
        &1.first_name,
        &1.last_name,
        &1.mobile,
        &1.email,
        &1.client_id,
        &1.inserted_at,
        &1.updated_at,
        Functions.table_numeric_status(&1.status)
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["CONTACT PERSON"],
              ["", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
