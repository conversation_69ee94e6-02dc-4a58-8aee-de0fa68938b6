defmodule App.Service.Export.CsvTransactionReportsService do
  @moduledoc false
  alias App.Service.Table.Transaction.ServiceTransactions
  alias App.Service.Export.Functions

  @headers [
    "TXN DATE",
    "REFERENCE",
    "AMOUNT",
    "NARRATION",
    "CLIENT",
    "USER",
    "STATUS"
  ]

  def index(assigns, payload) do
    ServiceTransactions.export(assigns, payload)
    |> Stream.map(
      &[
        NaiveDateTime.to_string(&1.txn_date),
        &1.reference,
        &1.txb_amount,
        &1.narration,
        &1.client,
        &1.transacting_user,
        &1.status
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Transaction Reports"],
              ["", "", "", ""],
              @headers,
              ["", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
