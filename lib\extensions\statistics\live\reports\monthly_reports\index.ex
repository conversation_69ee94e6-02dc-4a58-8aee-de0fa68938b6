defmodule AppWeb.Reports.MonthlyLive.Index do
  use AppWeb, :live_view

  alias App.Service.Table.PeriodicReports, as: TableQuery
  alias App.{Accounts, Clients}

  @impl true
  def mount(_params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("monthly_statistics-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Periodic Reports", "Accessed Monthly Reports Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      filter_data = %{
        "year" => nil,
        "month" => nil
      }

      months = [
        {"January", "01"},
        {"February", "02"},
        {"March", "03"},
        {"April", "04"},
        {"May", "05"},
        {"June", "06"},
        {"July", "07"},
        {"August", "08"},
        {"September", "09"},
        {"October", "10"},
        {"November", "11"},
        {"December", "12"}
      ]

      assign(socket, data: [])
      |> assign(show_filter: false)
      |> assign(data_loader: true)
      |> assign(period: [])
      |> assign(stats: [])
      |> assign(overall_total: [])
      |> assign(years: 2018..DateTime.utc_now().year)
      |> assign(months: months)
      |> assign(:clients, Accounts.get_clients!())
      |> assign(maker_checker: false)
      |> assign(form: filter_form())
      |> assign(live_socket_id: session["live_socket_id"])
      |> assign(showFilter: false)
      |> assign(form: useform(filter_data))
      |> LivePageControl.order_by_composer()
      |> LivePageControl.i_search_composer()
      |> ok()
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Periodic Reports", "Access Denied", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  defp useform(data) do
    to_form(
      data,
      as: "filter"
    )
  end

  defp filter_form(data \\ %{}), do: to_form(data, as: "filter")

  @impl true
  def handle_params(params, url, socket) do
    socket.endpoint.broadcast_from!(
      self(),
      "nav:" <> socket.assigns.live_socket_id,
      "change_nav",
      %{uri_path: URI.parse(url).path}
    )

    if connected?(socket), do: send(self(), {:get_list, params})

    socket
    |> assign(:params, params)
    |> apply_action(socket.assigns.live_action, params)
    |> noreply()
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "Periodic Reports")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {_record, _info} ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :live_action, :index)
        |> apply_action(:index, %{})
        |> noreply()
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, socket) do
    case target do
      "iSearch" ->
        send(self(), {:get_list, value})

        LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))
        |> noreply()

      "refresh_table" ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :data_loader, true)
        |> noreply()

      "filter" ->
        value = if socket.assigns.show_filter == true, do: false, else: true

        assign(socket, :show_filter, value)
        |> noreply()

      "filter_change" ->
        assign(socket, form: useform(value["filter"]))
        |> noreply()

      "filter" ->
        value = if socket.assigns.showFilter == true, do: false, else: true

        assign(socket, :showFilter, value)
        |> noreply()

      "reset_filter" ->
        socket
        |> assign(form: useform(%{}))
        |> noreply()

      "search" ->
        send(self(), {:get_list, value})

        noreply(
          socket
          |> assign(checked_ids: [])
          |> assign(param_list: [])
        )

      "live_select_change" ->
        handle_search(value, socket)

      "export" ->
        LiveFunctions.export_records(
          socket,
          value,
          "monthly_provider_report_service",
          Map.put(socket.assigns.params, "department", socket.assigns.role_department)
        )

        # _ ->
        {:noreply, socket}
    end
  end

  def handle_search(%{"text" => text, "id" => live_select_id}, socket) do
    clients = Clients.search(text)

    send_update(AppWeb.Component, id: live_select_id, options: clients)

    {:noreply, socket}
  end

  defp list(socket, params) do
    data = TableQuery.index(socket, LivePageControl.create_table_params(socket, params))

    color_map = %{
      "delivered" => "text-green-600",
      "failed" => "text-rose-600",
      "invalid" => "text-orange-600",
      "sent" => "text-blue-500"
    }

    stats =
      Enum.map(List.first(data.entries).sub_totals, fn {label, value} ->
        %{
          label: String.capitalize(label),
          value: value,
          text_color: Map.get(color_map, label, "text-gray-700")
        }
      end)

    {
      :noreply,
      assign(
        socket,
        :data,
        data
      )
      |> assign(data_loader: false)
      |> assign(params: params)
      |> assign(
        period:
          if(data != [], do: Timex.format!(List.first(data.entries).period, "{Mfull}, {YYYY}"))
      )
      |> assign(stats: stats)
      |> assign(overall_total: if(data != [], do: List.first(data.entries).overall_total))
    }
  end
end
