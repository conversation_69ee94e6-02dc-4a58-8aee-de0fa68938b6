defmodule AppWeb.Authenticated.Authentication.PermissionsLive.Access do
  @moduledoc false
  use AppWeb, :live_view
  alias App.Roles

  @impl true
  def mount(%{"role" => id}, _session, socket) do
    access_role = Roles.get_access_role!(id)

    available_permissions =
      Roles.get_available_permissions_for_access_role(id) |> group_permissions_by_service()

    current_permission_ids = Enum.map(access_role.permissions, & &1.id)

    {:ok,
     socket
     |> assign(:access_role, access_role)
     |> assign(:available_permissions, available_permissions)
     |> assign(:selected_permissions, MapSet.new(current_permission_ids))
     |> assign(:search_query, "")
     |> assign(:page_title, "Manage Permissions - #{access_role.name}")}
  end

  @impl true
  def handle_event("search", %{"value" => query}, socket) do
    assign(socket, :search_query, query)
    |> noreply()
  end

  @impl true
  def handle_event("toggle_permission", %{"permission_id" => permission_id}, socket) do
    permission_id = String.to_integer(permission_id)
    selected = socket.assigns.selected_permissions

    new_selected =
      if MapSet.member?(selected, permission_id) do
        MapSet.delete(selected, permission_id)
      else
        MapSet.put(selected, permission_id)
      end

    {:noreply, assign(socket, :selected_permissions, new_selected)}
  end

  @impl true
  def handle_event("save", _params, socket) do
    permission_ids = MapSet.to_list(socket.assigns.selected_permissions)
    access_role = socket.assigns.access_role

    case Roles.update_access_role_permissions(access_role, permission_ids) do
      %{} ->
        {:noreply,
         socket
         |> put_flash(:info, "Permissions updated successfully")
         |> push_navigate(to: ~p"/admin/access_roles")}

      {:error, _changeset} ->
        {:noreply, put_flash(socket, :error, "Error updating permissions")}
    end
  end

  defp filter_permissions(permissions, search_query) do
    query = String.downcase(search_query)

    permissions
    |> Enum.map(fn {service, perms} ->
      filtered_perms =
        Enum.filter(perms, fn perm ->
          String.contains?(String.downcase(perm.description), query) ||
            String.contains?(String.downcase(perm.service), query) ||
            String.contains?(String.downcase(perm.tab || ""), query)
        end)

      {service, filtered_perms}
    end)
    |> Enum.reject(fn {_, perms} -> Enum.empty?(perms) end)
  end

  defp group_permissions_by_service(permissions) do
    permissions
    |> Enum.group_by(& &1.service)
    |> Enum.sort_by(fn {service, _} -> service end)
  end
end
