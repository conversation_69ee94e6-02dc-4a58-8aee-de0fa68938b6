import Swal from 'sweetalert2';
import {sweet_alert} from "../functions/sweet_alert";

export const SweetAlert2 = {
    /**
     * @param {string} title - Alert title
     * @param {string} message - Alert message
     * @param {string} confirm_text - Confirmation button text
     * @param {string} action_type - Event action type
     * @param {Object} data - Data to be passed with the event
     */
    confirmAlert(title = '', message = '', confirm_text = 'Confirm', action_type, data = {}) {
        try {
            Swal.fire({
                title,
                text: message,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: confirm_text,
                allowOutsideClick: false
            }).then((result) => {
                if (result.isConfirmed && this.pushEvent) {
                    this.pushEvent(action_type, data);
                }
            }).catch(error => {
                console.error('SweetAlert2 error:', error);
                sweet_alert('error', 'An error occurred', 'Error');
            });
        } catch (error) {
            console.error('confirmAlert error:', error);
        }
    },

    mounted() {
        // Store hook reference
        window.sweetAlert2Hook = this;

        // Setup event handler
        this.handleEvent("sweetalert2", ({
            icon = 'success',
            message = '',
            title = '',
            expression = 'normal',
            timer = 3000
        }) => {
            try {
                sweet_alert(icon, message, title, expression, timer);
            } catch (error) {
                console.error('Sweet alert event error:', error);
            }
        });
    },

    destroyed() {
        // Clean up global reference
        if (window.sweetAlert2Hook === this) {
            window.sweetAlert2Hook = null;
        }

        // Close any open alerts
        if (Swal.isVisible()) {
            Swal.close();
        }
    }
}
