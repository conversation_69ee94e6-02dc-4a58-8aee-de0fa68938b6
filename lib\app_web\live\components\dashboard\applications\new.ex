defmodule AppWeb.Dashboard.Admin.NewApplications do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})

  alias App.Service.Dashboard.Stats

  def render(assigns) do
    ~H"""
    <Card.application_card
      title="New License Applications"
      count={@total}
      description="Applications awaiting review"
      navigate_to={~p"/license/applications/New"}
      gradient_from="from-amber-500"
      gradient_to="to-orange-600"
    >
      <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    </Card.application_card>
    """
  end

  def mount(_params, _session, socket) do
    {:ok, assign(socket, :total, Stats.new(socket))}
  end
end
