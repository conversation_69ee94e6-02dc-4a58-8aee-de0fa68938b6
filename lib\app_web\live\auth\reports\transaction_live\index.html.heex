<div class="px-4 sm:px-6 lg:px-8 mt-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">Transaction Reports</h1>
      <p class="mt-2 text-sm text-gray-700"></p>
    </div>
  </div>
  <!-- FILTERS -->
  <%= if @showFilter do %>
    <div class="border overflow-hidden animate-slide-in-from-top transition-all ease-in-out duration-500 rounded-lg bg-white my-4 flex flex-col gap-4 items-start">
      <h1 class="px-4 py-2 bg-gray-50 border-b font-medium w-full">Filter Transaction</h1>

      <FormJ.filter_form
        for={@form}
        class="w-full px-4"
        id="filter"
        phx-change="filter_change"
        phx-submit="search"
      >
        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 mb-4">
          <%!-- <.live_select
            field={@form[:client_id]}
            label="Client"
            placeholder="Search for a client"
            options={@get_client}
            update_min_len={3} /> --%>
          <.input
            field={@form[:client_name]}
            type="text"
            label="Client"
            placeholder="Enter Client Name"
          />
          <.input
            field={@form[:reference]}
            type="text"
            label="Reference"
            placeholder="Enter Ref. Number"
          />
          <.input
            field={@form[:status]}
            type="select"
            label="Status"
            prompt="--Select Status--"
            options={[{"All", ""}, {"COMPLETE", "S"}, {"PENDING", "PENDING"}, {"FAILED", "F"}]}
          />
        </div>

        <p class="text-gray-500 font-medium">Date Filters</p>

        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 pt-2">
          <FormJ.input_filter field={@form[:start_date]} type="date" label="From" />
          <FormJ.input_filter field={@form[:end_date]} type="date" label="To" />
        </div>

        <div class="flex gap-4 items-center justify-start mt-4 p-4 w-full border-t font-medium">
          <.button
            type="button"
            phx-click="reset_filter"
            class="cursor-pointer hover:text-brand-1 py-2"
          >
            Reset
          </.button>

          <.button type="submit" class="cursor-pointer hover:text-brand-1 py-2">Search</.button>
        </div>
      </FormJ.filter_form>
    </div>
  <% end %>
  <!-- END OF FILTERS -->
  <div class="mt-8 flow-root">
    <div class="-mx-4 -my-2 sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <Table.main_table id="session" rows={@data} params={@params} data_loader={@data_loader}>
          <:col :let={rj} label={table_link(@params, "Txn Date", :txn_date)}>
            <%= Calendar.strftime(
              NaiveDateTime.add(rj.txn_date, 7200, :second),
              "%d %B %Y %H:%M:%S"
            ) %>
          </:col>

          <:col :let={rj} label={table_link(@params, "Reference", :reference)}>
            <%= rj.reference %>
          </:col>

          <:col :let={rj} label={table_link(@params, "Amount", :txb_amount)}>
            <%= NumberF.currency(rj.txb_amount || 0, "") %>
          </:col>

          <:col :let={rj} label={table_link(@params, "Narration", :narration)}>
            <%= rj.narration %>
          </:col>

          <:col :let={rj} label={table_link(@params, "Client", :client)}>
            <%= rj.client %>
          </:col>

          <:col :let={rj} label={table_link(@params, "User", :transacting_user)}>
            <%= rj.transacting_user %>
          </:col>

          <:col :let={rj} label={table_link(@params, "Status", :status)}>
            <Table.table_string_status status={rj.status} />
          </:col>

          <:action :let={rj}>
            <%= if rj.status == "S" do %>
              <Option.bordered>
                <.link
                  phx-click="view_receipt"
                  phx-value-reference={rj.reference}
                  class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-brand-1 hover:text-gray-900"
                >
                  View Receipt
                </.link>
              </Option.bordered>
            <% else %>
              <div class="text-center text-rose-500">No Actions</div>
            <% end %>
          </:action>
        </Table.main_table>
      </div>
    </div>
  </div>
</div>

<Model.fullscreen
  :if={@live_action in [:view_receipt]}
  id="view_receipt-modal"
  show
  return_to="close_model"
>
  <iframe
    src={~p"/invoice/bundle/#{@reference}"}
    id="receipt_iframe"
    title="transaction"
    style="width: 100%;"
    height="700"
    name="Transaction"
  >
  </iframe>
</Model.fullscreen>
