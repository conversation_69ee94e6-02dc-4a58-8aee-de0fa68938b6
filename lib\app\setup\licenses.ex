defmodule App.SetUp.Licenses do
  @moduledoc false
  alias App.{Licenses, Repo}
  alias App.Licenses.{LicenseData, LicenseCategoriesData}

  defp run_seeds_db(schema), do: Repo.insert!(schema)

  def init(
        {form_1, form_2, form_4, form_6, form_8, form_9, form_10, form_11, form_12, sandbox,
         category_3, category_5}
      ) do
    [
      %{
        name: "company_name",
        label: "Name of company",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the registered name of the company."
      },
      %{
        name: "incorporation_date",
        label: "Date of incorporation",
        type: "date",
        options: nil,
        dependents: nil,
        required: true,
        description: "Select the date on which the company was incorporated."
      },
      %{
        name: "registered_office",
        label: "Registered office",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description: "Provide the full address of the registered office."
      },
      %{
        name: "proposed_exchange_name",
        label: "Proposed name of securities exchange",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the proposed name for the securities exchange."
      },
      %{
        name: "hours_of_business",
        label: "Hours of business",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "State the operational hours."
      },
      %{
        name: "number_of_members",
        label: "Number of members",
        required: true,
        type: "number",
        validations: %{"min" => "1", "max" => "10000"},
        dependents: nil,
        description: "Enter the total number of members."
      },
      %{
        name: "competing_members_count",
        label:
          "Number of members who will carry on business of dealing in securities independently of and in competition with each other",
        type: "number",
        required: true,
        validations: %{"min" => "1", "max" => "10000"},
        dependents: nil,
        description: "Enter the number of members that will compete independently."
      },
      %{
        name: "signature_upload",
        label: "Signature",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Upload a scanned copy of the signature."
      },
      %{
        name: "signature_name",
        label: "Signature Name",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the full name of the chairman."
      },
      %{
        name: "balance_sheet",
        label: "Balance Sheet",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: ""
      },
      %{
        name: "rules_proposed",
        label: "Rules of Proposed Securities Exchange",
        type: "upload",
        options: nil,
        required: true,
        dependents: nil,
        description: ""
      },
      %{
        name: "incorporation_certificate",
        label: "Certificate of Incorporation",
        type: "upload",
        options: nil,
        required: true,
        dependents: nil,
        description: ""
      },
      %{
        name: "memorandum",
        label: "Memorandum",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: ""
      },
      %{
        name: "articles_of_assoc",
        label: "Articles of Association",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: ""
      },
      %{
        name: "pop_upload",
        label: "Proof of Payment",
        type: "upload",
        options: nil,
        required: true,
        dependents: nil,
        description: "Upload a Proof of Payment(POP) Document."
      },
      %{
        name: "other_upload",
        label: "Other",
        type: "upload",
        options: nil,
        required: true,
        dependents: nil,
        required: false,
        description: "Upload any Other Relevant Document."
      }
    ]
    |> update_license_fields()
    |> Enum.map(& &1.id)
    |> update_license_data(form_1.id)
    |> update_categories_data(category_3.id)

    IO.inspect("FORM 2")

    [
      %{
        name: "applicant_name",
        label: "Name of applicant",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the full name of the applicant."
      },
      %{
        name: "representative_number",
        label: "Number of Representatives",
        type: "number",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the number of representatives."
      },
      %{
        name: "registered_office",
        label: "Registered office",
        type: "textarea",
        options: nil,
        required: true,
        dependents: nil,
        description: "Provide the registered office address."
      },
      %{
        name: "place_of_incorporation",
        label: "Place of incorporation",
        type: "select",
        required: true,
        options: App.Setup.Cities.city_options(),
        dependents: nil,
        description: "Enter the place where the company is incorporated."
      },
      %{
        name: "principal_business_address",
        label: "Full address of principal business place",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter address of the principal business location."
      },
      %{
        name: "principal_business_telephone",
        label: "Telephone number of principal business place",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter Telephone number of the principal business location."
      },
      %{
        name: "authorised_capital",
        label: "Authorised and paid-up capital",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the capital details, including types of shares issued."
      },
      %{
        name: "shareholders_details",
        label: "Shareholders details",
        type: "upload",
        options: nil,
        dependents: nil,
        required: true,
        description:
          "Provide details of each shareholder (name, address, shares held, acquisition date) Note: (Owns > 15%)."
      },
      %{
        name: "record_office_address",
        label: "Address for records under section twenty-eight",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the address where the records will be kept."
      },
      %{
        name: "directors_annexure",
        label: "Directors and Secretary details (Annexure)",
        type: "upload",
        options: nil,
        required: true,
        dependents: nil,
        description: "Upload an annexure listing each director and secretary with full details."
      },
      %{
        name: "principal_business_nature",
        label: "Nature of the principal business",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Describe the principal business activities."
      },
      %{
        name: "proposed_activity_description",
        label: "Description of proposed business activity",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Provide details on the activity and manner of conducting the business."
      },
      %{
        name: "client_types",
        label: "Type of clients",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Describe the types of clients with whom business will be conducted."
      },
      %{
        name: "internal_control_description",
        label: "Organisation structure and internal control procedures",
        type: "upload",
        options: nil,
        required: true,
        dependents: nil,
        description: "Describe the proposed internal control procedures."
      },
      # %{
      #   name: "internal_control_description",
      #   label: "Organisation structure and internal control procedures",
      #   type: "textarea",
      #   options: nil,
      #   required: true,
      #   dependents: nil,
      #   description: "Describe the proposed internal control procedures."
      # },
      %{
        name: "controlling_persons",
        label: "Persons with controlling influence (other than directors)",
        type: "upload",
        options: nil,
        required: true,
        dependents: nil,
        description: "List names and addresses of persons with controlling influence."
      },
      %{
        name: "director_other_corp",
        label: "Director/Secretary holding positions in other corporations?",
        type: "select",
        options: ["Yes", "No"],
        dependents: nil,
        required: true,
        description:
          "Select Yes if any director/secretary is also director in another corporation and provide details."
      },
      %{
        name: "director_other_corp_upload",
        label: "Director/Secretary holding positions in other corporations?",
        type: "upload",
        options: nil,
        required: true,
        dependents: ["director_other_corp"],
        dependent_selection: "Yes",
        description:
          "Select Yes if any director/secretary is also director in another corporation and provide details."
      },
      %{
        name: "licensed_dealing_securities",
        label: "Licensed for Dealing in Securities?",
        type: "select",
        required: true,
        options: ["Yes", "No"],
        dependents: ["annexure_licensed_dealing_securities"],
        description:
          "Has the applicant or any director/secretary been licensed or registered under any law requiring it for dealing in securities in the past 10 years?"
      },
      %{
        name: "annexure_licensed_dealing_securities",
        label: "Annexure: Securities Licensing Details",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["licensed_dealing_securities"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of the securities licensing or registration."
      },
      %{
        name: "licensed_trade_business",
        label: "Licensed for Any Trade, Business, or Profession?",
        type: "select",
        required: true,
        options: ["Yes", "No"],
        dependents: ["annexure_licensed_trade_business"],
        description:
          "Has the applicant or any director or secretary of the applicant been licensed by law to carry on any trade, business, or profession in any place in the past 10 years?"
      },
      %{
        name: "annexure_licensed_trade_business",
        label: "Annexure: Trade/Business Licensing Details",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["licensed_trade_business"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of the trade, business, or profession license."
      },
      %{
        name: "refused_trade_rights",
        label: "Refused or Restricted Trade Rights?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: ["annexure_refused_trade_rights"],
        description:
          "Has the company or any director been refused the right to trade in any business?"
      },
      %{
        name: "annexure_refused_trade_rights",
        label: "Annexure: Trade Right Restrictions",
        type: "upload",
        options: nil,
        required: true,
        dependents: ["refused_trade_rights"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of restrictions or refusals."
      },
      %{
        name: "member_of_securities_exchange",
        label: "Member of Securities Exchange?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: ["annexure_member_of_securities_exchange"],
        description:
          "Has any director or secretary of the applicant been a member or partner in a member firm of any securities exchange in the past 10 years?"
      },
      %{
        name: "annexure_member_of_securities_exchange",
        label: "Annexure: Membership Details",
        type: "upload",
        options: nil,
        required: true,
        dependents: ["member_of_securities_exchange"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of the membership."
      },
      %{
        name: "disciplined_by_exchange",
        label: "Disciplined by Securities Exchange?",
        type: "select",
        required: true,
        options: ["Yes", "No"],
        dependents: ["annexure_disciplined_by_exchange"],
        description:
          "Has any director or secretary of the applicant been suspended from membership of any securities exchange or otherwise disciplined?"
      },
      %{
        name: "annexure_disciplined_by_exchange",
        label: "Annexure: Disciplinary Details",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["disciplined_by_exchange"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of the suspension or disciplinary action."
      },
      %{
        name: "refused_membership_exchange",
        label: "Refused Membership of Securities Exchange?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: ["annexure_refused_membership_exchange"],
        description:
          "Has any director or secretary of the applicant been refused membership of any securities exchange?"
      },
      %{
        name: "annexure_refused_membership_exchange",
        label: "Annexure: Refusal Details",
        type: "upload",
        options: nil,
        required: true,
        dependents: ["refused_membership_exchange"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of the refusal."
      },
      %{
        name: "known_by_other_name",
        label: "Known by Other Name?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: ["annexure_known_by_other_name"],
        description:
          "Has any director or secretary of the applicant been known by any name other than those shown in this application?"
      },
      %{
        name: "annexure_known_by_other_name",
        label: "Annexure: Other Names Used",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["known_by_other_name"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of other names used."
      },
      %{
        name: "convicted_offence",
        label: "Convicted of an Offence?",
        type: "select",
        required: true,
        options: ["Yes", "No"],
        dependents: ["annexure_conviction"],
        description:
          "Has any director or secretary of the applicant been convicted of any offence (other than a traffic offence) in Zambia or elsewhere, or are there pending proceedings that may lead to such a conviction?"
      },
      %{
        name: "annexure_conviction",
        label: "Annexure: Conviction Details",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["convicted_offence"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of the conviction or pending proceedings."
      },
      %{
        name: "fraud_judgement",
        label: "Judgement Related to Fraud, Misrepresentation or Dishonesty?",
        type: "select",
        required: true,
        options: ["Yes", "No"],
        dependents: ["annexure_fraud_judgement"],
        description:
          "Has any director or secretary of the applicant had a judgment related to fraud, misrepresentation, or dishonesty in any civil proceedings?"
      },
      %{
        name: "annexure_fraud_judgement",
        label: "Annexure: Fraud/Misrepresentation Judgement",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["fraud_judgement"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details, including whether the judgment is unsatisfied."
      },
      %{
        name: "declared_bankrupt",
        label: "Declared Bankrupt?",
        type: "select",
        required: true,
        options: ["Yes", "No"],
        dependents: ["annexure_declared_bankrupt"],
        description:
          "Has any director or secretary of the applicant been declared bankrupt, compounded with, or made an arrangement for the benefit of creditors?"
      },
      %{
        name: "annexure_declared_bankrupt",
        label: "Annexure: Bankruptcy Details",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["declared_bankrupt"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of the bankruptcy or arrangement."
      },
      %{
        name: "managed_other_corporation",
        label: "Managed Other Corporation?",
        type: "select",
        required: true,
        options: ["Yes", "No"],
        dependents: ["annexure_managed_other_corporation"],
        description:
          "Has any director or secretary of the applicant been engaged in the management of any corporation other than those referred to in question 5?"
      },
      %{
        name: "annexure_managed_other_corporation",
        label: "Annexure: Other Corporation Management",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["managed_other_corporation"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of the corporation(s) managed."
      },
      %{
        name: "refused_fidelity_bond",
        label: "Refused Fidelity or Surety Bond?",
        type: "select",
        required: true,
        options: ["Yes", "No"],
        dependents: ["annexure_refused_fidelity_bond"],
        description:
          "Has any director or secretary of the applicant been refused a fidelity or surety bond in Zambia or elsewhere?"
      },
      %{
        name: "annexure_refused_fidelity_bond",
        label: "Annexure: Fidelity/Surety Bond Refusal",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["refused_fidelity_bond"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of the refusal."
      },
      %{
        name: "disqualified_director",
        label: "Disqualified as Director?",
        type: "select",
        required: true,
        options: ["Yes", "No"],
        dependents: ["annexure_disqualified_director"],
        description:
          "Has any director or secretary of the applicant been disqualified as a director or been a director of a company that has gone into receivership or liquidation?"
      },
      %{
        name: "annexure_disqualified_director",
        label: "Annexure: Director Disqualification/Liquidation",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["disqualified_director"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of the disqualification or company liquidation."
      },
      %{
        name: "audited_financials",
        label: "Last Audited Financial Statements",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description:
          "Upload the last audited balance sheet and profit and loss account or submit management accounts if finiancial statements are unavailable."
      },
      %{
        name: "incorporation_certificate",
        label: "Certificate of Incorporation",
        type: "upload",
        options: nil,
        required: true,
        dependents: nil,
        description:
          "Certified copy of certificate of incorporation or certificate of registration"
      },
      %{
        name: "share_capital",
        label: "Share Capital",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Certified copy of share capital"
      },
      %{
        name: "articles_of_assoc",
        label: "Articles of Association",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Certified copy of articles of association"
      },
      %{
        name: "business_address",
        label: "Proof of Business Address",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Proof of business address"
      },
      %{
        name: "business_plan",
        label: "Company’s Business Plan",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Company’s business plan"
      },
      %{
        name: "shareholder_upload",
        label: "Shareholders (PACRA print out)",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Shareholders (PACRA print out) Note: (Owns > 15%)."
      },
      %{
        name: "source_of_funds",
        label: "Sources of Funds",
        type: "select",
        required: true,
        options: ["Salary", "Commission", "Both", "Other"],
        dependents: nil,
        description: "Select Your Sources of funds"
      },
      %{
        name: "auditor_letter",
        label: "Letter from Auditors",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description:
          "Letter from auditors confirming that the applicant has appointed them to audit its books"
      },
      %{
        name: "capital_markets",
        label: "Proof of Capital Markets Association in Zambia (CMAZ)",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Proof of having joined a Capital Markets Association in Zambia"
      },
      %{
        name: "directors_id",
        label: "Identity Cards of Directors",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description:
          "Certified copies of directors identity card (NRC or Passport or Driver’s License)"
      },
      %{
        name: "directors_address",
        label: "Directors/Shareholders Residential Addresses",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Residential addresses for the directors/shareholders"
      },
      %{
        name: "curriculum_vitae_upload",
        label: "Curriculum vitae for the directors/shareholders",
        type: "upload",
        options: nil,
        required: true,
        dependents: nil,
        description: "Curriculum vitae for the directors/shareholders"
      },
      %{
        name: "referees_upload",
        label: "Referees for each director",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description:
          "Two names of referees and their current addresses for each director. These should provide appropriate reference letters addressed to the Commission"
      },
      %{
        name: "signature_upload",
        label: "Supervisor Consent",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Upload a supervisor/manager filled form that will show consent given."
      },
      %{
        name: "sign_off_upload",
        label: "Director's Sign-off",
        type: "upload",
        options: nil,
        required: true,
        dependents: nil,
        description: "Upload an official director's sign-off document."
      },
      %{
        name: "signature_name",
        label: "Signature Name",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the full name of the chairman."
      },
      %{
        name: "pop_upload",
        label: "Proof of Payment",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Upload a Proof of Payment(POP) Document."
      },
      %{
        name: "other_upload",
        label: "Other",
        type: "upload",
        options: nil,
        dependents: nil,
        required: false,
        description: "Upload any Other Relevant Document."
      }
    ]
    |> update_license_fields()
    |> Enum.map(& &1.id)
    |> update_license_data(form_2.id)
    |> update_categories_data(category_3.id)

    IO.inspect("FORM 3")

    [
      %{
        name: "applicant_name",
        label: "Name of applicant",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the full name of the applicant."
      },
      %{
        name: "principal_business_address",
        label: "Principal business address and telephone number",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description: "Provide full address and telephone details of the business location."
      },
      %{
        name: "national_id",
        label: "National Registration Card ID number",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the national ID number."
      },
      %{
        name: "residential_address",
        label: "Residential address",
        type: "textarea",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the applicant's home address."
      },
      %{
        name: "record_office_address",
        label: "Address for records under section twenty-eight",
        type: "textarea",
        required: true,
        options: nil,
        dependents: nil,
        description: "Enter the address where records will be maintained."
      },
      %{
        name: "investment_area",
        label: "Area of proposed business (Investment Adviser Licence)",
        type: "select",
        options: [
          "Advising others concerning securities",
          "Issuing/promulgating analyses or reports",
          "Managing a portfolio of securities for investment"
        ],
        dependents: nil,
        required: true,
        description: "Select one or more areas for the investment adviser licence."
      },
      %{
        name: "investment_business_description",
        label: "Details on business conduct and experience",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description:
          "Describe in detail how the business will be conducted and the experience in that area."
      },
      %{
        name: "organization_structure",
        label: "Organisation structure and internal control procedures",
        type: "textarea",
        options: nil,
        required: true,
        dependents: nil,
        description: "Describe the organisation structure and internal controls."
      },
      %{
        name: "customer_types",
        label: "Type of customers",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description: "Describe the type of customers proposed."
      },
      %{
        name: "principal_business_declaration",
        label: "Is investment advisory the principal business?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: ["principal_business_details"],
        description: "Select Yes/No and if No, provide details of the principal business."
      },
      %{
        name: "share_interest",
        required: true,
        label: "Interest in shares of any quoted company (>=5%)",
        type: "select",
        options: ["Yes", "No"],
        dependents: ["share_interest_details"],
        description: "Select Yes/No. If Yes, provide details of the share interest."
      },
      %{
        name: "employment_history",
        label: "Employment and business activities (last 10 years)",
        type: "textarea",
        required: true,
        options: nil,
        dependents: nil,
        description:
          "Provide details of employment and business activities over the past 10 years."
      },
      %{
        name: "referee_details",
        required: true,
        label: "Details of two character referees",
        type: "textarea",
        options: nil,
        dependents: nil,
        description: "List names, addresses, occupations and relationship of character referees."
      },
      %{
        name: "additional_information",
        label: "Additional information / qualifications",
        type: "textarea",
        required: true,
        options: nil,
        dependents: nil,
        description: "Provide any additional relevant information or qualifications."
      },
      %{
        name: "declaration_date",
        label: "Declaration Date",
        required: true,
        type: "date",
        options: nil,
        dependents: nil,
        description: "Select the date of declaration."
      },
      %{
        name: "signature_upload",
        label: "Signature",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Upload the applicant's signature."
      },
      %{
        name: "curriculum_upload",
        label: "Curriculum Vitae",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description:
          "Upload your Curriculum Vitae. Please include names of three referees and their current address (Not politically connected persons or family member or workmates. Please see SEC Guidelines)"
      },
      %{
        name: "reference_upload",
        label: "Reference Letters",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description:
          "Upload Two reference letters. (Please collect/ Standard letter for each from the SEC at point of collecting/ downloading application form)"
      },
      %{
        name: "certificate_upload",
        label: "Certificates",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Upload Certified Copies of Certificate"
      },
      %{
        name: "proof_of_residence_upload",
        label: "Proof of Residential Address",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description:
          "Upload Proof of residential address (e. g original or certified copy of utility bill, council land rate, lease agreement, or letter from employer, etc)"
      },
      %{
        name: "id_upload",
        label: "Identity Card",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Upload Certified copy of identity card"
      },
      %{
        name: "is_zambian",
        label: "Is the Applicant Zambian?",
        type: "select",
        required: true,
        options: ["Yes", "No"],
        dependents: nil,
        description: "Is the applicant Zambian?"
      },
      %{
        name: "work_permit_upload",
        label: "Work Permit",
        required: true,
        type: "upload",
        options: nil,
        dependents: ["is_zambian"],
        dependent_selection: "No",
        description: "If 'No', attach Certified copy of work permit if not Zambian."
      },
      %{
        name: "cmaz_upload",
        label: "CMAZ Membership",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Upload CMAZ Membership"
      },
      %{
        name: "sec_course_upload",
        label: "SEC Approved Course",
        required: true,
        type: "upload",
        options: nil,
        dependents: nil,
        description: "Upload Proof of having completed SEC approved course"
      },
      %{
        name: "asset_liabity_upload",
        label: "Statement of Assets and Liabilities",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Upload Latest statement of assets and liabilities (signed by owner)"
      },
      %{
        name: "pop_upload",
        label: "Proof of Payment",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Upload a Proof of Payment(POP) Document."
      },
      %{
        name: "other_upload",
        label: "Other",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        required: false,
        description: "Upload any Other Relevant Document."
      }
    ]

    # |> update_license_fields()
    # |> Enum.map(& &1.id)
    # |> update_license_data(form_3.id)
    # |> update_categories_data(category_3.id)

    IO.inspect("FORM 4")

    [
      # General Information
      %{
        name: "applicant_name",
        label: "Name of Applicant",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the name of the applicant company."
      },
      %{
        name: "registered_office",
        label: "Registered Office",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the registered office address of the applicant."
      },
      %{
        name: "place_of_incorporation",
        label: "Place of Incorporation",
        type: "select",
        required: true,
        options: App.Setup.Cities.city_options(),
        dependents: nil,
        description: "Enter the place where the applicant was incorporated."
      },
      %{
        name: "principal_address",
        label: "Principal Business Address",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the full address where the applicant's business is carried out."
      },
      %{
        name: "principal_telephone",
        label: "Telephone Number",
        required: true,
        type: "text",
        options: nil,
        dependents: nil,
        description: "Enter the main telephone number for the business."
      },
      %{
        name: "capital_details",
        label: "Authorized and Paid-Up Capital",
        required: true,
        type: "textarea",
        options: nil,
        dependents: nil,
        description:
          "Provide details of the authorized and paid-up capital, including types of shares issued."
      },
      %{
        name: "shareholder_details",
        label: "Shareholder Details",
        required: true,
        type: "textarea",
        options: nil,
        dependents: nil,
        description:
          "Provide the name, address, amount of shares held, and acquisition date for each shareholder."
      },
      %{
        name: "record_address",
        label: "Record-Keeping Address",
        required: true,
        type: "text",
        options: nil,
        dependents: nil,
        description: "Enter the address where records under Section 28 of the Act will be kept."
      },

      # Business Information
      %{
        name: "nature_of_business",
        label: "Nature of Business",
        required: true,
        type: "textarea",
        options: nil,
        dependents: nil,
        description: "Describe the primary business activities of the applicant."
      },
      %{
        name: "proposed_business",
        label: "Proposed Business Activities",
        required: true,
        type: "select",
        options: [
          "Advising others concerning securities",
          "Issuing or promulgating analyses or reports concerning securities",
          "Managing investment portfolios on behalf of customers"
        ],
        dependents: nil,
        description:
          "Select the areas of business for which the Investment Adviser's licence is sought."
      },
      %{
        name: "business_conduct_details",
        label: "Business Conduct and Experience",
        required: true,
        type: "textarea",
        options: nil,
        dependents: nil,
        description:
          "Describe how the business will be conducted and provide details on the experience of the management team."
      },
      %{
        name: "customer_types",
        required: true,
        label: "Type of Customers",
        type: "textarea",
        options: nil,
        dependents: nil,
        description: "Describe the types of customers the applicant intends to serve."
      },
      %{
        name: "organization_structure",
        required: true,
        label: "Organization Structure and Internal Controls",
        type: "textarea",
        options: nil,
        dependents: nil,
        description:
          "Provide details on the organization structure and internal control procedures."
      },

      # Management and Directorship
      %{
        name: "controlling_influence",
        required: true,
        label: "Controlling Influence",
        type: "textarea",
        options: nil,
        dependents: nil,
        description:
          "List individuals (other than directors) who have a controlling influence over management and policies."
      },
      %{
        name: "director_other_corporations",
        label: "Directorship in Other Corporations?",
        required: true,
        type: "select",
        options: ["Yes", "No"],
        dependents: nil,
        description: "Does any director or secretary hold directorships in other companies?"
      },

      # Yes/No Questions with Attachments
      %{
        name: "licensed_trade_securities",
        label: "Previously Licensed for Securities Trading?",
        required: true,
        type: "select",
        options: ["Yes", "No"],
        dependents: nil,
        description:
          "Has the company or its directors been previously licensed to trade in securities?"
      },
      %{
        name: "licensed_other_business",
        required: true,
        label: "Licensed for Other Business?",
        type: "select",
        options: ["Yes", "No"],
        dependents: nil,
        description:
          "Has the company or its directors been licensed for any other trade or business?"
      },
      %{
        name: "refused_trade_rights",
        required: true,
        label: "Refused Trade Rights?",
        type: "select",
        options: ["Yes", "No"],
        dependents: nil,
        description:
          "Has the company or any director been refused the right to trade in any business?"
      },
      %{
        name: "conviction_details",
        label: "Convicted of an Offense?",
        required: true,
        type: "select",
        options: ["Yes", "No"],
        dependents: nil,
        description: "Has any director or secretary been convicted of a criminal offense?"
      },
      %{
        name: "fraud_judgment",
        label: "Judgment for Fraud or Misrepresentation?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: nil,
        description:
          "Has any director or secretary been found guilty of fraud, misrepresentation, or dishonesty?"
      },
      %{
        name: "bankruptcy_details",
        label: "Declared Bankrupt?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: nil,
        description:
          "Has any director or secretary been declared bankrupt or made arrangements with creditors?"
      },
      %{
        name: "employment_history",
        label: "Employment History of Directors and Secretary",
        required: true,
        type: "textarea",
        options: nil,
        dependents: nil,
        description:
          "Provide details of employment and business activities over the past 10 years."
      },
      %{
        name: "character_references",
        label: "Character References for Directors and Secretary",
        required: true,
        type: "textarea",
        options: nil,
        dependents: nil,
        description: "Provide two character references for each director and the secretary."
      },
      %{
        name: "additional_information_select",
        label: "Additional Information?",
        required: true,
        type: "select",
        options: ["Yes", "No"],
        dependents: nil,
        description:
          "If 'Yes', provide any other relevant information that may assist in the application."
      },

      # Upload Fields

      %{
        name: "additional_documents_upload",
        label: "Additional Information/Documents",
        required: true,
        type: "upload",
        options: nil,
        dependents: ["additional_information_select"],
        dependent_selection: "Yes",
        description:
          "Upload any additional documents relevant to the application, including qualifications and training certificates."
      },
      %{
        name: "audited_financials",
        required: true,
        label: "Last Audited Financial Statements",
        type: "upload",
        options: nil,
        dependents: nil,
        description:
          "Upload the last audited balance sheet and profit and loss account or submit management accounts if finiancial statements unavailable."
      },
      %{
        name: "directors_details",
        label: "Directors and Secretary Details",
        required: true,
        type: "upload",
        options: nil,
        dependents: nil,
        description:
          "Attach details of each director and secretary, including full name, residential address, date of birth, office held, and date of appointment."
      },
      %{
        name: "director_other_corporations_upload",
        label: "Directorship in Other Corporations",
        required: true,
        type: "upload",
        options: nil,
        dependents: ["director_other_corporations"],
        dependent_selection: "Yes",
        description:
          "If 'Yes', attach details of corporations where any director or secretary holds a directorship, including names, places of incorporation, and dates of appointment."
      },
      %{
        name: "licensed_trade_securities_upload",
        label: "Licensed to Trade in Securities",
        required: true,
        type: "upload",
        options: nil,
        dependents: ["licensed_trade_securities"],
        required: true,
        dependent_selection: "Yes",
        description:
          "If 'Yes', attach details of previous licenses or registrations related to dealing in securities or investment advisory."
      },
      %{
        name: "licensed_other_business_upload",
        label: "Licensed for Other Business",
        type: "upload",
        required: true,
        required: true,
        options: nil,
        dependents: ["licensed_other_business"],
        dependent_selection: "Yes",
        description:
          "If 'Yes', attach details of other business or professional licenses obtained."
      },
      %{
        name: "refused_trade_rights_upload",
        label: "Refused Trade Rights",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["refused_trade_rights"],
        dependent_selection: "Yes",
        description:
          "If 'Yes', attach details regarding the refusal or restriction to carry on a trade or business."
      },
      %{
        name: "membership_securities_exchange_upload",
        label: "Membership in Securities Exchange",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["membership_securities_exchange"],
        dependent_selection: "Yes",
        description:
          "If 'Yes', attach details of memberships in securities exchanges, including past and present roles."
      },
      %{
        name: "suspended_exchange_upload",
        label: "Suspended or Disciplined by Securities Exchange",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["suspended_exchange"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details regarding the suspension or disciplinary actions."
      },
      %{
        name: "conviction_details_upload",
        label: "Conviction Details",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["conviction_details"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of convictions or pending proceedings."
      },
      %{
        name: "fraud_judgment_upload",
        label: "Judgment for Fraud or Misrepresentation",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["fraud_judgment"],
        dependent_selection: "Yes",
        description:
          "If 'Yes', attach details of judgments related to fraud, misrepresentation, or dishonesty."
      },
      %{
        name: "bankruptcy_details_upload",
        label: "Bankruptcy or Financial Settlements",
        required: true,
        type: "upload",
        options: nil,
        dependents: ["bankruptcy_details"],
        dependent_selection: "Yes",
        description:
          "If 'Yes', attach details of bankruptcy declarations or financial settlements."
      },
      %{
        name: "management_experience_upload",
        required: true,
        label: "Management Experience",
        type: "upload",
        options: nil,
        dependents: ["management_experience"],
        dependent_selection: "Yes",
        description:
          "If 'Yes', attach details of management experience outside the companies listed in the application."
      },
      %{
        name: "fidelity_bond_refusal_upload",
        required: true,
        label: "Refusal of Fidelity or Surety Bond",
        type: "upload",
        options: nil,
        dependents: ["fidelity_bond_refusal"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of any fidelity or surety bond refusals."
      },
      %{
        name: "director_disqualification_upload",
        label: "Disqualified as a Director",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["director_disqualification"],
        dependent_selection: "Yes",
        description:
          "If 'Yes', attach details of director disqualification or liquidation of companies where the director was involved."
      },
      %{
        name: "employment_history_upload",
        label: "Employment History of Directors and Secretary",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["employment_history"],
        dependent_selection: "Yes",
        description:
          "Attach a document listing employment and business activities for each director and secretary over the past 10 years."
      },
      %{
        name: "character_references_upload",
        label: "Character References for Directors and Secretary",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["character_references"],
        dependent_selection: "Yes",
        description:
          "Attach reference letters from two individuals attesting to the character and reputation of each director and the secretary."
      },
      %{
        name: "additional_information_upload",
        label: "Additional Information",
        type: "upload",
        options: nil,
        required: true,
        dependents: ["additional_information"],
        dependent_selection: "Yes",
        description:
          "Attach any additional documents relevant to the application, including qualifications and training certificates."
      },
      %{
        name: "pop_upload",
        label: "Proof of Payment",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Upload a Proof of Payment(POP) Document."
      }
    ]
    |> update_license_fields()
    |> Enum.map(& &1.id)
    |> update_license_data(form_4.id)
    |> update_categories_data(category_3.id)

    IO.inspect("FORM 6")

    [
      %{
        name: "applicant_name",
        label: "Applicant's Name",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the full name of the applicant."
      },
      %{
        name: "other_names",
        label: "Other Names",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        required: false,
        description: "Enter other names of the applicant."
      },
      %{
        name: "residential_address",
        label: "Residential Address",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the residential address of the applicant."
      },
      %{
        name: "telephone_number",
        label: "Telephone Number",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the applicant's telephone number."
      },
      %{
        name: "date_of_birth",
        label: "Date of Birth",
        required: true,
        type: "date",
        options: nil,
        dependents: nil,
        description: "Enter the applicant's date of birth."
      },
      %{
        name: "place_of_birth",
        label: "Place of Birth",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the place of birth of the applicant."
      },
      %{
        name: "years_resided",
        label: "Years Resided in Zambia",
        type: "number",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the number of years the applicant has resided in Zambia."
      },
      %{
        name: "nationality",
        label: "Nationality",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the nationality of the applicant."
      },
      %{
        name: "occupation",
        label: "Occupation",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the occupation for which the representative's licence is sought."
      },
      %{
        name: "principal_name",
        label: "Full Name of Principal",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the full name of the principal."
      },
      %{
        name: "principal_address",
        required: true,
        label: "Principal's Business Address",
        type: "text",
        options: nil,
        dependents: nil,
        description:
          "Enter the full address at which the business of the principal is to be carried on."
      },
      %{
        name: "principal_telephone",
        required: true,
        label: "Principal's Telephone Number",
        type: "text",
        options: nil,
        dependents: nil,
        description: "Enter the telephone number of the principal."
      },
      %{
        name: "principal_business_nature",
        label: "Nature of Business of Principal",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Describe the nature of the principal's business."
      },
      %{
        name: "record_address",
        label: "Record-Keeping Address",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the address where records under Section 28 of the Act will be kept."
      },
      %{
        name: "remuneration",
        label: "Remuneration Arrangement",
        required: true,
        type: "select",
        options: ["Salary", "Commission", "Both"],
        dependents: nil,
        description: "Select the present remuneration arrangement with the principal."
      },
      %{
        name: "directorships",
        label: "Directorships",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "List any directorships in companies in Zambia or elsewhere."
      },
      %{
        name: "national_id",
        label: "National Registration Card ID",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the applicant's National Registration Card ID number."
      },
      %{
        name: "passport_number",
        label: "Passport Number",
        type: "text",
        options: nil,
        dependents: nil,
        required: false,
        description: "Enter the applicant's Passport number."
      },
      %{
        name: "licensed_to_trade_securities",
        label: "Licensed to Trade in Securities?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: nil,
        description: "Has the applicant been licensed or registered to trade in securities?"
      },
      %{
        name: "licensed_other_business",
        label: "Licensed for Other Business?",
        type: "select",
        options: ["Yes", "No"],
        dependents: nil,
        required: true,
        description:
          "Has the applicant been licensed or authorized to carry out any other business or profession?"
      },
      %{
        name: "refused_trade_rights",
        label: "Refused Trade Rights?",
        type: "select",
        required: true,
        options: ["Yes", "No"],
        dependents: nil,
        description:
          "Has the applicant ever been refused the right to carry out a business requiring registration?"
      },
      %{
        name: "shareholder_member_firm",
        label: "Shareholder in a Member Firm?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: nil,
        description:
          "Has the applicant been a shareholder in a member firm of any securities exchange?"
      },
      %{
        name: "suspended_from_exchange",
        label: "Suspended from a Securities Exchange?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: nil,
        description: "Has the applicant been suspended or disciplined by a securities exchange?"
      },
      %{
        name: "refused_membership_exchange",
        label: "Refused Membership of Securities Exchange?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: ["annexure_refused_membership_exchange"],
        description:
          "Has any director or secretary of the applicant been refused membership of any securities exchange?"
      },
      %{
        name: "annexure_refused_membership_exchange",
        label: "Annexure: Refusal Details",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["refused_membership_exchange"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of the refusal."
      },
      %{
        name: "known_by_other_name",
        label: "Known by Other Name?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: ["annexure_known_by_other_name"],
        description:
          "Has any director or secretary of the applicant been known by any name other than those shown in this application?"
      },
      %{
        name: "annexure_known_by_other_name",
        label: "Annexure: Other Names Used",
        type: "upload",
        options: nil,
        required: true,
        dependents: ["known_by_other_name"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of other names used."
      },
      %{
        name: "convicted_offense",
        label: "Convicted of an Offense?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: nil,
        description:
          "Has the applicant been convicted of any offense, excluding traffic offenses?"
      },
      %{
        name: "judgment_fraud",
        label: "Judgment for Fraud or Misrepresentation?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: nil,
        description: "Has the applicant been judged for fraud, misrepresentation, or dishonesty?"
      },
      %{
        name: "refused_fidelity_bond",
        label: "Refused Fidelity or Surety Bond?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: ["annexure_refused_fidelity_bond"],
        description:
          "Has any director or secretary of the applicant been refused a fidelity or surety bond in Zambia or elsewhere?"
      },
      %{
        name: "annexure_refused_fidelity_bond",
        label: "Annexure: Fidelity/Surety Bond Refusal",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["refused_fidelity_bond"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of the refusal."
      },
      %{
        name: "bankruptcy_declared",
        label: "Declared Bankrupt?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: nil,
        description:
          "Has the applicant been declared bankrupt or made an arrangement with creditors?"
      },
      %{
        name: "disqualified_director",
        label: "Disqualified as a Director?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: nil,
        description:
          "Has the applicant been disqualified as a director or been a director of a liquidated company?"
      },
      %{
        name: "experience_in_role",
        label: "Experience in Role?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: nil,
        description:
          "Has the applicant had experience as a dealer's or investment representative?"
      },
      %{
        name: "employment_history",
        label: "Employment History",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description:
          "Provide details of employment and business activities over the last 10 years."
      },
      %{
        name: "character_references",
        label: "Character References",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description:
          "Provide details of two persons who can attest to the applicant’s character and reputation."
      },
      %{
        name: "significant_shareholdings",
        label: "Significant Shareholdings?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: nil,
        description:
          "Does the applicant hold at least 5% of shares in any publicly traded company?"
      },
      %{
        name: "additional_information",
        label: "Additional Information",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: false,
        description:
          "Enter any other relevant information, including qualifications and training."
      },
      %{
        name: "supporting_certificate",
        label: "Supporting Certificate",
        type: "upload",
        options: nil,
        dependents: nil,
        required: true,
        description:
          "Upload a signed certificate verifying the applicant's character, reputation, and competence."
      },
      %{
        name: "annexure_trade_securities",
        label: "Annexure: Licensed to Trade in Securities",
        type: "upload",
        options: nil,
        required: true,
        dependents: ["licensed_to_trade_securities"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details about previous licenses or registrations."
      },
      %{
        name: "annexure_other_business",
        label: "Annexure: Licensed for Other Business",
        type: "upload",
        options: nil,
        required: true,
        dependents: ["licensed_other_business"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of business or profession licenses."
      },
      %{
        name: "annexure_refused_trade",
        label: "Annexure: Refused Trade Rights",
        type: "upload",
        options: nil,
        required: true,
        dependents: ["refused_trade_rights"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of restrictions or refusals."
      },
      %{
        name: "annexure_suspended_exchange",
        label: "Annexure: Suspended from a Securities Exchange",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["suspended_from_exchange"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of suspension or disciplinary actions."
      },
      %{
        name: "annexure_conviction",
        label: "Annexure: Conviction Details",
        type: "upload",
        options: nil,
        required: true,
        dependents: ["convicted_offense"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of the conviction or ongoing proceedings."
      },
      %{
        name: "annexure_fraud_judgment",
        label: "Annexure: Judgment for Fraud or Misrepresentation",
        type: "upload",
        options: nil,
        required: true,
        dependents: ["judgment_fraud"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of the judgment, including status."
      },
      %{
        name: "annexure_bankruptcy",
        label: "Annexure: Bankruptcy Declaration",
        type: "upload",
        options: nil,
        required: true,
        dependents: ["bankruptcy_declared"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of bankruptcy or financial settlements."
      },
      %{
        name: "annexure_director_disqualification",
        label: "Annexure: Director Disqualification",
        type: "upload",
        options: nil,
        required: true,
        dependents: ["disqualified_director"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of disqualification or company liquidation."
      },
      %{
        name: "annexure_experience",
        label: "Annexure: Experience in Role",
        type: "upload",
        options: nil,
        required: true,
        dependents: ["experience_in_role"],
        dependent_selection: "Yes",
        description:
          "If 'Yes', attach details of experience as a dealer’s or investment representative."
      },
      %{
        name: "annexure_employment_history",
        label: "Annexure: Employment History",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["employment_history"],
        dependent_selection: "Yes",
        description:
          "Attach a document listing the applicant's employment and business activities over the last 10 years."
      },
      %{
        name: "annexure_character_references",
        label: "Annexure: Character References",
        type: "upload",
        options: nil,
        required: true,
        dependents: ["character_references"],
        dependent_selection: "Yes",
        description:
          "Attach reference letters from two individuals attesting to the applicant’s character and reputation."
      },
      %{
        name: "annexure_shareholdings",
        label: "Annexure: Significant Shareholdings",
        type: "upload",
        options: nil,
        required: true,
        dependents: ["significant_shareholdings"],
        dependent_selection: "Yes",
        description: "If 'Yes', attach details of shares owned and percentage of ownership."
      },
      %{
        name: "annexure_additional_info",
        label: "Annexure: Additional Information",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["additional_information"],
        dependent_selection: "Yes",
        description:
          "Attach any additional information relevant to the application, including qualifications or training certificates."
      },
      %{
        name: "curriculum_upload",
        label: "Curriculum Vitae",
        type: "upload",
        options: nil,
        dependents: nil,
        required: true,
        description:
          "Upload your Curriculum Vitae. Please include names of three referees and their current address (Not politically connected persons or family member or workmates. Please see SEC Guidelines)"
      },
      %{
        name: "reference_upload",
        label: "Reference Letters",
        type: "upload",
        options: nil,
        dependents: nil,
        required: true,
        description:
          "Upload Two reference letters. (Please collect/ Standard letter for each from the SEC at point of collecting/ downloading application form)"
      },
      %{
        name: "certificate_upload",
        label: "Certificates",
        type: "upload",
        options: nil,
        required: true,
        dependents: nil,
        description: "Upload Certified Copies of Certificate"
      },
      %{
        name: "proof_of_residence_upload",
        label: "Proof of Residential Address",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description:
          "Upload Proof of residential address (e. g original or certified copy of utility bill, council land rate, lease agreement, or letter from employer, etc)"
      },
      %{
        name: "id_upload",
        label: "Identity Card",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Upload Certified copy of identity card"
      },
      %{
        name: "is_zambian",
        label: "Is the Applicant Zambian?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: nil,
        description: "Is the applicant Zambian?"
      },
      %{
        name: "work_permit_upload",
        label: "Work Permit",
        type: "upload",
        required: true,
        options: nil,
        dependents: ["is_zambian"],
        dependent_selection: "No",
        description: "If 'No', attach Certified copy of work permit if not Zambian."
      },
      %{
        name: "expiring_date",
        label: "Work Permit Expiry Date",
        type: "date",
        required: false,
        options: nil,
        # dependents: ["is_zambian"],
        dependents: nil,
        description: "Work Permit Expiry Date (Only applicable if applicant is foreign)."
      },
      %{
        name: "cmaz_upload",
        label: "CMAZ Membership",
        type: "upload",
        required: true,
        options: nil,
        dependents: nil,
        description: "Upload CMAZ Membership"
      },
      %{
        name: "sec_course_upload",
        label: "SEC Approved Course",
        type: "upload",
        options: nil,
        required: true,
        dependents: nil,
        description: "Upload Proof of having completed SEC approved course"
      },
      %{
        name: "asset_liabity_upload",
        label: "Statement of Assets and Liabilities",
        type: "upload",
        options: nil,
        required: true,
        dependents: nil,
        description: "Upload Latest statement of assets and liabilities (signed by owner)"
      },
      %{
        name: "pop_upload",
        label: "Proof of Payment",
        type: "upload",
        options: nil,
        dependents: nil,
        required: true,
        description: "Upload a Proof of Payment(POP) Document."
      },
      %{
        name: "course_certificate_upload",
        label: "Course Certificate or Proof of Enrollment",
        type: "upload",
        options: nil,
        dependents: nil,
        required: false,
        description:
          "Upload Certificate of Course Completion or Proof of Enrollment. Note: License will only be issued after completion of the course."
      },
      %{
        name: "other_upload",
        label: "Other",
        type: "upload",
        options: nil,
        dependents: nil,
        required: false,
        description: "Upload any Other Relevant Document."
      }
    ]
    |> update_license_fields()
    |> Enum.map(& &1.id)
    |> update_license_data(form_6.id)
    |> update_categories_data(category_3.id)

    IO.inspect("FORM 9")

    [
      %{
        name: "name",
        label: "Full Name",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the full name of the applicant."
      },
      %{
        name: "capacity",
        label: "Capacity",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Specify the capacity in which Section 28 of the Act applies."
      },
      %{
        name: "residential_address",
        label: "Residential Address",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the residential address of the applicant."
      },
      %{
        name: "residential_phone",
        label: "Residential Telephone Number",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the residential telephone number of the applicant."
      },
      %{
        name: "business_address",
        label: "Business Address",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the business address of the applicant."
      },
      %{
        name: "business_phone",
        label: "Business Telephone Number",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the business telephone number of the applicant."
      },
      %{
        name: "employer_name",
        label: "Employer Name",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the name of the employer, if any."
      },
      %{
        name: "record_place",
        label: "Place of Record Keeping",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Specify the place where the record will be kept."
      },
      %{
        name: "record_start_date",
        label: "Date of Commencement",
        type: "date",
        options: nil,
        dependents: nil,
        required: true,
        description: "Select the date on which the record-keeping will commence."
      },
      %{
        name: "signature",
        label: "Signature",
        type: "signature",
        options: nil,
        dependents: nil,
        required: true,
        description: "Provide the signature of the applicant."
      },
      %{
        name: "applicant_name",
        label: "Applicant Name",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the name of the applicant."
      },
      %{
        name: "applicant_capacity",
        label: "Applicant Capacity",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Specify the capacity of the applicant."
      },
      %{
        name: "signature_upload",
        label: "Signature",
        type: "upload",
        options: nil,
        dependents: nil,
        required: true,
        description: "Upload the applicant's signature."
      },
      %{
        name: "pop_upload",
        label: "Proof of Payment",
        type: "upload",
        options: nil,
        dependents: nil,
        required: true,
        description: "Upload a Proof of Payment(POP) Document."
      }
    ]
    |> update_license_fields()
    |> Enum.map(& &1.id)
    |> update_license_data(form_9.id)
    |> update_categories_data(category_3.id)

    IO.inspect("FORM 10")

    [
      %{
        name: "licensee_name",
        label: "Name of Licensee",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the name of the licensee."
      },
      %{
        name: "license_type",
        label: "Type of Licence Held",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Specify the type of licence held."
      },
      %{
        name: "license_number",
        label: "Licence Number",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the licence number."
      },
      %{
        name: "former_business_address",
        label: "Former Address of Place of Business",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the former address of the place of business."
      },
      %{
        name: "new_business_address",
        label: "New Address of Place of Business",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the new address of the place of business."
      },
      %{
        name: "business_address_change_date",
        label: "Date of Change of Place of Business",
        type: "date",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the date when the business address changed."
      },
      %{
        name: "former_record_address",
        label: "Former Address of Place at Which Record Was Kept",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the former address where records were kept."
      },
      %{
        name: "new_record_address",
        label: "New Address of Place at Which Record is Kept",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the new address where records are kept."
      },
      %{
        name: "record_address_change_date",
        label: "Date of Change of Address of Place at Which Record is Kept",
        type: "date",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the date when the record-keeping address changed."
      },
      %{
        name: "signatory_name",
        label: "Name of Signatory",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the name of the person signing the document."
      },
      %{
        name: "capacity",
        label: "Capacity",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Specify the capacity in which the signatory is acting."
      },
      %{
        name: "signature_upload",
        label: "Signature",
        type: "upload",
        options: nil,
        dependents: nil,
        required: true,
        description: "Upload the applicant's signature."
      },
      %{
        name: "pop_upload",
        label: "Proof of Payment",
        type: "upload",
        options: nil,
        required: true,
        dependents: nil,
        description: "Upload a Proof of Payment(POP) Document."
      },
      %{
        name: "other_upload",
        label: "Other",
        type: "upload",
        options: nil,
        dependents: nil,
        required: false,
        description: "Upload any Other Relevant Document."
      }
    ]
    |> update_license_fields()
    |> Enum.map(& &1.id)
    |> update_license_data(form_10.id)
    |> update_categories_data(category_3.id)

    IO.inspect("FORM 11")

    [
      %{
        name: "licensee_name",
        label: "Name of Licensee",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the name of the licensee."
      },
      %{
        name: "license_type",
        label: "Type of Licence Held",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Specify the type of licence held."
      },
      %{
        name: "license_number",
        label: "Licence Number",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the licence number."
      },
      %{
        name: "cessation_date",
        label: "Date of Cessation of Business",
        type: "date",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the date when the business ceased operations."
      },
      %{
        name: "signatory_name",
        label: "Name of Signatory",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the name of the person signing the document."
      },
      %{
        name: "capacity",
        label: "Capacity",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Specify the capacity in which the signatory is acting."
      },
      %{
        name: "signature_upload",
        label: "Signature",
        type: "upload",
        options: nil,
        dependents: nil,
        required: true,
        description: "Upload the applicant's signature."
      },
      %{
        name: "pop_upload",
        label: "Proof of Payment",
        type: "upload",
        options: nil,
        required: true,
        dependents: nil,
        description: "Upload a Proof of Payment(POP) Document."
      },
      %{
        name: "other_upload",
        label: "Other",
        type: "upload",
        options: nil,
        dependents: nil,
        required: false,
        description: "Upload any Other Relevant Document."
      }
    ]
    |> update_license_fields()
    |> Enum.map(& &1.id)
    |> update_license_data(form_9.id)
    |> update_categories_data(category_3.id)

    IO.inspect("FORM 12")

    [
      %{
        name: "licensee_name",
        label: "Name of Licensee",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the name of the licensee."
      },
      %{
        name: "license_type",
        label: "Type of Licence Held",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Specify the type of licence held."
      },
      %{
        name: "license_number",
        label: "Licence Number",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the licence number."
      },
      %{
        name: "former_business_address",
        label: "Former Address of Place of Business",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the former address of the place of business."
      },
      %{
        name: "new_business_address",
        label: "New Address of Place of Business",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the new address of the place of business."
      },
      %{
        name: "business_address_change_date",
        label: "Date of Change of Place of Business",
        type: "date",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the date when the business address changed."
      },
      %{
        name: "former_record_address",
        label: "Former Address of Place at Which Record Was Kept",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the former address where records were kept."
      },
      %{
        name: "new_record_address",
        label: "New Address of Place at Which Record is Kept",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the new address where records are kept."
      },
      %{
        name: "record_address_change_date",
        label: "Date of Change of Address of Place at Which Record is Kept",
        type: "date",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the date when the record-keeping address changed."
      },
      %{
        name: "signatory_name",
        label: "Name of Signatory",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Enter the name of the person signing the document."
      },
      %{
        name: "capacity",
        label: "Capacity",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Specify the capacity in which the signatory is acting."
      },
      %{
        name: "signature_upload",
        label: "Signature",
        type: "upload",
        options: nil,
        dependents: nil,
        required: true,
        description: "Upload the applicant's signature."
      },
      %{
        name: "pop_upload",
        label: "Proof of Payment",
        type: "upload",
        options: nil,
        dependents: nil,
        required: true,
        description: "Upload a Proof of Payment(POP) Document."
      },
      %{
        name: "other_upload",
        label: "Other",
        type: "upload",
        options: nil,
        dependents: nil,
        required: false,
        description: "Upload any Other Relevant Document."
      }
    ]
    |> update_license_fields()
    |> Enum.map(& &1.id)
    |> update_license_data(form_12.id)
    |> update_categories_data(category_3.id)

    IO.inspect("SANDBOX")

    [
      %{
        name: "participant_name",
        label: "Name of Participant/Firm",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter the full name of the participant or firm."
      },
      %{
        name: "firm_type",
        label: "Type of Firm",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description:
          "Specify the type of firm (e.g., sole proprietor, limited liability company)."
      },
      %{
        name: "business_description",
        label: "Description of the nature and scale of business",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description: "Provide details of the nature and scale of your business."
      },
      %{
        name: "registered_by_sec",
        label: "Is the firm registered, licensed or supervised by SEC?",
        type: "select",
        options: ["Yes", "No"],
        required: true,
        dependents: nil,
        description:
          "State if the firm is currently registered, licensed or supervised by the Securities and Exchange Commission."
      },
      %{
        name: "registered_by_other",
        label: "Is the firm registered, licensed or supervised by another financial regulator?",
        type: "select",
        options: ["Yes", "No"],
        dependents: nil,
        required: true,
        description:
          "State if the firm is currently registered, licensed or supervised by any other financial sector regulator."
      },
      %{
        name: "contact_details",
        label: "Contact Information",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description: "Provide physical address, postal address, e-mail address, website."
      },
      %{
        name: "phone_number",
        label: "Phone Number",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Enter a valid phone number for contact."
      },
      %{
        name: "company_ids",
        label: "Company Registration Number and TPIN",
        type: "text",
        options: nil,
        required: true,
        dependents: nil,
        description: "Provide company registration and taxpayer identification number."
      },
      %{
        name: "owners_details",
        label: "Details of Owners/Shareholders/Directors",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description: "Include name, position, email, and phone number of each."
      },
      %{
        name: "key_personnel",
        label: "Key Personnel",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description:
          "Include name, position, email, and phone number of each key personnel (e.g., CEO, CFO, COO)."
      },
      %{
        name: "key_contacts",
        label: "Key Contact Persons",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description: "Include name, position, email, and phone number of each key contact person."
      },
      %{
        name: "partner_firms",
        label: "Details of Partner Firms",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description:
          "Include name, partnership description, and full contact details of partner firms."
      },
      %{
        name: "innovation_description",
        label: "Description of the Proposed Innovation",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description: "Provide a description of the innovation you are proposing."
      },
      %{
        name: "innovation_principles",
        label: "How Innovation Meets Sandbox Principles",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description: "Explain how your innovation meets the sandbox principles and criteria."
      },
      %{
        name: "innovation_timeline",
        label: "Development and Deployment Timeline",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description:
          "Describe the key timelines for the development and deployment of the innovation."
      },
      %{
        name: "innovation_stage",
        label: "Current Stage of Innovation",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "Describe the current stage (e.g., prototype, MVP, fully deployed)."
      },
      %{
        name: "technology_description",
        label: "Technology and Infrastructure",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description: "Describe the underlying technology and infrastructure of the innovation."
      },
      %{
        name: "technology_compliance",
        label: "Standards and Compliance",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description: "Indicate any standards, accreditation, or compliance adhered to."
      },
      %{
        name: "technology_location",
        label: "Location of Technology",
        type: "text",
        options: nil,
        dependents: nil,
        required: true,
        description: "State the location of the technology and infrastructure."
      },
      %{
        name: "innovation_risks",
        label: "Risks Associated with the Innovation",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description: "Describe the risks involved in implementing the innovation."
      },
      %{
        name: "mitigation_measures",
        label: "Mitigation Measures",
        type: "textarea",
        options: nil,
        dependents: nil,
        required: true,
        description: "For each identified risk, describe the proposed mitigation measures."
      },
      %{
        name: "attachment_business_profile",
        label: "Business Profile",
        type: "upload",
        options: nil,
        dependents: nil,
        required: true,
        description: "Upload the business profile document."
      },
      %{
        name: "attachment_key_personnel_profiles",
        label: "Profiles of Key Personnel",
        type: "upload",
        options: nil,
        dependents: nil,
        required: true,
        description: "Upload the profiles of key personnel."
      },
      %{
        name: "attachment_business_plan",
        label: "Business Plan",
        type: "upload",
        options: nil,
        dependents: nil,
        required: true,
        description: "Upload the business plan."
      },
      %{
        name: "attachment_financial_statements",
        label: "Financial Statements",
        type: "upload",
        options: nil,
        dependents: nil,
        required: true,
        description: "Upload the financial statements."
      },
      %{
        name: "attachment_test_plan",
        label: "Test Plan",
        type: "upload",
        options: nil,
        required: true,
        dependents: nil,
        description: "Upload the test plan."
      },
      %{
        name: "attachment_risk_framework",
        label: "Risk Management Framework",
        type: "upload",
        options: nil,
        dependents: nil,
        required: true,
        description: "Upload the risk management framework document."
      }
    ]
    |> update_license_fields()
    |> Enum.map(& &1.id)
    |> update_license_data(sandbox.id)
    |> update_categories_data(category_5.id)

    {form_1, form_2, form_4, form_6, form_8, form_9, form_10, form_11, form_12}
  end

  defp update_license_data(ids, form_id) do
    Enum.each(ids, fn id ->
      run_seeds_db(%LicenseData{
        license_id: form_id,
        license_field_id: id
      })
    end)

    ids
  end

  defp update_categories_data(ids, category_id) do
    Enum.each(ids, fn id ->
      run_seeds_db(%LicenseCategoriesData{
        categories_id: category_id,
        license_field_id: id
      })
    end)
  end

  defp update_license_fields(list) do
    Enum.map(list, fn data ->
      if record = Licenses.get_fields_by_name(data[:name]) do
        Licenses.update_license_fields(record, %{
          field_name: data[:name],
          field_type: data[:type],
          required: data[:required],
          field_label: data[:label],
          field_validations: data[:validations],
          field_options: data[:options],
          field_dependents: data[:dependents],
          dependent_selection: data[:dependent_selection],
          field_description: data[:description]
        })
      else
        Licenses.create_license_fields(%{
          field_name: data[:name],
          field_type: data[:type],
          #

          required: data[:required],
          field_label: data[:label],
          field_validations: data[:validations],
          field_options: data[:options],
          field_dependents: data[:dependents],
          dependent_selection: data[:dependent_selection],
          field_description: data[:description]
        })
      end
    end)
  end
end
