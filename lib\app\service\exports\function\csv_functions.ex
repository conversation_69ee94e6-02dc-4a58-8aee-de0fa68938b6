defmodule App.Service.Export.CsvFunctions do
  @moduledoc false
  alias App.Service.Export.Functions
  alias AppWeb.Endpoint

  @extension ".csv"

  alias App.Service.Export.{
    CsvUsers,
    CsvContactPersonService,
    CsvClientProfileService,
    CsvAccessService,
    CsvSenderService,
    CsvPurchasesService,
    CsvRatesServices,
    CsvErrorService,
    CsvAnnualStatisticsService,
    CsvMonthlyStatisticalService,
    # CsvAccessService,
    CsvApiLogsService,
    CsvSessionLogService,
    CsvSystemLogService,
    # CsvClientStatisticalService,
    CsvSelfRegistrationApplicationsServices,
    CsvSmsLogsServices,
    CsvPaymentPlanServices,
    CsvPrepaidClientStatementServices,
    CsvPostpaidClientStatementServices,
    CsvApiServicesService,
    CsvSmppServicesServices,
    CsvDepartmentSRolesManagementService,
    CsvAdminAccessService,
    CsvAdminAccessNoneUserService,
    CsvServiceProviderReports,
    CsvClientStatisticsService,
    CsvArchieveSmsLogsServices,
    CsvTransactionReportsService,
    CsvAnnualStatisticalService,
    Functions
  }

  def annual_report_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Annual Reports in CSV",
      "annual_reports",
      @extension,
      &CsvAnnualStatisticalService.index/2
    )
  end

  def transaction_reports_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Transaction Reports in Csv",
      "transaction_reports",
      @extension,
      &CsvTransactionReportsService.index/2
    )
  end

  def archieve_sms_logs_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Sms Logs Archieve in CSV",
      "sms_logs_archieve",
      @extension,
      &CsvArchieveSmsLogsServices.index/2
    )
  end

  def client_statistics_report(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Client Statistics in Csv",
      "client_statistics",
      @extension,
      &CsvClientStatisticsService.index/2
    )
  end

  def service_provider_report(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Service Provider Reports in Csv",
      "service_provider_reports",
      @extension,
      &CsvServiceProviderReports.index/2
    )
  end

  def session_logs_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Session Logs table in Csv",
      "session_logs",
      @extension,
      &CsvServiceProviderReports.index/2
    )
  end

  def admin_access_role_none_role_user_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Admin Access Role Management for None Role User in CSV",
      "Admin Access Role None Role user",
      @extension,
      &CsvAdminAccessNoneUserService.index/1
    )
  end

  def smpp_services_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for SMPP Services in Csv",
      "SMPP Services",
      @extension,
      &CsvSmppServicesServices.index/2
    )
  end

  def department_roles_management_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for Department Roles Management in Csv",
      "Department Role Management",
      @extension,
      &CsvDepartmentSRolesManagementService.index/1
    )
  end

  def api_services_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for API Services in Csv",
      "API Services",
      @extension,
      &CsvApiServicesService.index/2
    )
  end

  def postpaid_client_statement_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for Postpaid Client Statement in Csv",
      "postpaid client statement",
      @extension,
      &CsvPostpaidClientStatementServices.index/2
    )
  end

  def prepaid_client_statement_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for Prepaid Client Statement in Csv",
      "prepaid client statement",
      @extension,
      &CsvPrepaidClientStatementServices.index/2
    )
  end

  def payment_plans_services(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Payment Plans in CSV",
      "Payment Plans",
      @extension,
      &CsvPaymentPlanServices.index/2
    )
  end

  def sms_logs_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Sms Logs Service  in CSV",
      "Sms Logs Service",
      @extension,
      &CsvSmsLogsServices.index/2
    )
  end

  def self_registration_applications_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Self Registration Applications in CSV",
      "self registration application",
      @extension,
      &CsvSelfRegistrationApplicationsServices.index/2
    )
  end

  def system_logs_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading System Logs in CSV",
      "system_audit logs_service",
      @extension,
      &CsvSystemLogService.index/1
    )
  end

  def api_logs_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading API Logs Management in CSV",
      "api_logs_service",
      @extension,
      &CsvApiLogsService.index/1
    )
  end

  def access_service(_socket, payload) do
    Functions.run_export(
      "",
      payload,
      "Downloading Access Roles Management in CSV",
      "Access Roles Management",
      @extension,
      &CsvAccessService.index/1
    )
  end

  def admin_access_role_management_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Admin Access Role Management in CSV",
      "Admin Access Role Management",
      @extension,
      &CsvAdminAccessService.index/1
    )
  end

  def monthly_report_service(socket, payload, date \\ Timex.now()) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Periodic Reports in CSV",
      "monthly_provider_report_service",
      @extension,
      &CsvMonthlyStatisticalService.index/2
    )
  end

  def annual_report_service(socket, payload, date \\ Timex.now()) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Yearly Reports in CSV",
      "annual_provider_report_service",
      @extension,
      &CsvAnnualStatisticsService.index/2
    )
  end

  def error_service(assigns, payload) do
    Functions.run_export(
      assigns,
      payload,
      "Downloading for Messaging Error table in Csv",
      "Messaging Error",
      @extension,
      &CsvErrorService.index/2
    )
  end

  def rates_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Rate Service table in CSV",
      "Rate Service",
      @extension,
      &CsvRatesServices.index/2
    )
  end

  def purchase_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Bundle Purchases table in CSV",
      "Purchases Bundle",
      @extension,
      &CsvPurchasesService.index/2
    )
  end

  def sender_service(assign, payload, date \\ Timex.now()) do
    Functions.run_export(
      assign,
      payload,
      "Downloading Sender Service table in Csv",
      "Sender Service",
      @extension,
      &CsvSenderService.index/2
    )
  end

  def client_profile_service(assign, payload) do
    Functions.run_export(
      assign,
      payload,
      "Downloading for Client Profiles table in Csv",
      "Clients Profiles",
      @extension,
      &CsvClientProfileService.index/1
    )
  end

  def contact_person_service(_socket, payload, date \\ Timex.now()) do
    Functions.run_export(
      "",
      payload,
      "Downloading for Contact Person table in Csv",
      "Contact Person",
      @extension,
      &CsvContactPersonService.index/1
    )
  end

  def user_service(_socket, payload) do
    Functions.run_export(
      " ",
      payload,
      "Downloading for Admin User table in Csv",
      "Admin Users",
      @extension,
      &CsvUsers.index/1
    )
  end
end
