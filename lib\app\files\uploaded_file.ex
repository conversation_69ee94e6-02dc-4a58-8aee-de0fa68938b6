defmodule App.Files.UploadedFile do
  use Ecto.Schema
  import Ecto.Changeset

  schema "uploaded_files" do
    field :filename, :string
    field :path, :string
    field :type, :string
    field :level, :integer, default: 0
    field :is_condition, :boolean, default: false
    belongs_to :user, App.Accounts.User
    belongs_to :application, App.Licenses.LicenseMapping
    belongs_to :condition_map, App.Licenses.LicenseConditionsMapping

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(uploaded_file, attrs) do
    uploaded_file
    |> cast(attrs, [
      :filename,
      :type,
      :path,
      :level,
      :user_id,
      :application_id,
      :condition_map_id,
      :is_condition
    ])
    |> validate_required([:filename, :path, :user_id, :application_id])
  end
end
