defmodule App.Service.Table.ServiceSender do
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false

  alias App.Accounts.SenderId
  alias App.Repo

  @pagination [page_size: 10]

  def index(assigns, params) do
    compose_query(params, assigns)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def export(assigns, params) do
    assigns = Map.put_new(assigns, :client, nil)

    compose_query(params, assigns)
    |> Repo.all()
  end

  def compose_query(params, assigns) do
    SenderId
    |> join(:left, [a], b in assoc(a, :user))
    |> join(:left, [a], c in assoc(a, :client))
    |> check_user_role(assigns)
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  defp check_user_role(query, assigns) do
    if !is_nil(assigns.client) do
      query
      |> where([a], a.client_id == ^assigns.client.id)
    else
      query
    end
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"client_id", value}, query when byte_size(value) > 0 ->
        where(query, [a], a.client_id == ^value)

      {"sender", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.sender, ^sanitize_term(value)))

      {"status", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.status, ^sanitize_term(value)))

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a, b, c], %{
      id: a.id,
      client_id: c.id,
      sender: a.sender,
      client_name: c.client_name,
      payment_type: c.payment_type,
      status: a.status,
      created_by: fragment("CONCAT(?, ' ', ?)", b.first_name, b.last_name),
      inserted_at: fragment("TO_CHAR (?, 'DD MON YYYY, HH24:MI:SS')", a.inserted_at),
      updated_at: fragment("TO_CHAR (?, 'DD MON YYYY, HH24:MI:SS')", a.updated_at)
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      fragment("lower(?) LIKE lower(?)", c.client_name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", b.last_name, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
