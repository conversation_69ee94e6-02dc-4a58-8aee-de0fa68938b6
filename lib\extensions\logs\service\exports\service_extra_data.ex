defmodule Log.Service.Export.ExtraData do
  @moduledoc false
  alias App.Service.Export.Functions

  def sms_logs_service_header do
    [
      ["INIT DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["SENT DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["RECIPIENT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["MASSAGE ID", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["ATTEMPT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["BODY", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def sms_logs_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      Calendar.strftime(post.date_sent, "%d %B %Y %H:%M:%S"),
      post.recipient,
      post.message_id,
      NumberF.comma_separated(post.msg_count, 0),
      post.status,
      post.message
    ]
  end

  def session_logs_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["USERNAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PORTAL", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["SESSION", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["DESCRIPTION", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def session_logs_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      post.user_id,
      post.portal,
      post.status,
      post.description
    ]
  end

  def system_audit_logs_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["USERNAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["SERVICE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["ACTION", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["IP ADDRESS", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["DESCRIPTION", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def system_audit_logs_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      post.user,
      post.service,
      post.action,
      post.ip_address,
      post.narration
    ]
  end

  def api_logs_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["SERVICE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["REFERENCE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["IP ADDRESS", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["EXTERNAL REFERENCE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["ENDPOINT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["REQUEST", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["RESPONSE", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def api_logs_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      post.service,
      post.reference,
      post.ip_address,
      post.external_reference,
      post.endpoint,
      post.request,
      post.response
    ]
  end

  def access_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["DESCRIPTION", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["DISTRICT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["USER COUNT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def access_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      post.name,
      post.description,
      post.users,
      Functions.table_numeric_status(post.status)
    ]
  end

  def department_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["DESCRIPTION", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["DISTRICT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["ACCESS ROLES COUNT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def department_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      post.name,
      post.description,
      post.access_roles,
      Functions.table_numeric_status(post.status)
    ]
  end

  def location_depot_service_header do
    [
      ["NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["CODE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PROVINCE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["DISTRICT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def location_depot_service_row(post) do
    [
      post.name,
      post.code,
      post.province,
      post.district,
      Functions.table_numeric_status(post.status)
    ]
  end

  def location_district_service_header do
    [
      ["NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["CODE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PROVINCE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def location_district_service_row(post) do
    [
      post.name,
      post.code,
      post.province,
      Functions.table_numeric_status(post.status)
    ]
  end

  def location_region_service_header do
    [
      ["NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["CODE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def location_region_service_row(post) do
    [
      post.name,
      post.code,
      Functions.table_numeric_status(post.status)
    ]
  end

  def device_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["CODE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["API KEY", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def device_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      post.code,
      post.api_key,
      Functions.table_numeric_status(post.status)
    ]
  end

  def admin_email_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["EMAIL", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def admin_email_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      post.name,
      post.email,
      Functions.table_numeric_status(post.status)
    ]
  end

  def crop_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["CROP NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["CROP CODE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["DESCRIPTION", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PRICE(ZMW)", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def crop_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      post.name,
      post.code,
      post.description,
      NumberF.currency(post.amount, ""),
      Functions.table_numeric_status(post.status)
    ]
  end

  def payment_provider_service_header do
    [
      ["NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["TOTAL FARMERS", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["NUMBER PRCNs", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PAID PRCNs", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PENDING PRCNs", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["BATCH", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def payment_provider_service_row(post) do
    [
      post.name,
      post.total_farmers,
      post.number_prcns,
      post.paid_prcn,
      post.pending_prcns,
      post.batch
    ]
  end

  def regional_report_service_header do
    [
      ["NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["TOTAL FARMERS", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["NUMBER PRCNs", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PAID PRCNs", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PENDING PRCNs", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PENDING BCLs", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PAID BCLs", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def regional_report_service_row(post) do
    [
      post.name,
      post.total_farmers,
      post.number_prcns,
      post.paid_prcn,
      post.pending_prcns,
      post.pending_bcl,
      post.paid.bcl
    ]
  end

  def district_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["CODE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["TOTAL FARMERS", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PENDING PRCNs", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PAID PRCNs", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PRCN COUNT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def district_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      post.name,
      post.code,
      post.total_farmer,
      post.pending_prcn,
      post.paid_prcn,
      post.parcn_count,
      Functions.table_numeric_status(post.status)
    ]
  end

  def branch_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["BRANCH NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["BRANCH CODE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PAYMENT PARTICIPANT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def branch_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      post.name,
      post.branch_code,
      post.nfs_participant,
      Functions.table_numeric_status(post.status)
    ]
  end

  def farmersr_service_header do
    [
      ["REGION", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["FARMERS", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["TOTAL PRCNs", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PAID PRCNs", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PENDING PRCNs", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["BATCH", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def farmersr_service_row(post) do
    [
      post.name,
      post.total_farmers,
      post.number_prcns,
      post.paid_prcns,
      post.pending_prcns,
      post.batch
    ]
  end

  def farmers_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["NRC", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["FIRST NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["LAST NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["MOBILE NUMBERs", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["VALIDATION", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def farmers_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      post.username,
      post.first_name,
      post.last_name,
      post.mobile_number,
      Functions.table_numeric_status(post.status),
      Functions.kyc_validation_status(post.kyc_validation)
    ]
  end

  def users_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["USERNAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["FIRST NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["LAST NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["MOBILE NUMBER", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def users_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      post.username,
      post.first_name,
      post.last_name,
      post.mobile_number,
      Functions.table_numeric_status(post.status)
    ]
  end

  def client_service_header do
    [
      ["USERNAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["FIRSTNAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["LASTNAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["EMAIL", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["REGISTRATION DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["LAST LOGIN", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def client_service_row(post) do
    [
      post.username,
      post.first_name,
      post.last_name,
      post.email,
      Calendar.strftime(
        NaiveDateTime.add(post.registerd_date, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      Calendar.strftime(
        NaiveDateTime.add(post.date, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      Functions.table_numeric_status(post.status)
    ]
  end

  def agent_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["USERNAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["FIRST NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["GENDER", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["MOBILE NUMBER", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def agent_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      post.username,
      post.first_name,
      post.last_name,
      post.gender,
      post.mobile_number,
      Functions.table_numeric_status(post.status)
    ]
  end

  def crop_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["USERNAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["FIRST NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["GENDER", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["MOBILE NUMBER", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def crop_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      post.username,
      post.first_name,
      post.last_name,
      post.gender,
      post.mobile_number,
      Functions.table_numeric_status(post.status)
    ]
  end

  def prcn_maintenance_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PURCHASE LOCATION", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["FULL NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["NRC", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["CROP NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["NUMBER OF BAGS", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["TOTAL", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def prcn_maintenance_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      post.reference,
      post.depot,
      post.farmer_name,
      post.farmer_nrc,
      post.crop_name,
      post.number_of_bags,
      NumberF.currency(post.total, ""),
      Functions.table_numeric_status(post.status)
    ]
  end

  def nfs_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PARTICIPANT NAME", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["INSTITUTION CODE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["TYPE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["STATUS", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def nfs_service_row(post) do
    [
      Calendar.strftime(
        NaiveDateTime.add(post.inserted_at, 7200, :second),
        "%d %B %Y %H:%M:%S"
      ),
      post.name,
      post.inst_code,
      post.type,
      Functions.table_numeric_status(post.status)
    ]
  end

  def daily_provider_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PROVIDER", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PAID TXN COUNT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PAID TXN VALUE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["FAILED TXN COUNT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["FAILED TXN VALUE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["TOTAL TXN COUNT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["TOTAL TXN VALUE", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def daily_provider_service_row(post) do
    [
      Calendar.strftime(
        post.statement_date,
        "%d %B %Y"
      ),
      post.provider,
      NumberF.comma_separated(post.paid_transactions, 0),
      NumberF.currency(post.amount_paid, ""),
      NumberF.comma_separated(post.failed_transactions, 0),
      NumberF.currency(post.amount_failed, ""),
      NumberF.comma_separated(post.total_transactions, 0),
      NumberF.currency(post.total_amount, "")
    ]
  end

  def weekly_provider_service_header do
    [
      ["RANGE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PROVIDER", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PAID TXN COUNT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PAID TXN VALUE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["FAILED TXN COUNT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["FAILED TXN VALUE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["TOTAL TXN COUNT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["TOTAL TXN VALUE", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def weekly_provider_service_row(post) do
    [
      "#{post.start_date} - #{post.end_date}",
      post.provider,
      NumberF.comma_separated(post.paid_transactions, 0),
      NumberF.currency(post.amount_paid, ""),
      NumberF.comma_separated(post.failed_transactions, 0),
      NumberF.currency(post.amount_failed, ""),
      NumberF.comma_separated(post.total_transactions, 0),
      NumberF.currency(post.total_amount, "")
    ]
  end

  def monthly_provider_service_header do
    [
      ["DATE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PROVIDER", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PAID TXN COUNT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PAID TXN VALUE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["FAILED TXN COUNT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["FAILED TXN VALUE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["TOTAL TXN COUNT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["TOTAL TXN VALUE", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def monthly_provider_service_row(post) do
    [
      "#{App.Util.DateCounter.get_month_words(post.month)}, #{post.year}",
      post.provider,
      NumberF.comma_separated(post.paid_transactions, 0),
      NumberF.currency(post.amount_paid, ""),
      NumberF.comma_separated(post.failed_transactions, 0),
      NumberF.currency(post.amount_failed, ""),
      NumberF.comma_separated(post.total_transactions, 0),
      NumberF.currency(post.total_amount, "")
    ]
  end

  def annual_provider_service_header do
    [
      ["YEAR", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PROVIDER", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PAID TXN COUNT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["PAID TXN VALUE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["FAILED TXN COUNT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["FAILED TXN VALUE", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["TOTAL TXN COUNT", bg_color: "#2A185B", bold: true, color: "#ffffff"],
      ["TOTAL TXN VALUE", bg_color: "#2A185B", bold: true, color: "#ffffff"]
    ]
  end

  def annual_provider_service_row(post) do
    [
      post.year,
      post.provider,
      NumberF.comma_separated(post.paid_transactions, 0),
      NumberF.currency(post.amount_paid, ""),
      NumberF.comma_separated(post.failed_transactions, 0),
      NumberF.currency(post.amount_failed, ""),
      NumberF.comma_separated(post.total_transactions, 0),
      NumberF.currency(post.total_amount, "")
    ]
  end
end
