defmodule AppWeb.Components.RichTextEditorComponent do
  use AppWeb, :live_component

  @impl true
  def mount(socket) do
    {:ok, assign(socket, content: "")}
  end

  @impl true
  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_new(:id, fn -> "editor-#{Ecto.UUID.generate()}" end)
      |> assign_new(:height, fn -> "" end)
      |> assign_new(:content, fn -> "" end)
      |> assign_new(:with_export, fn -> true end)

    # If the editor is already loaded and content changes from parent,
    # we need to update the editor content
    if socket.assigns[:loaded] && socket.assigns.content != assigns[:content] do
      send_update_after_refresh(socket)
    end

    {:ok, assign(socket, loaded: true)}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="rich-text-editor">
      <div class="flex justify-between items-center">
        <%= if @with_export do %>
          <div class="flex space-x-2">
            <button
              phx-click="export_pdf"
              phx-target={@myself}
              class="px-3 py-1 bg-blue-500 text-white rounded text-sm"
            >
              Export PDF
            </button>
            <button
              phx-click="export_word"
              phx-target={@myself}
              class="px-3 py-1 bg-green-500 text-white rounded text-sm"
            >
              Export Word
            </button>
          </div>
        <% end %>
      </div>
      <div class="flex-1 p-4 bg-white" id={"container-#{@id}"} phx-update="ignore">
        <div
          id={@id}
          phx-hook="RichTextEditor"
          phx-update="ignore"
          class={"relative w-full #{@height} border rounded-lg overflow-hidden border-gray-300"}
        >
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def handle_event("update_rich_text", %{"id" => id, "value" => value}, socket) do
    # Make sure this event is for this specific component
    if id == socket.assigns.id do
      # Notify the parent if there's a change
      if socket.assigns.content != value do
        send(self(), {:editor_content_updated, id, value})
      end

      {:noreply, assign(socket, content: value)}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("export_pdf", _params, socket) do
    # Generate a filename based on the editor ID
    filename = "document-#{socket.assigns.id}"

    # Send event to the client to trigger PDF export
    {:noreply, push_event(socket, "export_pdf", %{id: socket.assigns.id, file_name: filename})}
  end

  @impl true
  def handle_event("export_word", _params, socket) do
    # Generate a filename based on the editor ID
    filename = "document-#{socket.assigns.id}"

    # Send event to the client to trigger Word export
    {:noreply, push_event(socket, "export_word", %{id: socket.assigns.id, file_name: filename})}
  end

  @impl true
  def handle_event("export_error", %{"format" => format, "message" => message}, socket) do
    # Handle export errors - could put a flash message here
    IO.puts("Error exporting to #{format}: #{message}")
    {:noreply, socket}
  end

  # Helper to send content to the editor after the component is fully rendered
  defp send_update_after_refresh(socket) do
    # This needs to happen after the client hook is mounted
    if connected?(socket) do
      # Use send_after to ensure DOM is ready
      Process.send_after(
        self(),
        {:initialize_editor, socket.assigns.id, socket.assigns.content},
        100
      )
    end

    socket
  end
end
