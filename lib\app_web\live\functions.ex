defmodule AppWeb.LiveFunctions do
  use AppWeb, :live_component

  def render(assigns) do
    ~H"""
    ...
    """
  end

  def notify_parent(module \\ __MODULE__, msg), do: send(self(), {module, msg})

  def change_record_status(value, socket) do
    params = Jason.decode!(value["params"])

    case params["action"] do
      "enable" ->
        send(self(), {:change_record_status, params})

        {:noreply, assign(socket, :confirmation_model_icon, "loading")}

      "disable" ->
        send(self(), {:change_record_status, params})
        {:noreply, assign(socket, :confirmation_model_icon, "loading")}

      "approve" ->
        send(self(), {:approval_status, Map.put(params, "reason", value["reason"])})
        {:noreply, assign(socket, :confirmation_model_icon, "loading")}

      "reject" ->
        send(self(), {:approval_status, Map.put(params, "reason", value["reason"])})
        {:noreply, assign(socket, :confirmation_model_icon, "loading")}
    end
  end

  def change_record(value, socket, target) do
    params = Jason.decode!(value["params"])

    param = Map.merge(params, %{"reason" => value["reason"]})
    send(self(), {target, param})

    {:noreply, assign(socket, :confirmation_model_icon, "loading")}
  end

  def ext(entry) do
    [ext | _] = MIME.extensions(entry.client_type)
    ext
  end

  def static_path(path), do: "#{Path.join([:code.priv_dir(:app), "static"])}#{path}"
  def priv_path(path), do: "#{:code.priv_dir(:app)}#{path}"

  def image_error_to_string(:too_large), do: "Too large"
  def image_error_to_string(:not_accepted), do: "You have selected an unacceptable file type"
  def image_error_to_string(:too_many_files), do: "You have selected too many files"

  def export_records(socket, data, service, filter) do
    {:noreply,
     CustomFunctions.file_export(socket, %{
       "browser_id" => to_string(socket.assigns.live_socket_identifier),
       "file_type" => data["file_type"],
       "service" => service,
       "filter" => filter
     })}
  end

  def sweet_alert(socket, message, icon \\ "info") do
    CustomFunctions.toast_messages(socket, message, icon)
  end

  def filter_form(data), do: to_form(data, as: "filter")

  def http_redirect(socket, fields, api, api_data) do
    {
      :noreply,
      push_event(socket, "http_redirect", %{
        "form_data" => %{
          "fields" => fields,
          "method" => api_data["redirect_method"],
          "action" => "#{api.base_url}#{api_data["redirect_endpoint"]}"
        }
      })
      |> assign(:live_action, :confirm)
      |> assign(confirmation_model_icon: "loading")
      |> assign(confirmation_model_title: "Accessing payment gateway")
      |> assign(
        confirmation_model_text:
          "Please wait while we access the payment gateway for you to make your payment"
      )
    }
  end
end
