defmodule App.Repo.Migrations.CreateConditionLevelsTable do
  use Ecto.Migration

  def change do
    create table(:conditional_levels) do
      add :status, :integer, default: 0
      add :approval_status, :integer, default: 1

      add :categories_id, references(:license_categories, on_delete: :nothing)
      add :department_id, references(:department_roles, on_delete: :nothing), null: false
      add :role_id, references(:access_roles, on_delete: :nothing), null: false

      timestamps(type: :utc_datetime)
    end

    create index(:conditional_levels, [:department_id, :role_id, :categories_id])
    create unique_index(:conditional_levels, [:department_id, :approval_status])
  end
end
