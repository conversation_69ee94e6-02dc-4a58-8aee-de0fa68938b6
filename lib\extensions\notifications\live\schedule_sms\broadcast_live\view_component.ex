defmodule AppWeb.UploadedList.ScheduleMessageList.ViewComponent do
  use AppWeb, :live_component

  @impl true
  def render(assigns) do
    ~H"""
    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
      <br />
      <Table.extract id="review" rows={@records} record_title={@record_title}>
        <:col :let={james} label="Row"><%= james["key"] %></:col>

        <:col :let={james} label="Mobile Number"><%= james["col1"] %></:col>

        <:description :let={james} class="text-center"><%= james["message"] %></:description>
      </Table.extract>
    </div>
    """
  end

  @impl true
  def update(%{records: records, title: record_title}, socket) do
    new_records =
      Jason.decode!(records)
      |> Enum.sort(&(&1["key"] < &2["key"]))

    {
      :ok,
      socket
      |> assign(:records, new_records)
      |> assign(:record_title, record_title)
    }
  end
end
