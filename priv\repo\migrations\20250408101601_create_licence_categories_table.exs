defmodule App.Repo.Migrations.CreateLicenceCategoriesTable do
  use Ecto.Migration

  def change do
    create table(:license_categories) do
      add :name, :string
      add :type, :string
      add :description, :string
      add :color, :string
      add :status, :integer, default: 1

      timestamps()
    end

    create table(:license_categories_data) do
      add :license_field_id, references(:license_fields, on_delete: :nothing)
      add :categories_id, references(:license_categories, on_delete: :nothing)
      add :status, :integer, default: 1

      timestamps()
    end

    alter table(:licenses) do
      add :categories_id, references(:license_categories, on_delete: :nothing)
    end

    alter table(:user_license_mapping) do
      add :categories_id, references(:license_categories, on_delete: :nothing)
    end

    create unique_index(:license_categories, [:name])

    create(index(:license_categories_data, [:license_field_id, :categories_id]))
    create(index(:licenses, [:categories_id]))
  end
end
