defmodule App.Service.Export.AdminAccessRoleNoneRoleUserAccessPdf do
  @moduledoc false

  alias App.Service.Table.AddRoleUsers

  alias App.Service.Export.{
    Functions
  }

  def index(payload) do
    results =
      AddRoleUsers.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "email" => data.email,
          "first_name" => data.first_name,
          "last_name" => data.last_name,
          "status" => Functions.table_numeric_status(data.status),
          "system_role" => data.system_role,
          "access_role" => data.access_role
        }
      end)
      |> Enum.map(fn data ->
        """
         <tr>
            <td style="text-align: center;">{{ email }}</td>
            <td style="text-align: center;">{{ first_name }}</td>
            <td style="text-align: center;">{{ last_name }}</td>
            <td style="text-align: center;">{{ status }}</td>
            <td style="text-align: center;">{{ system_role }}</td>
            <td style="text-align: center;">{{ access_role }}</td>

         </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/admin_access_role_none_role_user_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Admin Access Role None Role User",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end
