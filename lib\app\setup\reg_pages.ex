defmodule App.SetUp.RegPages do
  alias App.Registration.Pages
  alias App.Registration.ApplicationSteps
  alias App.Repo

  defp run_seeds_db(schema) do
    Repo.insert!(schema)
  end

  def init({form_1, form_2, form_4, form_6, form_8, form_9, form_10, form_11, form_12}) do
    text =
      run_seeds_db(%Pages{
        name: "Registration Information",
        url: "/license/registration/fields/",
        icon: "hero-clipboard-document",
        status: 1
      })

    uploads =
      run_seeds_db(%Pages{
        name: "Document Uploads",
        url: "/license/registration/uploads/",
        icon: "hero-document-arrow-up",
        status: 1
      })

    payments =
      run_seeds_db(%Pages{
        name: "Payments",
        url: "/license/registration/payments/",
        icon: "hero-credit-card",
        status: 1
      })

    summary =
      run_seeds_db(%Pages{
        name: "Summary & Review",
        url: "/license/registration/summary/",
        icon: "hero-clipboard-document-check",
        status: 1
      })

    [
      %{
        license_id: form_1.id,
        step_id: text.id,
        number: 1,
        status: 1
      },
      %{
        license_id: form_1.id,
        step_id: uploads.id,
        number: 2,
        status: 1
      },
      %{
        license_id: form_1.id,
        step_id: payments.id,
        number: 3,
        status: 1
      },
      %{
        license_id: form_1.id,
        step_id: summary.id,
        number: 4,
        status: 1
      },
      %{
        license_id: form_2.id,
        step_id: text.id,
        number: 1,
        status: 1
      },
      %{
        license_id: form_2.id,
        step_id: uploads.id,
        number: 2,
        status: 1
      },
      %{
        license_id: form_2.id,
        step_id: payments.id,
        number: 3,
        status: 1
      },
      %{
        license_id: form_2.id,
        step_id: summary.id,
        number: 4,
        status: 1
      },
      %{
        license_id: form_4.id,
        step_id: text.id,
        number: 1,
        status: 1
      },
      %{
        license_id: form_4.id,
        step_id: uploads.id,
        number: 2,
        status: 1
      },
      %{
        license_id: form_4.id,
        step_id: payments.id,
        number: 3,
        status: 1
      },
      %{
        license_id: form_4.id,
        step_id: summary.id,
        number: 4,
        status: 1
      },
      %{
        license_id: form_6.id,
        step_id: text.id,
        number: 1,
        status: 1
      },
      %{
        license_id: form_6.id,
        step_id: uploads.id,
        number: 2,
        status: 1
      },
      %{
        license_id: form_6.id,
        step_id: payments.id,
        number: 3,
        status: 1
      },
      %{
        license_id: form_6.id,
        step_id: summary.id,
        number: 4,
        status: 1
      },
      %{
        license_id: form_8.id,
        step_id: text.id,
        number: 1,
        status: 1
      },
      %{
        license_id: form_8.id,
        step_id: uploads.id,
        number: 2,
        status: 1
      },
      %{
        license_id: form_8.id,
        step_id: payments.id,
        number: 3,
        status: 1
      },
      %{
        license_id: form_8.id,
        step_id: summary.id,
        number: 4,
        status: 1
      },
      %{
        license_id: form_9.id,
        step_id: text.id,
        number: 1,
        status: 1
      },
      %{
        license_id: form_9.id,
        step_id: uploads.id,
        number: 2,
        status: 1
      },
      %{
        license_id: form_9.id,
        step_id: payments.id,
        number: 3,
        status: 1
      },
      %{
        license_id: form_9.id,
        step_id: summary.id,
        number: 4,
        status: 1
      },
      %{
        license_id: form_10.id,
        step_id: text.id,
        number: 1,
        status: 1
      },
      %{
        license_id: form_10.id,
        step_id: uploads.id,
        number: 2,
        status: 1
      },
      %{
        license_id: form_10.id,
        step_id: payments.id,
        number: 3,
        status: 1
      },
      %{
        license_id: form_10.id,
        step_id: summary.id,
        number: 4,
        status: 1
      },
      %{
        license_id: form_11.id,
        step_id: text.id,
        number: 1,
        status: 1
      },
      %{
        license_id: form_11.id,
        step_id: uploads.id,
        number: 2,
        status: 1
      },
      %{
        license_id: form_11.id,
        step_id: payments.id,
        number: 3,
        status: 1
      },
      %{
        license_id: form_11.id,
        step_id: summary.id,
        number: 4,
        status: 1
      },
      %{
        license_id: form_12.id,
        step_id: text.id,
        number: 1,
        status: 1
      },
      %{
        license_id: form_12.id,
        step_id: uploads.id,
        number: 2,
        status: 1
      },
      %{
        license_id: form_12.id,
        step_id: payments.id,
        number: 3,
        status: 1
      },
      %{
        license_id: form_12.id,
        step_id: summary.id,
        number: 4,
        status: 1
      }
    ]
    |> Enum.each(fn data ->
      Repo.insert!(struct(ApplicationSteps, data))
    end)
  end
end
