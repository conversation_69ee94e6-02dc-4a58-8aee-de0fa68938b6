defmodule App.FilesTest do
  use App.DataCase

  alias App.Files

  describe "uploaded_files" do
    alias App.Files.UploadedFile

    import App.FilesFixtures

    @invalid_attrs %{approver_id: nil, content_type: nil, filename: nil, path: nil}

    test "list_uploaded_files/0 returns all uploaded_files" do
      uploaded_file = uploaded_file_fixture()
      assert Files.list_uploaded_files() == [uploaded_file]
    end

    test "get_uploaded_file!/1 returns the uploaded_file with given id" do
      uploaded_file = uploaded_file_fixture()
      assert Files.get_uploaded_file!(uploaded_file.id) == uploaded_file
    end

    test "create_uploaded_file/1 with valid data creates a uploaded_file" do
      valid_attrs = %{
        approver_id: 42,
        content_type: "some content_type",
        filename: "some filename",
        path: "some path"
      }

      assert {:ok, %UploadedFile{} = uploaded_file} = Files.create_uploaded_file(valid_attrs)
      assert uploaded_file.approver_id == 42
      assert uploaded_file.content_type == "some content_type"
      assert uploaded_file.filename == "some filename"
      assert uploaded_file.path == "some path"
    end

    test "create_uploaded_file/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Files.create_uploaded_file(@invalid_attrs)
    end

    test "update_uploaded_file/2 with valid data updates the uploaded_file" do
      uploaded_file = uploaded_file_fixture()

      update_attrs = %{
        approver_id: 43,
        content_type: "some updated content_type",
        filename: "some updated filename",
        path: "some updated path"
      }

      assert {:ok, %UploadedFile{} = uploaded_file} =
               Files.update_uploaded_file(uploaded_file, update_attrs)

      assert uploaded_file.approver_id == 43
      assert uploaded_file.content_type == "some updated content_type"
      assert uploaded_file.filename == "some updated filename"
      assert uploaded_file.path == "some updated path"
    end

    test "update_uploaded_file/2 with invalid data returns error changeset" do
      uploaded_file = uploaded_file_fixture()

      assert {:error, %Ecto.Changeset{}} =
               Files.update_uploaded_file(uploaded_file, @invalid_attrs)

      assert uploaded_file == Files.get_uploaded_file!(uploaded_file.id)
    end

    test "delete_uploaded_file/1 deletes the uploaded_file" do
      uploaded_file = uploaded_file_fixture()
      assert {:ok, %UploadedFile{}} = Files.delete_uploaded_file(uploaded_file)
      assert_raise Ecto.NoResultsError, fn -> Files.get_uploaded_file!(uploaded_file.id) end
    end

    test "change_uploaded_file/1 returns a uploaded_file changeset" do
      uploaded_file = uploaded_file_fixture()
      assert %Ecto.Changeset{} = Files.change_uploaded_file(uploaded_file)
    end
  end
end
