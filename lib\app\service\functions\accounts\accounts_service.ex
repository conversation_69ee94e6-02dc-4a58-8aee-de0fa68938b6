defmodule App.Service.ServiceAccountsMaintenance.Functions do
  alias App.AccountMapping

  def index(socket, attrs, function \\ "change_status") do
    account = AccountMapping.get_account!(attrs["id"])

    cond do
      function == "change_status" ->
        AccountMapping.change_account_status(socket, attrs, account)
    end
  end

  def create(socket, attrs) do
    AccountMapping.create_account(socket, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end

  def update(socket, account, attrs) do
    AccountMapping.update_account(socket, account, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end
end
