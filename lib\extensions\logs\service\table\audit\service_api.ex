defmodule App.Service.Logs.ApiLogs do
  @moduledoc false
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false
  alias Logs.Audit.Api
  alias Logs.LogRepo, as: Repo

  @pagination [page_size: 10]

  def index(params) do
    compose_query(params)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def export(params) do
    compose_query(params)
    |> Repo.all()
  end

  defp compose_query(params) do
    Api
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(String.trim(value)))

      {"service", value}, query when byte_size(value) > 0 ->
        where(query, [a], like(a.service, ^sanitize_term(value)))

      {"reference", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          like(a.reference, ^sanitize_term(value)) or
            like(a.external_reference, ^sanitize_term(value))
        )

      {"ip_address", value}, query when byte_size(value) > 0 ->
        where(query, [a], a.ip_address == ^value)

      {"start_date", value}, query when byte_size(value) > 0 ->
        naive_datetime = NaiveDateTime.from_iso8601!("#{value} 00:00:00")
        where(query, [s, c, d], s.inserted_at >= ^naive_datetime)

      {"end_date", value}, query when byte_size(value) > 0 ->
        naive_datetime = NaiveDateTime.from_iso8601!("#{value} 23:59:59")
        where(query, [s, c, d], s.inserted_at <= ^naive_datetime)

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a], %{
      inserted_at: a.inserted_at,
      service: a.service,
      reference: a.reference,
      external_reference: a.external_reference,
      request: a.request,
      endpoint: a.endpoint,
      ip_address: a.ip_address,
      response: a.response,
      id: a.id
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      fragment("lower(?) LIKE lower(?)", a.service, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.reference, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.ip_address, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.endpoint, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.external_reference, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
