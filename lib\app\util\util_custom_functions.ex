defmodule App.Util.CustomFunctions do
  @moduledoc false

  def get_value_from_select_data(attrs, select_list, target, excludes_list \\ []) do
    Enum.map(
      attrs,
      fn {k, v} ->
        cond do
          k in excludes_list ->
            nil

          k == target ->
            %{
              head: String.replace(k, "_id", ""),
              value: get_from_select_tag_value(select_list, v)
            }

          true ->
            %{head: k, value: v}
        end
      end
    )
    |> Enum.filter(&(!is_nil(&1)))
  end

  def get_value_from_select_data2(attrs, select_list, target) do
    Enum.reduce(attrs, %{}, fn {k, v}, new_map ->
      if k == target do
        Map.merge(new_map, %{
          "#{String.replace(k, "_id", "")}" => get_from_select_tag_value(select_list, v),
          "#{k}" => v
        })
      else
        Map.merge(new_map, %{"#{k}" => v})
      end
    end)
  end

  def get_value_from_select_data_pipe(attrs, select_list, target) do
    Enum.map(
      attrs,
      fn data ->
        cond do
          data.head == target ->
            %{head: data.head, value: get_from_select_tag_value(select_list, data.value)}

          true ->
            %{head: data.head, value: data.value}
        end
      end
    )
  end

  def list_with_head_and_value_to_map(list) do
    Enum.reduce(
      list,
      %{},
      fn item, new_map ->
        Map.merge(new_map, %{"#{item.head}" => item.value})
      end
    )
  end

  def get_from_select_tag_value(list, value) do
    Enum.map(
      list,
      fn {k, v} ->
        if to_string(v) == to_string(value), do: k, else: nil
      end
    )
    |> Enum.filter(&(!is_nil(&1)))
    |> Enum.at(0)
  end

  def get_node_response({response, 0}, field) do
    data = Jason.decode!(response)
    if data[field], do: {:ok, data}, else: {:error, data}
  end

  def get_schema_name(schema) do
    schema.__schema__(:source)
  end

  def convert_image_to_base_64(path) do
    base64_image =
      Application.app_dir(:app, "/priv/static" <> path)
      |> File.read!()
      |> Base.encode64()

    file_name = "#{:os.system_time()}"
    file_path = Application.app_dir(:app, "/priv/static/images/base66/")

    if File.exists?(file_path) do
      File.open(file_path <> "#{file_name}", [:write])
      File.write(file_path <> "/#{file_name}", base64_image)
      "/images/base66/#{file_name}"
    else
      File.mkdir(file_path)
      File.open(file_path <> "#{file_name}", [:write])
      File.write(file_path <> "#{file_name}", base64_image)
      "/images/base66/#{file_name}"
    end
  end

  def validate_mobile(mobile) do
    (mobile |> String.length() == 9 and
       mobile |> String.slice(0, 1) |> NumberF.convert_to_int() > 5) or
      (mobile |> String.length() == 10 and mobile |> String.slice(0, 1) == "0") or
      (mobile |> String.length() == 12 and mobile |> String.slice(0, 2) == "26") or
      (mobile |> String.length() == 13 and mobile |> String.contains?("+26"))
  end

  def get_node_response({response, _}, field) do
    data = Jason.decode!(response)
    if data[field], do: {:ok, data}, else: {:error, data}
  end

  def atomic_map_string_key(atomic_map) do
    Jason.encode!(atomic_map)
    |> Jason.decode!()
  end

  def toast_messages(%{assigns: assigns} = socket, message, icon) do
    socket.endpoint.broadcast(
      "sweet_alert:" <> to_string(assigns.live_socket_identifier),
      "sweet_alert:" <> to_string(assigns.live_socket_identifier),
      %{
        message: message,
        icon: icon,
        expression: "normal",
        timer: 7000
      }
    )

    socket
  end

  def file_export(%{assigns: assigns} = socket, payload) do
    socket.endpoint.broadcast(
      "exports:" <> to_string(assigns.live_socket_identifier),
      "exports:" <> to_string(assigns.live_socket_identifier),
      payload
    )

    socket
  end

  def sanitize_params(params) do
    Enum.reduce(params, %{}, fn {k, value}, acc ->
      val =
        cond do
          String.valid?(value) ->
            value
            |> decode_binary()

          true ->
            binary_to_string = fn str ->
              if is_binary(str), do: Enum.join(for <<c::utf8 <- str>>, do: <<c::utf8>>), else: str
            end

            binary_to_string.(value)
        end

      Map.put(acc, k, val)
    end)
  end

  # ascii_data = :binary.bin_to_list(binary_part(binary_data, 0, 8)) |> List.to_string()
  def decode_binary(binary_data, n \\ 0) do
    if is_binary(binary_data) do
      case n do
        0 ->
          for(<<c <- binary_data>>, c in 0..127, into: "", do: <<c>>)
          |> decode_binary(n + 1)

        1 ->
          Enum.join(for <<c::utf8 <- binary_data>>, do: <<c::utf8>>)
          |> decode_binary(n + 1)

        2 ->
          try do
            :binary.bin_to_list(binary_part(binary_data, 0, 8))
            |> List.to_string()
            |> decode_binary(n + 1)
          rescue
            _ -> decode_binary(n + 1)
          end

        _ ->
          binary_data
      end
    else
      binary_data
    end
  end

  def calculate_name_accuracy(name1, name2) do
    name1 = String.downcase(name1 || "")
    name2 = String.downcase(name2 || "")

    longer_name = if String.length(name1) > String.length(name2), do: name1, else: name2
    shorter_name = if longer_name == name1, do: name2, else: name1

    common_chars = count_common_characters(longer_name, shorter_name)

    percentage_accuracy = common_chars / String.length(longer_name) * 100
    Float.round(percentage_accuracy, 2)
  end

  defp count_common_characters(longer_name, shorter_name) do
    Enum.count(String.graphemes(longer_name), fn char ->
      String.contains?(shorter_name, char)
    end)
  end
end
