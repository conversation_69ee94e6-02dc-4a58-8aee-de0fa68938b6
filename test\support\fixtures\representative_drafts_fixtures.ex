defmodule App.RepresentativeDraftsFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `App.RepresentativeDrafts` context.
  """

  @doc """
  Generate a representative_draft.
  """
  def representative_draft_fixture(attrs \\ %{}) do
    {:ok, representative_draft} =
      attrs
      |> Enum.into(%{
        template: "some template"
      })
      |> App.RepresentativeDrafts.create_representative_draft()

    representative_draft
  end
end
