defmodule App.Service.Export.PurchasesServicePdf do
  alias App.Service.Table.ServicePurchases

  alias App.Service.Export.{
    Functions
  }

  def index(assigns, payload) do
    results =
      ServicePurchases.export(assigns, payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "client" => data.client,
          "desc" => data.desc,
          "txb_amount" => NumberF.currency(data.txb_amount, ""),
          "total_vat" => NumberF.currency(data.total_vat, ""),
          "amount_aft_vat" => NumberF.currency(data.amount_aft_vat, ""),
          "unit_price" => NumberF.currency(data.unit_price, ""),
          "bundle" => to_string(data.bundle),
          "total_bundle_load" => to_string(data.total_bundle_load),
          "trn_type" => data.trn_type,
          "created_by" => data.created_by,
          "inserted_at" => to_string(data.inserted_at)
        }
      end)
      |> Enum.map(fn data ->
        """
          <tr>
            <td style="text-align: center;">{{ client }}</td>
            <td style="text-align: center;">{{ desc }}</td>
            <td style="text-align: center;">{{ txb_amount }}</td>
            <td style="text-align: center;">{{ total_vat }}</td>#
            <td style="text-align: center;">{{ amount_aft_vat }}</td>
            <td style="text-align: center;">{{ unit_price }}</td>
            <td style="text-align: center;">{{ bundle }}</td>
            <td style="text-align: center;">{{ total_bundle_load }}</td>
            <td style="text-align: center;">{{ trn_type }}</td>
            <td style="text-align: center;">{{ created_by }}</td>
            <td style="text-align: center;">{{ inserted_at }}</td>
          </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/purchases_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Bundle Purchases",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end
