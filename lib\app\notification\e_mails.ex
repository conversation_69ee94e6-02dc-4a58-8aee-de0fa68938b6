defmodule App.Send.Email do
  @moduledoc false
  alias App.{Http.Send, Management}
  alias Notification.MessageDrafts

  # App.Send.Email.license_status_update("<EMAIL>", "peter")

  def send_prepaid_invoice_mail_notification(data, email) do
    client_details = %{
      client_name: data.client.client_name,
      client_id: data.client.client_id,
      payment_type: data.client.payment_type
    }

    email_formatting(%{
      to: email,
      subject: "SUMMARY REPORT FOR #{Timex.format!(data.period, "{Mfull}, {YYYY}")}"
    })
    |> Map.put(:service, "prepaid_summary_report")
    |> Map.put(:client, client_details)
    |> Map.put(:results, data.results)
    |> Map.put(:period, data.period)
    |> Map.put(:last_trns, data.last_trns)
    |> send_email()
  end

  def send_health_check_report(data, status, email) do
    email_formatting(%{
      to: email,
      subject: "Health Check Notification: #{data.name}"
    })
    |> Map.put(:service, "health_check_report")
    |> Map.put(:status, status)
    |> Map.put(:endpoint, data.endpoint)
    |> Map.put(:name, data.name)
    |> Map.put(:last_seen, data.last_seen)
    |> send_email()
  end

  def send_postpaid_invoice_mail_notification(data, email) do
    client_details = %{
      client_name: data.client.client_name,
      client_id: data.client.client_id,
      payment_type: data.client.payment_type
    }

    email_formatting(%{
      to: email,
      subject: "SUMMARY REPORT FOR #{Timex.format!(data.period, "{Mfull}, {YYYY}")}"
    })
    |> Map.put(:service, "postpaid_summary_report")
    |> Map.put(:client, client_details)
    |> Map.put(:results, data.results)
    |> Map.put(:period, data.period)
    |> Map.put(:payable_bill, data.payable_bill)
    |> send_email()
  end

  def send_email_notification(email, password) do
    email_formatting(%{
      to: email,
      subject: "Account Creation"
    })
    |> Map.put(:service, "client_creation")
    |> Map.put(:password, password)
    |> send_email()
  end

  def client_send_email_notification(email, password, username) do
    email_formatting(%{
      to: email,
      subject: "API Account Credentials"
    })
    |> Map.put(:service, "client_api_credentials")
    |> Map.put(:password, password)
    |> Map.put(:username, username)
    |> send_email()
  end

  # def client_send_email_notification_expiring_date(email, username) do
  #   email_formatting(%{
  #     to: email,
  #     subject: "Client Expiring Date Notification"
  #   })
  #   |> Map.put(:service, "expiring_date_notification")
  #   |> Map.put(:username, username)
  #   |> send_email()
  # end

  def client_send_email_notification_expiring_date(email, client_name, expiring_date, condition) do
    message =
      MessageDrafts.index_email("EXPIRING_DATE_NOTIFICATION", %{
        "client_name" => client_name,
        "expiring_date" => expiring_date,
        "condition" => condition
      })

    email_formatting(%{
      to: email,
      subject: "Client Expiring Date Notification"
    })
    |> Map.put(:service, "html")
    |> Map.put(:html, message)
    |> send_email()
  end

  def client_send_email(email, client_name, expiring_date, condition) do
    message =
      MessageDrafts.index_email("EXPIRING_DATE_CLIENT_NOTIFICATION", %{
        "client_name" => client_name,
        "expiring_date" => expiring_date,
        "condition" => condition
      })

    email_formatting(%{
      to: email,
      subject: "Expiring Date  Condition Notification"
    })
    |> Map.put(:service, "html")
    |> Map.put(:html, message)
    |> send_email()
  end

  def client_send_email_to_admin(
        email,
        client_name,
        expiring_date,
        condition,
        comments,
        user_name
      ) do
    message =
      MessageDrafts.index_email("SEND_CONDITION_REASON_TO_ADMIN", %{
        "client_name" => client_name,
        "comments" => comments,
        "expiring_date" => expiring_date,
        "condition" => condition,
        "user_name" => user_name
      })

    email_formatting(%{
      to: email,
      subject: "Expiring Date Condition Notification"
    })
    |> Map.put(:service, "html")
    |> Map.put(:html, message)
    |> send_email()
  end

  def confirm_password_reset(password, email) do
    email_formatting(%{
      to: email,
      subject: "Password Reset"
    })
    |> Map.put(:service, "password_reset")
    |> Map.put(:password, password)
    |> send_email()
  end

  def license_status_update(email, client_name, status) do
    message =
      MessageDrafts.index_email("LICENCE_STATUS_UPDATE", %{
        client_name: client_name,
        status: status
      })

    email_formatting(%{
      to: email,
      subject: "Status Update for Your License"
    })
    |> Map.put(:service, "html")
    |> Map.put(:html, message)
    |> send_email()
  end

  def credit_debit_mail_notification(email, amount, trn_type, date, current_bundle) do
    email_formatting(%{
      to: email,
      subject: "Credit/Debit Alert"
    })
    |> Map.put(:service, "credit_debit_alert")
    |> Map.put(:email, email)
    |> Map.put(:amount, amount)
    |> Map.put(:trn_type, trn_type)
    |> Map.put(:date, date)
    |> Map.put(:current_bundle, current_bundle)
    |> send_email()
  end

  def send_email_user_password(email, password) do
    email_formatting(%{
      to: email,
      subject: "Probase SMS Password"
    })
    |> Map.put(:service, "")
    |> Map.put(:password, password)
    |> send_email()
  end

  def send_otp_notification(email, otp) do
    email_formatting(%{
      to: email,
      subject: "OTP Notification"
    })
    |> Map.put(:service, "otp_notification")
    |> Map.put(:otp, otp)
    |> send_email()
  end

  def send_reg_notification(email, client_name) do
    message =
      MessageDrafts.index_email("LICENCE_REGISTRATION", %{
        client_name: client_name
      })

    email_formatting(%{
      to: email,
      subject: "SEC Licence Registration"
    })
    |> Map.put(:service, "html")
    |> Map.put(:html, message)
    |> send_email()
  end

  def send_email(email_data) do
    Management.get_api_management_by_name_and_type("EMAIL", "SERVER")
    |> case do
      nil ->
        IO.puts("No email server found")

      email_server ->
        data = email_server.data

        Send.post_json_request(
          email_server.base_url,
          Map.merge(email_data, %{
            sender: data["sender"]
          })
        )
    end
  end

  def email_formatting(email) do
    data = Management.get_data_by_name("EMAIL")

    Map.merge(email, %{
      sender: data["sender"],
      user: data["user"]
    })
  end
end
