defmodule Logs.Audit.Access do
  use AppWeb, :schema

  alias App.Accounts.User

  schema "logs_access" do
    field :description, :string
    field :page, :string
    field :page_access, :boolean, default: false
    field :route, :string
    field :session_id, :string

    belongs_to :user, User

    timestamps()
  end

  @doc false
  def changeset(access, attrs) do
    access
    |> cast(attrs, [:session_id, :route, :page_access, :page, :description, :user_id])
    |> validate_required([:session_id, :route, :page, :user_id])
  end
end
