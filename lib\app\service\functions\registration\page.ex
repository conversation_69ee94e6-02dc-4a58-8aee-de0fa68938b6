defmodule App.Service.Table.Registration.Page.Functions do
  alias App.Registration

  def index(socket, attrs, function \\ "change_status") do
    record = Registration.get_page!(attrs["id"])

    cond do
      function == "change_status" ->
        Registration.change_page_status(socket, attrs, record)
    end
  end

  def create(socket, attrs) do
    Registration.create_pages_data(socket, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end
end
