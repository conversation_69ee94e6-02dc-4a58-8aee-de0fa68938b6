defmodule App.Roles.AccessRoles do
  use AppWeb, :schema
  alias App.{Accounts.User, Roles.DepartmentRoles, Roles.AccessRolePermission}

  schema "access_roles" do
    field :name, :string
    field :description, :string
    field :system_gen, :boolean, default: false
    field :editable, :boolean, default: true
    field :user_interface, :boolean, default: false
    field :status, :integer, default: 1
    field :deleted_at, :naive_datetime

    belongs_to :department_role, DepartmentRoles
    belongs_to :created_by_user, User, foreign_key: :created_by
    belongs_to :updated_by_user, User, foreign_key: :updated_by

    has_many :users, User, foreign_key: :role_id
    has_many :access_role_permissions, AccessRolePermission, foreign_key: :access_role_id
    has_many :permissions, through: [:access_role_permissions, :permission]

    timestamps()
  end

  def changeset(access_role, attrs) do
    access_role
    |> cast(attrs, [
      :name,
      :description,
      :department_role_id,
      :system_gen,
      :editable,
      :user_interface,
      :status,
      :created_by,
      :updated_by
    ])
    |> validate_required([:name, :description, :department_role_id])
    |> validate_length(:name, min: 3, max: 50)
    |> validate_length(:description, min: 3, max: 150)
    |> unique_constraint([:name, :department_role_id])
  end
end
