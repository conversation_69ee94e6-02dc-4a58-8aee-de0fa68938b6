// Import UI related hooks
import LiveViewPushEvent from "./live_push";
import {LocalTime} from "./local_time";
import ScrollLock from "./scroll_lock";
import {<PERSON><PERSON><PERSON>, PieChart} from "./charts";
import {SweetAlert2} from "./sweetalert2";
import {Transition} from "./transistion";
import {menuDropDown, sideBarToggle} from "./left_side_menu";
import ActiveNav from "./active_nav";

// Import form and input related hooks
import FormRedirection from "./form_redirection";
import {OTPInput} from "./otp";
import {
    nrcInputHook,
    validateNumberHook,
    validateNumberHook2,
    validateAmountsHook,
    validatePassportHook,
    messageCounter,
    validateTpinHook,
    validatesSenderHook,
    validateIntegerHook
} from "./input_hook";
import CurrencyInput from "./currency_input";
import {LiveSelect} from "./live_select.js";

// Import utility hooks
import DeviceUuid from "./device_uuid";
import {CopyInnerHTML} from "./copy_to_clipboard";
import SortableJs from "./sortable";
import {PrintContent, PrintIframe} from "./print_content";
import {ResizeIframe} from "./iframe";
import { ActivityTracker } from "./trackers";
import ReadNotification from "./read_notification";
import {RichTextEditor} from "./quill";
import {EditableHook} from "./edit";

export default {
    // UI related hooks
    LiveViewPushEvent,
    LocalTime,
    ScrollLock,
    BarChart,
    PieChart,
    SweetAlert2,
    Transition,
    menuDropDown,
    sideBarToggle,
    ActiveNav,
    
    // Form and input related hooks
    FormRedirection,
    OTPInput,
    nrcInputHook,
    validateNumberHook,
    validateNumberHook2,
    validateAmountsHook,
    validatePassportHook,
    messageCounter,
    validateTpinHook,
    validatesSenderHook,
    validateIntegerHook,
    CurrencyInput,
    LiveSelect,
    
    // Utility hooks
    DeviceUuid,
    CopyInnerHTML,
    Sortable: SortableJs,
    PrintContent,
    PrintIframe,
    ResizeIframe,
    ActivityTracker,
    ReadNotification,
    RichTextEditor,
    EditableHook
};
