defmodule App.Service.LicenceApplications.Functions do
  alias App.{Licenses, Utilities, LicenseConditions}

  def index(socket, params, record, associates, function \\ "change_status") do
    category = Licenses.get_license_category_by_id(record.license_id)

    approval_level_list = Utilities.get_approval_level_list_by_category(category)

    status =
      case Enum.find_index(approval_level_list, &(&1 == record.status)) do
        nil ->
          record.status

        index ->
          new_index =
            case function do
              "approve" -> index + 1
              "decline" -> index - 1
              _ -> index
            end

          cond do
            new_index <= 1 ->
              # This indicates that the application has been declined and is sent to the client
              "-1"

            new_status = Enum.at(approval_level_list, new_index) ->
              new_status

            true ->
              record.status
          end

          #          new_status = Enum.at(approval_level_list, new_index)
          #
          #          if new_status == nil do
          #            record.status
          #          else
          #            new_status
          #          end
      end

    attrs = Map.put(params, "status", status)

    cond do
      function == "approve" ->
        has_conditions = LicenseConditions.license_has_conditions(record.id)

        cond do
          status == record.status && !has_conditions ->
            Licenses.final_approval(socket, attrs, record, associates)

          status == record.status && has_conditions ->
            Licenses.final_approval(socket, attrs, record, associates)

          true ->
            Licenses.approve_application(socket, attrs, record)
        end

      function == "decline" ->
        Licenses.decline_application(socket, attrs, record, associates)

      function == "check_amount" ->
        Licenses.perfom_debit(socket, attrs, record, associates)
    end
  end
end
