<div>
  <div class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Application Summary View -->
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">Application Summary</h1>
      </div>

      <div id="toBePrinted">
        <div class="bg-white shadow rounded-xl overflow-hidden border border-gray-200 mb-6">
          <div class="flex flex-col lg:flex-row">
            <div class="lg:w-full">
              <!-- Controls Bar -->
              <div class="flex justify-center items-center gap-5 p-4 bg-gray-50 border-b border-gray-200 no-print">
                <!-- Print button -->
                <button
                  type="button"
                  phx-click="export_pdf"
                  phx-target={@myself}
                  phx-value-file_name="MainLicence"
                  class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Print"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <polyline points="6 9 6 2 18 2 18 9" />
                    <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2" />
                    <rect width="12" height="8" x="6" y="14" />
                  </svg>
                </button>
                <!-- Word download link -->
                <button
                  type="button"
                  phx-click="export_word"
                  phx-target={@myself}
                  phx-value-file_name="MainLicence"
                  class="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
                  title="Download Word Document"
                >
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M28.806,3H9.705A1.192,1.192,0,0,0,8.512,4.191h0V9.5l11.069,3.25L30,9.5V4.191A1.192,1.192,0,0,0,28.806,3Z"
                      fill="#41a5ee"
                    /> <path d="M30,9.5H8.512V16l11.069,1.95L30,16Z" fill="#2b7cd3" />
                    <path d="M8.512,16v6.5L18.93,23.8,30,22.5V16Z" fill="#185abd" />
                    <path
                      d="M9.705,29h19.1A1.192,1.192,0,0,0,30,27.809h0V22.5H8.512v5.309A1.192,1.192,0,0,0,9.705,29Z"
                      fill="#103f91"
                    />
                    <path
                      d="M3.194,8.85H15.132a1.193,1.193,0,0,1,1.194,1.191V21.959a1.193,1.193,0,0,1-1.194,1.191H3.194A1.192,1.192,0,0,1,2,21.959V10.041A1.192,1.192,0,0,1,3.194,8.85Z"
                      fill="#2368c4"
                    />
                    <path
                      d="M6.9,17.988c.023.184.039.344.046.481h.028c.01-.13.032-.287.065-.47s.062-.338.089-.465l1.255-5.407h1.624l1.3,5.326a7.761,7.761,0,0,1,.162,1h.022a7.6,7.6,0,0,1,.135-.975l1.039-5.358h1.477l-1.824,7.748H10.591L9.354,14.742q-.054-.222-.122-.578t-.084-.52H9.127q-.021.189-.084.561c-.042.249-.075.432-.1.552L7.78,19.871H6.024L4.19,12.127h1.5l1.131,5.418A4.469,4.469,0,0,1,6.9,17.988Z"
                      fill="#fff"
                    />
                  </svg>
                </button>
                <!-- View License Form button -->
                <button
                  type="button"
                  phx-click="view_licence_summary"
                  phx-target={@myself}
                  phx-value-id={@record.license_id}
                  class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                  title="View License Form"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                    <polyline points="14 2 14 8 20 8" /> <line x1="16" y1="13" x2="8" y2="13" />
                    <line x1="16" y1="17" x2="8" y2="17" /> <polyline points="10 9 9 9 8 9" />
                  </svg>
                </button>
                <!-- View Details button -->
                <button
                  type="button"
                  phx-click="view_details"
                  phx-target={@myself}
                  class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                  title="View Details"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <circle cx="12" cy="12" r="10" /> <line x1="12" y1="16" x2="12" y2="12" />
                    <line x1="12" y1="8" x2="12.01" y2="8" />
                  </svg>
                </button>
                <!-- Add Condition button -->
                <button
                  type="button"
                  phx-click="view_condition"
                  phx-target={@myself}
                  class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                  title="License Condition"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <circle cx="12" cy="12" r="10" /> <line x1="12" y1="8" x2="12" y2="16" />
                    <line x1="8" y1="12" x2="16" y2="12" />
                  </svg>
                </button>
              </div>

              <div class="flex-1 p-6 bg-white" id="editor-container" phx-update="ignore">
                <div
                  id={@editors["id"]}
                  phx-hook="RichTextEditor"
                  phx-update="ignore"
                  class="relative w-full h-full border rounded-lg overflow-hidden border-gray-300 min-h-[500px]"
                >
                </div>
              </div>

              <div class="flex items-center justify-between p-6 border-t border-brand-10 no-print">
                <%= if @approval_level && @record.status > 1 && !@record.condition_tracking do %>
                  <div class="flex items-center gap-3">
                    <%= if @edited == "hide" do %>
                      <.button
                        type="button"
                        phx-click="edit_form"
                        phx-target={@myself}
                        phx-value-id={@editors["id"]}
                        class="flex items-center gap-2 px-5 py-2 rounded-md bg-brand-2 text-white hover:bg-brand-1 transition-colors"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                        Edit
                      </.button>
                    <% else %>
                      <.button
                        type="button"
                        phx-click="save_edits"
                        phx-target={@myself}
                        phx-value-license_id={@record.license_id}
                        class="flex items-center gap-2 px-5 py-2 rounded-md bg-brand-10 text-white hover:bg-indigo-700 transition-colors"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                          />
                        </svg>
                        Save Changes
                      </.button>
                    <% end %>
                  </div>
                <% end %>
              </div>
              <!-- Associated editors -->
            </div>
            <!-- Right sidebar in summary view -->
            <div class="lg:w-80 p-6 border-l border-gray-200 bg-gray-50 no-print">
              <!-- Attachments Section -->
              <div class="bg-white shadow rounded-xl overflow-hidden border border-gray-200 mb-6">
                <div class="bg-gradient-to-r from-brand-10 to-brand-2 px-6 py-4">
                  <h2 class="text-lg font-bold text-white">Attachments</h2>
                </div>

                <div class="p-4 space-y-2">
                  <%= for field <- @license_data do %>
                    <%= if field.field_type == "upload" do %>
                      <%= if is_nil(field.field_dependents) ||
                            field.dependent_selection == (if !Enum.empty?(field.field_dependents),
                              do: @record.data[List.first(field.field_dependents)]) do %>
                        <div class="flex items-center space-x-2 py-3 px-3 border-b border-gray-100 last:border-0 hover:bg-gray-50 rounded-md transition-all">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-5 w-5 text-brand-2"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>

                          <button
                            phx-value-doc_name={field.field_label}
                            phx-value-path={@field_data |> Map.get(field.field_name)}
                            phx-click="view_document"
                            phx-target={@myself}
                            class="text-sm font-medium text-gray-700 hover:text-brand-10 transition-colors flex-1 text-left"
                          >
                            <%= field.field_label %>
                          </button>
                        </div>
                      <% end %>
                    <% end %>
                  <% end %>

                  <%= if Enum.empty?(@license_data) || !Enum.any?(@license_data, fn field ->
                        field.field_type == "upload"
                      end) do %>
                    <div class="p-4 text-center text-gray-500 italic">
                      No attachments available
                    </div>
                  <% end %>
                </div>
              </div>
              <!-- Uploaded Files Section -->
              <div class="bg-white shadow rounded-xl overflow-hidden border border-gray-200">
                <div class="bg-gradient-to-r from-brand-10 to-brand-2 px-6 py-4">
                  <h2 class="text-lg font-bold text-white">Uploaded Files</h2>
                </div>

                <.simple_form
                  for={@upload_form}
                  id="card-form"
                  phx-change="validate_file"
                  phx-submit="upload"
                  class="p-4"
                >
                  <AppWeb.DocumentComponent.document_upload
                    uploads={@uploads}
                    db_files={@db_files}
                  />
                  <%= if @uploads.file.entries != [] do %>
                    <div class="flex items-center justify-center mt-4">
                      <.button
                        type="submit"
                        class="px-4 py-2 bg-brand-10 hover:bg-brand-2 text-white rounded-lg transition"
                      >
                        Upload Files
                      </.button>
                    </div>
                  <% end %>
                </.simple_form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <Model.fullscreen
    :if={@action in [:view_document]}
    id="view_document-modal"
    title={@doc_name}
    show
    return_to="close_model"
  >
    <iframe
      src={@document}
      id="document_iframe"
      title="document"
      style="width: 100%;"
      height="700"
      name="DOCUMENT"
    >
    </iframe>
  </Model.fullscreen>

  <Model.fullscreen
    :if={@action in [:view_licence_form]}
    id="view_licence-modal"
    title="License Form"
    show
    return_to="close_model"
  >
    <iframe
      src={@licence_form}
      id="licence_iframe"
      title="License"
      style="width: 100%;"
      height="700"
      name="License"
    >
    </iframe>
  </Model.fullscreen>

  <Model.confirmation_model
    :if={@action == :confirm}
    show
    id="confirmation_model-modal"
    model_title={@confirmation_model_title}
    body_text={@confirmation_model_text}
    agree_function={@confirmation_model_agree}
    reject_function={@confirmation_model_reject}
    params={Jason.encode!(@confirmation_model_params)}
    icon={@confirmation_model_icon}
  />
  <Model.confirmation_with_des
    :if={@action == :confirm_with_des}
    show
    id="confirmation_with_des-modal"
    model_title={@confirmation_model_title}
    body_text={@confirmation_model_text}
    agree_function={@confirmation_model_agree}
    reject_function={@confirmation_model_reject}
    params={Jason.encode!(@confirmation_model_params)}
    icon={@confirmation_model_icon}
  />
  <Model.confirmation_with_comments
    :if={@action == :confirmation_with_comments}
    show
    id="confirmation_with_comments-modal"
    model_title={@confirmation_model_title}
    body_text={@confirmation_model_text}
    agree_function={@confirmation_model_agree}
    reject_function={@confirmation_model_reject}
    params={Jason.encode!(@confirmation_model_params)}
    icon={@confirmation_model_icon}
  />
  <Model.fullscreen
    :if={@action == :view_details}
    id="user-modal"
    show
    return_to="close_model"
    title={@page_title}
  >
    <.live_component
      module={AppWeb.LicenceDetailsLive.ViewComponent}
      id={@current_user.id}
      title={@page_title}
      action={@action}
      license_data={@license_data}
      record={@record}
      field_data={@field_data}
      current_user={@current_user}
    />
  </Model.fullscreen>

  <Model.small
    :if={@action == :view_conditions}
    id="user-modal"
    show
    return_to="close_model"
    title={@page_title}
  >
    <.live_component
      module={AppWeb.LicenceDetailsLive.ConditionComponent}
      id={@current_user.id}
      title={@page_title}
      action={@action}
      record={@record}
      browser_info={@browser_info}
      current_user={@current_user}
    />
  </Model.small>
</div>
