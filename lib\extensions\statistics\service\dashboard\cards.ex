defmodule App.Service.Table.MonthlyReports do
  def dashboard() do
    results = Reports.Context.dashboard_results()

    summary =
      prepare_dash_result(results)
      |> generate_stats_results()

    totals = get_totals(summary)
    local = get_total_traffic(results, "LOCAL")
    intl = get_total_traffic(results, "INTL")

    %{totals: totals, summary: summary, local: local, intl: intl}
  end

  defp prepare_dash_result(results) do
    Enum.reduce(default_dashboard(), results, fn item, acc ->
      filtered = Enum.filter(acc, &(&1.day == item.day))
      if item not in acc && Enum.empty?(filtered), do: [item | acc], else: acc
    end)
    |> Enum.sort_by(& &1.day)
  end

  def get_total_traffic(results, type) do
    result = Enum.filter(results, &(&1.traffic == type))

    with false <- Enum.empty?(result) do
      Enum.reduce(result, &%{&1 | count: &1.count + &2.count}).count
    else
      _ -> 0
    end
  end

  def get_totals(results) do
    ~w(delivered failed sent)
    |> Enum.map(fn status ->
      {status, Enum.reduce(results, &%{&1 | status => &1[status] + &2[status]})[status]}
    end)
    |> Enum.into(%{})
  end

  def generate_stats_results(results) do
    results = Enum.group_by(results, & &1.day)

    for {key, values} <- results do
      %{
        "date" => key,
        "failed" => get_status_value(values, "FAILED"),
        "sent" => get_status_value(values, "SENT"),
        "pending" => get_status_value(values, "PENDING"),
        "invalid" => get_status_value(values, "Invalid phone number"),
        "system_failure" => get_status_value(values, "SYSTEM_FAILURE"),
        "delivered" => get_status_value(values, "DELIVERED")
      }
    end
  end

  def default_dashboard do
    today = Date.utc_today()
    days = Date.days_in_month(today)

    Date.range(%{today | day: 1}, %{today | day: days})
    |> Enum.map(&%{count: 0, day: "#{&1}", status: nil, traffic: nil})
  end

  def get_status_value(values, status) do
    result = Enum.filter(values, &(&1.status == status))

    with false <- Enum.empty?(result) do
      Enum.reduce(result, &%{&1 | count: &1.count + &2.count}).count
    else
      _ -> 0
    end
  end
end
