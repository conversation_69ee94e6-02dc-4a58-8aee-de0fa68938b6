export const menuDropDown = {
    mounted() {
        const subBtn = this.el;
        const subMenu = subBtn.nextElementSibling;
        const dropdown = subBtn.querySelector('.dropdown');

        this.clickHandler = () => {
            requestAnimationFrame(() => {
                dropdown.classList.toggle('rotate');
                subMenu.style.display = subMenu.style.display === 'block' ? 'none' : 'block';
            });
        };

        subBtn.addEventListener('click', this.clickHandler);
    },
    destroyed() {
        this.el.removeEventListener('click', this.clickHandler);
    }
}

export const sideBarToggle = {
    mounted() {
        const btn = this.el;
        const sidebar = document.getElementById('sidebar');
        const body = document.getElementById('main-body');
        const logo = document.getElementById('sub-logo');
        const close = document.getElementById('x-close');

        this.clickHandler = () => {
            requestAnimationFrame(() => {
                sidebar.classList.toggle('-translate-x-full');
                body.classList.toggle('md:pl-64');
                logo.classList.toggle('hidden');
                close.classList.toggle('-translate-x-full');
            });
        };

        btn.addEventListener('click', this.clickHandler);
    },
    destroyed() {
        this.el.removeEventListener('click', this.clickHandler);
    }
}
