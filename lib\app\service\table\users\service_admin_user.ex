defmodule App.Service.Table.Users do
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false

  alias App.Accounts.User
  alias App.Repo

  @pagination [page_size: 10]
  # nfs_participates

  def index(params) do
    compose_query(params)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def export(params) do
    compose_query(params)
    |> Repo.all()
  end

  def compose_query(params) do
    User
    |> join(:left, [a], b in assoc(a, :role))
    |> where([a], a.user_type == "STAFF" and a.role_id not in [1])
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"status", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.status, ^sanitize_term(value)))

      {"mobile", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.mobile, ^sanitize_term(value))
        )

      {"email", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.email, ^sanitize_term(value)))

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a, b, c], %{
      id: a.id,
      status: a.status,
      first_name: a.first_name,
      last_name: a.last_name,
      user_type: a.user_type,
      mobile: a.mobile,
      inserted_at: fragment("TO_CHAR (?, 'DD MON YYYY, HH24:MI:SS')", a.inserted_at),
      email: a.email
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a],
      fragment("lower(?) LIKE lower(?)", a.mobile, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.first_name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.email, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.last_name, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
