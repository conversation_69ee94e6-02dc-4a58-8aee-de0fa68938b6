defmodule App.Util.CustomTime do
  @moduledoc false

  def today(), do: Timex.today()

  def local_time() do
    Timex.local()
    |> DateTime.truncate(:second)
    |> DateTime.to_naive()
  end

  def local_date(), do: Timex.now()

  def local_datetime(truncate \\ :second) do
    # :second or :millisecond
    local_date()
    |> DateTime.truncate(truncate)
    |> Timex.to_naive_datetime()
    |> Timex.shift(hours: 2)
  end

  def add_time_to_current_time(time_shift) do
    local_date()
    |> DateTime.truncate(:second)
    |> Timex.to_naive_datetime()
    |> Timex.shift(time_shift)
  end

  def add_time_to_current_time(time, time_shift) do
    time
    |> Timex.to_naive_datetime()
    |> Timex.shift(time_shift)
  end

  def to_time_formatted_length(length) do
    String.pad_leading("#{div(length, 60)}", 2, "0") <>
      ":#{to_time_formatted_seconds(rem(length, 60))}"
  end

  defp to_time_formatted_seconds(s) when s < 10, do: "0#{s}"
  defp to_time_formatted_seconds(s), do: "#{s}"

  def days_left(start_date, number_of_days) do
    deadline = Date.add(start_date, number_of_days)
    today = Date.utc_today()
    max(Date.diff(deadline, today), 0)
  end

  def days_left_message(start_date, number_of_days \\ 90) do
    days = days_left(start_date, number_of_days)

    cond do
      days == 0 -> "The approval deadline has passed."
      days == 1 -> "1 day left"
      true -> "#{days} days left"
    end
  end
end
