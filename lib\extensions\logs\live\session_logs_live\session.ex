defmodule AppWeb.Auth.LogsLive.Session do
  @moduledoc false
  use AppWeb, :live_view
  alias App.Service.Logs.LogsSession, as: TableQuery

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("session_logs-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Session Logs", "Accessed Session Logs Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> assign(live_socket_id: session["live_socket_id"])
        |> assign(showFilter: false)
        |> assign(form: LiveFunctions.filter_form(params))
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Session Logs", "Accessed Session Logs Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_params(params, url, socket) do
    socket.endpoint.broadcast_from!(
      self(),
      "nav:" <> socket.assigns.live_socket_id,
      "change_nav",
      %{uri_path: URI.parse(url).path}
    )

    if connected?(socket), do: send(self(), {:get_list, params})

    {:noreply,
     socket
     |> assign(:params, params)
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "Session Trails")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {:search_records, params} ->
        send(self(), {:get_list, %{"filter" => params}})
        noreply(socket)

      {AppWeb.Component.DateFilter, {:filtered, data}} ->
        send(self(), {:get_list, %{"filter" => data}})

        {
          :noreply,
          socket
          |> assign(:live_action, :index)
          |> apply_action(:index, data)
        }
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, socket) do
    case target do
      "refresh_table" ->
        send(self(), {:get_list, socket.assigns.params})

        assign(socket, :data_loader, true)
        |> noreply()

      "filter_change" ->
        assign(socket, form: LiveFunctions.filter_form(value["filter"]))
        |> noreply()

      "search" ->
        send(self(), {:get_list, value})

        assign(socket, :data_loader, true)
        |> noreply()

      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      "filter" ->
        value = if socket.assigns.showFilter == true, do: false, else: true

        assign(socket, :showFilter, value)
        |> noreply()

      "reset_filter" ->
        socket
        |> assign(form: LiveFunctions.filter_form(%{}))
        |> noreply()

      "search" ->
        send(self(), {:get_list, value})

        assign(socket, :data_loader, true)
        |> noreply()

      "export" ->
        LiveFunctions.export_records(
          socket,
          value,
          "session_logs_service",
          socket.assigns.params
        )

      "close_model" ->
        socket =
          socket
          |> assign(:new_edit_modal, false)
          |> assign(:view_modal, false)
          |> assign(:live_action, :index)
          |> apply_action(:index, value)

        {:noreply, socket}

      _ ->
        {:noreply, socket}
    end
  end

  defp list(socket, params) do
    {
      :noreply,
      assign(socket, :data, TableQuery.index(LivePageControl.create_table_params(socket, params)))
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end
end
