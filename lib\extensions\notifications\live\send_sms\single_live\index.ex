defmodule AppWeb.SendSms.SingleLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias Notification.{Sms, Notification.SmsLogs}
  alias App.{Settings, Notifications.ServerCalls}

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("single_sms-view", assigns.permissions) && !is_nil(assigns.client) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Single SMS", "Accessed Single SMS Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      changeset = Sms.send_change_sms_logs(%SmsLogs{})

      socket =
        assign(socket, data: [])
        |> assign(loader: true)
        |> assign(params: params)
        |> assign_form(changeset)
        |> assign(:senders, Settings.get_senders_by_client_id!(assigns.client.id))
        |> LivePageControl.order_by_composer()
        |> LivePageControl.i_search_composer()
        |> assign(confirmation_model: false)
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(live_socket_id: session["live_socket_id"])

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Single SMS", "Accessed Single SMS Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, socket) do
    case target do
      "validate" ->
        validate(value, socket)

      "submit" ->
        handle_sms(value, socket)

      _ ->
        {:noreply, socket}
    end
  end

  defp validate(%{"sms_logs" => params}, socket) do
    changeset =
      Sms.send_change_sms_logs(%SmsLogs{}, params)
      |> Map.put(:action, :validate)

    socket
    |> assign_form(changeset)
    |> noreply()
  end

  defp handle_sms(%{"sms_logs" => params}, socket) do
    value =
      CustomFunctions.get_value_from_select_data2(params, socket.assigns.senders, "sender_id")[
        "sender"
      ]

    attrs =
      Map.merge(params, %{
        "sender" => value,
        "client_id" => socket.assigns.client.id
      })

    changeset = Sms.send_change_sms_logs(%SmsLogs{}, attrs)

    if changeset.valid? do
      changeset = Sms.send_change_sms_logs(%SmsLogs{}, attrs)
      send(self(), {:send, attrs})

      socket
      |> LiveFunctions.sweet_alert("Processing message", "info")
      |> assign(loader: true)
      |> noreply()
    else
      noreply(LiveFunctions.sweet_alert(socket, "Make sure all fields are filled", "error"))
    end
  end

  @impl true
  def handle_info({:send, attrs}, socket) do
    ServerCalls.api_server("single", Map.put(attrs, "user_id", socket.assigns.current_user.id))
    |> case do
      {:ok, body} ->
        if body["status"] do
          changeset = Sms.send_change_sms_logs(%SmsLogs{}, %{})

          LiveFunctions.sweet_alert(socket, body["message"], "success")
          |> assign(loader: false)
          |> assign_form(Map.put(changeset, :action, :validate))
          |> noreply()
        else
          LiveFunctions.sweet_alert(socket, body["message"], "error")
          |> assign(loader: false)
          |> noreply()
        end

      {:error, message} ->
        LiveFunctions.sweet_alert(socket, message, "error")
        |> assign(loader: false)
        |> noreply()
    end
  end
end
