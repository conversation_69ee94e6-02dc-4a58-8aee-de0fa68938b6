defmodule App.Licenses.ConditionalLevels do
  use Ecto.Schema
  import Ecto.Changeset

  schema "conditional_levels" do
    field :approval_status, :integer
    field :status, :integer, default: 1

    belongs_to :department, App.Roles.DepartmentRoles
    belongs_to(:role, App.Roles.AccessRoles)
    belongs_to :categories, App.Licenses.LicenseCategories

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(condition_levels, attrs) do
    condition_levels
    |> cast(attrs, [
      :approval_status,
      :status,
      :department_id,
      :role_id,
      :categories_id
    ])
    |> validate_required([:approval_status, :department_id, :role_id, :categories_id])
    |> validate_number(:approval_status, greater_than: 0, message: "must be greater than 0")
    |> unsafe_validate_unique([:approval_status], App.Repo)
    |> unique_constraint([:approval_status])
  end
end
