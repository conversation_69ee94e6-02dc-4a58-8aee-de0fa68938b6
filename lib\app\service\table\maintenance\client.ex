defmodule App.Service.Table.ClientMaintenance do
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false

  alias App.Accounts.User
  alias App.Repo

  @pagination [page_size: 10]
  # nfs_participates

  def index(params) do
    compose_query(params)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def export(params) do
    compose_query(params)
    |> Repo.all()
  end

  def compose_query(params) do
    User
    |> where([a], a.user_type == "CLIENT")
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"status", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.status, ^sanitize_term(value)))

      {"last_name", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.last_name, ^sanitize_term(value))
        )

      {"first_name", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.first_name, ^sanitize_term(value))
        )

      {"email", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.email, ^sanitize_term(value))
        )

      {"mobile", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.mobile, ^sanitize_term(value)))

      {"form_number", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.form_number, ^sanitize_term(value))
        )

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a, b, c], %{
      id: a.id,
      status: a.status,
      first_name: a.first_name,
      last_name: a.last_name,
      mobile: a.mobile,
      login_attempts: a.login_attempts,
      last_logon: a.last_logon,
      remote_ip: a.remote_ip,
      email: a.email,
      inserted_at: fragment("TO_CHAR (?, 'DD MON YYYY, HH24:MI:SS')", a.inserted_at)
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a],
      fragment("lower(?) LIKE lower(?)", a.first_name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.mobile, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.last_name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.status, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.email, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
