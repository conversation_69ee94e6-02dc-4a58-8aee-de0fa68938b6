<div class="px-4 sm:px-6 lg:px-8 mt-5">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">
        Pending Attachment Requests
      </h1>
    </div>
  </div>
  <!-- FILTERS -->
  <%= if @showFilter do %>
    <div class="border overflow-hidden animate-slide-in-from-top transition-all ease-in-out duration-500 rounded-lg bg-white my-4 flex flex-col gap-4 items-start">
      <h1 class="px-4 py-2 bg-gray-50 border-b font-medium w-full">Filter Requests</h1>

      <FormJ.filter_form
        for={@form}
        class="w-full px-4"
        id="filter"
        phx-change="filter_change"
        phx-submit="search"
      >
        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 mb-4">
          <FormJ.input_filter
            field={@form[:record_name]}
            type="text"
            label="Record Name"
            placeholder="Enter Record Name"
          />
          <FormJ.input_filter
            field={@form[:status]}
            type="select"
            label="Status"
            prompt="--Select Status--"
            options={[
              {"All", ""},
              {"PENDING APPROVAL", 0},
              {"DECLINED", -1}
            ]}
          />
        </div>

        <p class="text-gray-500 font-medium">Date Filters</p>

        <div class="gap-4 grid sm:grid-cols-2 xl:grid-cols-3 pt-2">
          <FormJ.input_filter field={@form[:start_date]} type="date" label="From" />
          <FormJ.input_filter field={@form[:end_date]} type="date" label="To" />
        </div>

        <div class="flex gap-4 items-center justify-start mt-4 p-4 w-full border-t font-medium">
          <.button type="reset" class="cursor-pointer hover:text-brand-1 py-2">Reset</.button>

          <.button type="submit" class="cursor-pointer hover:text-brand-1 py-2">Search</.button>
        </div>
      </FormJ.filter_form>
    </div>
  <% end %>
  <!-- END OF FILTERS -->
  <div class="mt-8 flow-root">
    <div class="-mx-4 -my-2 sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <Table.main_table id="adverts" rows={@data} params={@params} data_loader={@data_loader}>
          <:col :let={rj} label={table_link(@params, "Registration Date", :inserted_at)}>
            <%= Calendar.strftime(
              NaiveDateTime.add(rj.inserted_at, 7200, :second),
              "%d %B %Y %H:%M:%S"
            ) %>
          </:col>

          <:col :let={rj} label={table_link(@params, "Full Name", :user_name)}>
            <%= rj.user_name %>
          </:col>

          <:col :let={rj} label={table_link(@params, "Email", :email)}>
            <%= rj.email %>
          </:col>

          <:col :let={rj} label={table_link(@params, "Company", :company)}>
            <%= rj.company %>
          </:col>

          <:col :let={rj} label={table_link(@params, "Current Licence", :licence_name)}>
            <%= rj.licence_name %>
          </:col>

          <:action :let={rj}>
            <%= if rj.status == 0 do %>
              <Option.bordered>
                <.link
                  phx-click="process"
                  phx-value-id={rj.id}
                  phx-value-activity="approve"
                  phx-value-status="1"
                  class="w-full text-left text-gray-700 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-white"
                >
                  Confirm
                </.link>

                <.link
                  phx-click="process"
                  phx-value-id={rj.id}
                  phx-value-activity="decline"
                  phx-value-status="-1"
                  class="w-full text-left text-rose-600 block px-4 py-2 text-sm hover:bg-brand-10 hover:text-white"
                >
                  Decline
                </.link>
              </Option.bordered>
            <% end %>
          </:action>
        </Table.main_table>
      </div>
    </div>
  </div>
</div>

<Model.confirmation_model
  :if={@live_action == :confirm}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
<Model.confirmation_with_des
  :if={@live_action == :confirm_with_des}
  show
  id="confirmation_with_des-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
