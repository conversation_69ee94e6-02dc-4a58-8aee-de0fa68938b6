defmodule AppWeb.PageLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias Logs.Audit
  alias App.Table.Registration.Page.Service, as: TableQuery
  alias App.Registration.Pages
  alias App.{Registration}
  # alias App.Service.Registration.ApplicationSteps.Functions
  @impl true

  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("page-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Page", "Accessed Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      filter_data = %{
        "record_name" => nil,
        "status" => nil,
        "start_date" => nil,
        "end_date" => nil
      }
      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> assign(loader: true)
        |> assign(params: params)
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(session_id: session["live_socket_id"])
        |> assign(showFilter: false)
        |> assign(form: useform(filter_data))
        |> LivePageControl.order_by_composer(%{
          "sort_direction" => "asc",
          "sort_field" => "id"
        })
        |> LivePageControl.i_search_composer()

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"page", "Accessed page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  defp useform(data) do
    to_form(
      data,
      as: "filter"
    )
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_list, params})
    {:noreply,
     socket
     |> assign(:params, params)
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "registration pages")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)
  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {:change_record_status, params} ->
        record_status(socket, params)

      {:search_records, params} ->
        send(self(), {:get_list, %{"filter" => params}})
        noreply(socket)

      {AppWeb.Component.DateFilter, {:filtered, data}} ->
        send(self(), {:get_list, %{"filter" => data}})
        {
          :noreply,
          socket
          |> assign(:live_action, :index)
          |> assign(:page_title, "Licence Applications")
        }

      {AppWeb.LiveFunctions, message} ->
        send(self(), {:get_list, %{}})
        {
          :noreply,
          socket
          |> put_flash(:info, message)
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)
  def handle_event_switch(target, value, %{assigns: assigns} = socket) do
    case target do
      "iSearch" ->
        send(self(), {:get_list, value})
        {:noreply, LivePageControl.i_search_composer(socket, Map.delete(value, "_target"))}

      "refresh_table" ->
        send(self(), {:get_list, assigns.params})

        assign(socket, :data_loader, true)
        |> noreply()

      "view_reason" ->
        socket =
          socket
          |> assign(:page_title, "Application Declined")
          |> assign(:application, Licenses.get_licence_details!(value["license_id"]))
          |> assign(:live_action, :view_reason)

        {:noreply, socket}

        "add_page" ->
        socket =
          socket
          |> assign(:page_title, "New Page")
          |> assign(:page, %{})
          |> assign(:live_action, :new)

        {:noreply, socket}

      "edit" ->
        socket =
          socket
          |> assign(:live_action, :edit)
          |> apply_action(:edit, value)

        {:noreply, socket}

      "view_document" ->
        case value["id"] do
          nil ->
            {:noreply, put_flash(socket, :error, "License ID is required")}

          id ->
            socket =
              socket
              |> assign(:page_title, "View Document")
              |> assign(:live_action, :view_document)
              |> assign(:doc_name, value["doc_name"])
              |> assign(:licence_form, ~p"/license/certificate/#{id}")

            {:noreply, socket}
        end

      "update_status" ->
        new_status = if value["status"] == "0", do: "revoke", else: "activate"
        {
          :noreply,
          assign(socket, :live_action, :confirm_with_des)
          |> assign(
            :confirmation_model_text,
            "Are you sure you want to #{new_status} this License?"
          )
          |> assign(:confirmation_model_params, Map.merge(value, %{"action" => new_status}))
          |> assign(:confirmation_model_agree, "change_record_status")
        }

      "change_record_status" ->
        LiveFunctions.change_record(value, socket, :change_record_status)

      "filter_change" ->
        assign(socket, form: useform(value["filter"]))
        |> noreply()
      "filter" ->
        value = if socket.assigns.showFilter == true, do: false, else: true
        assign(socket, :showFilter, value)
        |> noreply()
      "search" ->
        send(self(), {:get_list, value})
        noreply(socket)
      "export" ->
        LiveFunctions.export_records(socket, value, "applications", socket.assigns.params)

      "close_model" ->
        socket =
          socket
          |> assign(:new_edit_modal, false)
          |> assign(:view_modal, false)
          |> assign(:live_action, :index)
          |> apply_action(:index, value)

        {:noreply, socket}

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :live_action, :index)
        }

      _ ->
        {:noreply, socket}
    end
  end

  defp list(%{assigns: assigns} = socket, params) do
    data =
      LivePageControl.create_table_params(socket, params)
      |> Map.merge(%{"session_id" => assigns.session_id})
    {
      :noreply,
      assign(socket, :data, TableQuery.index(data))
      |> assign(data_loader: false)
      |> assign(params: params)
    }
  end

  defp record_status(socket, params) do
    Functions.index(socket, params, "change_status")
    |> case do
      {:ok, _message} ->
        success_message(socket, "License Status Successfully Changed.")

      {:error, message} ->
        error_message(socket, message)
    end
  end

  defp success_message(%{assigns: _assigns} = socket, message) do
    send(self(), {:get_list, socket.assigns.params})
    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "success"
      )
    }
  end

  defp error_message(%{assigns: _assigns} = socket, message) do
    send(self(), {:get_list, socket.assigns.params})
    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "error"
      )
    }
  end
end
