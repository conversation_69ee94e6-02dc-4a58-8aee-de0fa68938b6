defmodule App.LicenseConditions do
  @moduledoc """
  The Conditions context.
  """

  alias App.Licenses.{
    LicenseMapping
  }

  # App.LicenseConditions.get_license_expiring_condition!(1)
  # App.LicenseConditions.get_license_expiring_reason()
  # App.LicenseConditions.update_license_expiring_condition(1, 0)
  import Ecto.Query, warn: false

  alias App.{Repo, Notification.UserNotifications, Licenses, Utilities}
  alias Logs.Audit
  alias App.Files.UploadedFile
  alias App.Licenses.LicenseMapping
  alias App.Licenses.{Conditions, LicenseConditionsMapping}
  # App.LicenseConditions.query_condition_by_user_id(6)

  # Conditions --->>>>>

  def get_condition!(id), do: Repo.get!(Conditions, id)

  def get_conditions!() do
    Conditions
    |> where([a], a.status == 1)
    |> Repo.all()
  end

  def get_condition_by_name(name) do
    Conditions
    |> where([a], a.name == ^name)
    |> limit(1)
    |> Repo.one()
  end

  def update_conditions(%Conditions{} = admin_contacts, attrs) do
    admin_contacts
    |> Conditions.changeset(attrs)
    |> Repo.update()
  end

  def create_conditions(attrs \\ %{}) do
    %Conditions{}
    |> Conditions.changeset(attrs)
    |> Repo.insert()
  end

  def license_has_conditions(application_id) do
    LicenseConditionsMapping
    |> where([a], a.application_id == ^application_id)
    |> Repo.exists?()
  end

  def get_conditions_tuple!(list \\ []) when is_list(list) do
    Conditions
    |> where([a], a.status == 1 and a.id not in ^list)
    |> order_by([a], asc: a.name)
    |> select([a], {a.name, a.id})
    |> Repo.all()
  end

  def query_condition_by_user_id(id) do
    Conditions
    |> join(:left, [a], b in LicenseConditionsMapping, on: a.id == b.condition_id)
    |> where([a, b], b.application_id == ^id and b.status == 1)
    |> select([a, b], %{description: a.description, name: a.name})
    |> limit(1)
    |> Repo.one()
  end

  def create_condition(%{assigns: assigns} = socket, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "create",
      Conditions.changeset(
        %Conditions{},
        Map.put(attrs, "user_id", assigns.current_user.id)
      )
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{assigns.current_user.email} created a condition: #{attrs["name"]}",
      "CONDITION CREATE",
      attrs,
      "Conditions"
    )
    |> Repo.transaction()
  end

  def update_condition(socket, %Conditions{} = record, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update",
      Conditions.changeset(record, attrs)
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} updated a condition: #{attrs["name"]}",
      "CONDITION UPDATE",
      attrs,
      "Conditions"
    )
    |> Repo.transaction()
  end

  def change_condition_status(socket, attrs, record) do
    new_status = if attrs["status"] == 0, do: "Disable", else: "Enable"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update_status",
      Conditions.changeset(record, %{status: attrs["status"]})
    )
    |> Logs.Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} #{new_status}d a condition: #{record.name}",
      "CONDITION STATUS",
      attrs,
      "Conditions"
    )
    |> Repo.transaction()
  end

  def change_conditions(%Conditions{} = condition, attrs \\ %{}) do
    Conditions.changeset(condition, attrs)
  end

  def get_condition_mapping!(id), do: Repo.get!(LicenseConditionsMapping, id)

  def get_cond_map_preload!(id) do
    LicenseConditionsMapping
    |> where([a], a.id == ^id)
    |> preload([:condition, :application])
    |> limit(1)
    |> Repo.one()
  end

  def get_uploaded_by_condition_map!(id) do
    UploadedFile
    |> where([a], a.condition_map_id == ^id and a.is_condition == true)
    |> limit(1)
    |> preload([:application])
    |> Repo.one()
  end

  def get_license_condition!(license_id) do
    LicenseConditionsMapping
    |> where([a], a.application_id == ^license_id)
    |> order_by([a], asc: a.id)
    |> preload([:condition, :added_by, :confirmed_by, :application])
    |> Repo.all()
  end

  def get_license_condition_type_by_id!(id) do
    Conditions
    |> where([a], a.status == 1 and a.id == ^id)
    |> order_by([a], asc: a.id)
    |> limit(1)
    |> Repo.one()
  end

  def get_license_expiring_condition!(license_id) do
    LicenseConditionsMapping
    |> where([a], a.application_id == ^license_id)
    |> order_by([a], asc: a.id)
    # |> preload([:condition, :added_by, :confirmed_by, :application])
    |> limit(1)
    |> Repo.one()
  end

  def get_license_expiring() do
    LicenseConditionsMapping
    |> where([a], a.expiring_status in ["SET", "EXPIRED"])
    |> order_by([a], asc: a.id)
    # |> preload([:condition, :added_by, :confirmed_by, :application])

    |> Repo.all()
  end

  def get_license_expiring_reason() do
    LicenseConditionsMapping
    |> where(
      [a],
      not is_nil(a.comments) and a.expiring_status in ["EXPIRED", "EXPIRED_CONDITION"]
    )
    # |> preload([:condition, :added_by, :confirmed_by, :application])

    |> Repo.all()
  end

  #
  #  def update_license_expiring_condition(license_id, expiring_date) do
  #    if expiring_date != 0 do
  #    else
  #    license =  get_application_id(license_id)
  ##    LicenseConditionsMapping
  ##    |> where([a], a.application_id == ^license_id)
  ##    |> order_by([a], asc: a.id)
  ##      # |> preload([:condition, :added_by, :confirmed_by, :application])
  ##    |> limit(1)
  #   # |> Repo.one()
  #    Ecto.Multi.new()
  #    |> Ecto.Multi.update(:updating_user_mapping, LicenseMapping.changeset_draft(license, %{status: -1}))
  #    |> Repo.transaction()
  #   end
  #  end

  def change_license_condition_mapping(%LicenseConditionsMapping{} = condition, attrs \\ %{}) do
    LicenseConditionsMapping.changeset(condition, attrs)
  end

  def change_license_condition_mapping_attrs(attrs \\ %{}) do
    %LicenseConditionsMapping{}
    |> LicenseConditionsMapping.changeset(attrs)
  end

  def create_condition_mapping(%{assigns: assigns} = socket, attrs) do
    condition = get_condition!(attrs["condition_id"])

    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "create",
      LicenseConditionsMapping.changeset(
        %LicenseConditionsMapping{},
        %{
          condition_id: attrs["condition_id"],
          responsibility: attrs["responsibility"],
          application_id: attrs["application_id"],
          expiring_date: attrs["expiring_date"],
          expiring_status: "SET",
          status: 1,
          condition_met: false,
          added_by_id: assigns.current_user.id
        }
      )
    )
    |> Ecto.Multi.update(
      "record",
      LicenseMapping.changeset_draft(
        assigns.record,
        %{
          "summary_user_draft" => "<br><p>#{condition.description}</p>"
        }
      )
    )
    |> run_user_updates(condition, assigns.record, attrs)
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{assigns.current_user.email} created a condition mapping for license: #{assigns.record.record_name}",
      "CONDITION MAPPING CREATE",
      attrs,
      "License Conditions Mapping"
    )
    |> Repo.transaction()
    |> case do
      {:ok, result} ->
        {:ok, result}

      {:error, error} ->
        {:error, error}

      {:error, _ecto, error, _} ->
        {:error, error}
    end
  end

  defp run_user_updates(multi, condition, record, attrs) do
    if attrs["responsibility"] == "Applicant" do
      multi
      |> Ecto.Multi.insert(
        "create_notification#{condition.id}",
        fn %{"create" => mapped} ->
          UserNotifications.changeset(
            %UserNotifications{},
            %{
              "message" =>
                "Your Licence Application has been assigned a condition: #{condition.name}",
              "page_url" => "/client/applications/submitted?id=#{record.id}",
              "type" => "LICENCE CONDITIONS",
              "user_id" => record.user_id,
              "role_id" => 8
            }
          )
        end
      )
    else
      multi
      |> Ecto.Multi.run("get_level", fn _, _ ->
        Utilities.get_condition_approval_level!(1)
        |> case do
          nil ->
            {:error,
             "Conditional Approval Level not Maintenained, please contact system Administrator!"}

          data ->
            {:ok, data}
        end
      end)
      |> Ecto.Multi.insert(
        "create_notification#{record.id}",
        fn %{"create" => mapped, "get_level" => get_level} ->
          UserNotifications.changeset(
            %UserNotifications{},
            %{
              "message" =>
                "A Licence Application has been assigned a condition: #{condition.name}",
              "page_url" => "/license/conditions/#{mapped.application_id}",
              "type" => "LICENCE CONDITIONS",
              "role_id" => get_level.department_id
            }
          )
        end
      )
    end
  end

  def confirm_condition(%{assigns: assigns} = socket, attrs) do
    # condition_met = if assigns.current_user.role_id == 6, do: true, else: false

    multi =
      Enum.with_index(attrs["ids"])
      |> Enum.reduce(Ecto.Multi.new(), fn {entry, idx}, multi ->
        record = get_condition_mapping!(entry)

        approval_level_list = Utilities.get_conditional_approval_level_list()

        {condition_met, status} =
          case Enum.find_index(approval_level_list, &(&1 == record.status)) do
            nil ->
              # return true for final approval
              {true, record.status}

            index ->
              next_status = Enum.at(approval_level_list, index + 1)

              if next_status == nil do
                IO.inspect(record)
                {true, record.status}
              else
                {false, next_status}
              end
          end

        params = Map.put(attrs, "status", status)

        Ecto.Multi.update(
          multi,
          {:approval, idx},
          LicenseConditionsMapping.changeset(
            record,
            %{
              condition_met: condition_met,
              status: params["status"],
              confirmed_by_id: "#{if condition_met, do: assigns.current_user.id, else: nil}"
            }
          )
        )
        |> Ecto.Multi.run({:is_final_condition, idx}, fn _repo, _changes ->
          {license, associates} = Licenses.get_application_w_assoc(record.application_id)

          if condition_met && !final_condition_met?(record.application_id) do
            Licenses.final_approval(
              socket,
              Map.merge(attrs, %{"status" => license.status, "comments" => "All Conditions Met"}),
              license,
              associates
            )
          else
            Licenses.update_conditional_status(
              socket,
              %{"condition_status" => params["status"]},
              license
            )

            # {:ok, :not_final_conditon}
          end
        end)
      end)

    multi
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{assigns.current_user.email} confirmed a condition for license: #{attrs["name"]}",
      "CONDITION CONFIRM",
      attrs,
      "License Conditions Mapping"
    )
    |> Repo.transaction()
    |> case do
      {:ok, result} -> {:ok, result}
      {:error, _ecto, error, _} -> {:error, error}
    end
  end

  defp final_condition_met?(application_id) do
    LicenseConditionsMapping
    |> where([a], a.application_id == ^application_id and a.condition_met == false)
    |> Repo.exists?()
  end

  def decline_condition(%{assigns: assigns} = socket, attrs) do
    multi =
      Enum.with_index(attrs["ids"])
      |> Enum.reduce(Ecto.Multi.new(), fn {entry, idx}, multi ->
        record = get_cond_map_preload!(entry)
        approval_level_list = Utilities.get_conditional_approval_level_list()

        status =
          case Enum.find_index(approval_level_list, &(&1 == record.status)) do
            nil -> record.status
            0 -> record.status
            index -> Enum.at(approval_level_list, index - 1)
          end

        IO.inspect(status, label: "STATUS")

        update_multi =
          Ecto.Multi.update(
            multi,
            {:approval, idx},
            LicenseConditionsMapping.changeset(record, %{status: status})
          )

        #  delete only when analyst
        final_multi =
          if status in [1, "1"] do
            uploaded_condition = get_uploaded_by_condition_map!(record.id)

            update_multi
            |> Ecto.Multi.delete({:delete, idx}, uploaded_condition)
            |> Ecto.Multi.insert(
              {:create_notification, idx},
              UserNotifications.changeset(%UserNotifications{}, %{
                "message" => "A Condition has been declined because: #{attrs["reason"]}",
                "page_url" => "/dashboard",
                "type" => "CONDITION DECLINED",
                "reason" => attrs["reason"],
                "user_id" => record.application.user_id,
                "role_id" => 8
              })
            )
          else
            get_level = Utilities.get_condition_approval_level!(status)

            update_multi
            |> Ecto.Multi.insert(
              {:create_notification, idx},
              UserNotifications.changeset(%UserNotifications{}, %{
                "message" => "A Condition has been declined because: #{attrs["reason"]}",
                "page_url" => "/dashboard",
                "type" => "CONDITION DECLINED",
                "reason" => attrs["reason"],
                "role_id" => get_level.department_id
              })
            )
          end

        final_multi
      end)

    multi
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{assigns.current_user.email} declined a condition for license: #{attrs["name"]}",
      "CONDITION DECLINE",
      attrs,
      "License Conditions Mapping"
    )
    |> Repo.transaction()
    |> case do
      {:ok, result} -> {:ok, result}
      {:error, _ecto, error, _} -> {:error, error}
    end
  end

  # def decline_condition(%{assigns: assigns} = socket, attrs, record) do
  #   Ecto.Multi.new()
  #   |> Ecto.Multi.update(
  #     "update",
  #     LicenseConditionsMapping.changeset(
  #       record,
  #       %{
  #         condition_met: false,
  #         status: attrs["status"],
  #         confirmed_by_id: assigns.current_user.id
  #       }
  #     )
  #   )
  #   |> Audit.create_system_log_session_live_multi(
  #     socket,
  #     "User: #{assigns.current_user.email} declined a condition for license: #{attrs["name"]}",
  #     "CONDITION DECLINE",
  #     attrs,
  #     "License Conditions Mapping"
  #   )
  #   |> Repo.transaction()
  # end

  def remove_condition(%{assigns: assigns} = socket, attrs) do
    record = get_condition_mapping!(attrs["id"])

    Ecto.Multi.new()
    |> Ecto.Multi.delete("delete", record)
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{assigns.current_user.email} removed a condition mapping for license: #{assigns.record.record_name}",
      "CONDITION MAPPING REMOVE",
      attrs,
      "License Conditions Mapping"
    )
    |> Repo.transaction()
  end
end
