defmodule App.Accounts.User do
  use Ecto.Schema
  import Ecto.Changeset
  alias App.Database.CustomDB

  @mail_regex ~r/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,9}$/
  # ensure that the email looks valid

  @number_regex ~r(^[0-9]*$)
  # ensure that the mobile looks valid

  @columns [
    :first_name,
    :last_name,
    :mobile,
    :email,
    :password,
    :status,
    :user_role,
    :user_type,
    :auto_password,
    :password_hash,
    :remote_ip,
    :last_logon,
    :login_attempts,
    :role_id,
    :user_id,
    :department_id,
    :sex,
    :registration_type,
    :company_id
  ]

  schema "tbl_users" do
    field(:email, :string)
    field(:first_name, :string)
    field(:last_name, :string)
    field(:mobile, :string)
    field(:password_hash, :string)
    field(:status, :string, default: "A")
    field(:user_role, :integer, default: 2)
    field(:user_type, :string, default: "STAFF")
    field(:registration_type, :string, default: "INDIVIDUAL")
    field(:auto_password, :string, default: "Y")
    field(:remote_ip, :string)
    field(:sex, :string)
    field(:last_logon, :naive_datetime)
    field(:login_attempts, :integer, default: 0)
    field(:department_id, :string, virtual: true)

    field(:password, :string, virtual: true, redact: true)
    field(:confirm_password, :string, virtual: true, redact: true)

    belongs_to(:role, App.Roles.AccessRoles)
    belongs_to(:user, App.Accounts.User)
    belongs_to(:company, App.Accounts.Company)

    has_many :user_permissions, App.Roles.UserPermission
    has_many :additional_permissions, through: [:user_permissions, :permission]

    timestamps()
  end

  @doc """
  A user changeset for registration.

  It is important to validate the length of both email and password.
  Otherwise databases may truncate the email without warnings, which
  could lead to unpredictable or insecure behaviour. Long passwords may
  also be very expensive to hash for certain algorithms.

  ## Options

    * `:hash_password` - Hashes the password so it can be stored securely
      in the database and ensures the password field is cleared to prevent
      leaks in the logs. If password hashing is not needed and clearing the
      password field is not desired (like when using this changeset for
      validations on a LiveView form), this option can be set to `false`.
      Defaults to `true`.

    * `:validate_email` - Validates the uniqueness of the email, in case
      you don't want to validate the uniqueness of the email (like when
      using this changeset for validations on a LiveView form before
      submitting the form), this option can be set to `false`.
      Defaults to `true`.
  """

  def changeset(user, attrs) do
    cast(user, attrs, @columns)
  end

  def self_registration_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :first_name,
      :last_name,
      :email,
      :mobile,
      :role_id,
      :password,
      :sex,
      :confirm_password,
      :auto_password,
      :registration_type
    ])
    |> validate_required([
      :first_name,
      :last_name,
      :mobile,
      :email,
      :sex,
      :password,
      :confirm_password
    ])
    |> validate_email()
    |> validate_confirmation(:password, message: "does not match password")
    |> validate_password(opts)
    |> validate_new_password()
    |> CustomDB.validate_mobile_number2()
    |> validate_mobile_number?()
  end

  def company_registration_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :email,
      :mobile,
      :password,
      :confirm_password,
      :auto_password,
      :registration_type
    ])
    |> validate_required([
      :mobile,
      :email,
      :password,
      :confirm_password
    ])
    |> validate_email()
    |> validate_confirmation(:password, message: "does not match password")
    |> validate_password(opts)
    |> validate_new_password()
    |> CustomDB.validate_mobile_number2()
    |> validate_mobile_number?()
  end

  def self_registration_insert_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :first_name,
      :last_name,
      :email,
      :mobile,
      :role_id,
      :user_type,
      :password,
      :sex,
      :confirm_password,
      :auto_password,
      :registration_type
    ])
    |> validate_required([
      :first_name,
      :last_name,
      :mobile,
      :email,
      :sex,
      :password
    ])
    |> validate_email()
    |> validate_confirmation(:password, message: "does not match password")
    |> validate_password(opts)
    |> CustomDB.validate_mobile_number2()
    |> validate_mobile_number?()
  end

  def login_changeset(user, attrs, _opts \\ []) do
    user
    |> cast(attrs, [:email, :password])
  end

  def attach_changeset(user, attrs, _opts \\ []) do
    user
    |> cast(attrs, [:company_id])
  end

  def change_company_changeset(user, attrs, _opts \\ []) do
    user
    |> cast(attrs, [:company_id])
    |> validate_required([:company_id])
  end

  def update_changeset(user, attrs, _opts \\ []) do
    user
    |> cast(attrs, @columns)
    |> validate_email()
  end

  def assign_changeset(user, attrs, _opts \\ []) do
    user
    |> cast(attrs, [:role_id])
  end

  def registration_seed_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :first_name,
      :last_name,
      :mobile,
      :email,
      :password,
      :status,
      :user_type,
      :auto_password,
      :password_hash,
      :remote_ip,
      :role_id,
      :last_logon,
      :sex,
      :login_attempts,
      :registration_type
    ])
    # Keep these required for registration
    |> validate_required([
      :first_name,
      :mobile,
      :email,
      :user_type,
      # :password,
      :status,
      :user_role
    ])
    |> update_change(:email, &String.downcase/1)
    |> update_change(:first_name, &String.capitalize/1)
    |> update_change(:last_name, &String.capitalize/1)
    |> validate_password1(attrs, opts)
    |> validate_email()
  end

  def registration_admin_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :first_name,
      :last_name,
      :mobile,
      :email,
      :password,
      :status,
      :user_type,
      :auto_password,
      :remote_ip,
      :role_id,
      :last_logon,
      :login_attempts,
      :department_id,
      :sex
    ])
    # Keep these required for registration
    |> validate_required([
      :first_name,
      :mobile,
      :email,
      :user_type,
      :role_id,
      :status,
      :user_role
    ])
    |> CustomDB.validate_mobile_number2()
    |> validate_mobile_number?()
    |> validate_email()
    |> validate_format(:mobile, @number_regex)
    |> update_change(:email, &String.downcase/1)
    |> update_change(:first_name, &String.capitalize/1)
    |> update_change(:last_name, &String.capitalize/1)
    # Pass attrs to skip password validation if not provided
    |> validate_password(attrs, opts)
    |> validate_email()
  end

  def registration_client_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :first_name,
      :last_name,
      :mobile,
      :email,
      :password,
      :status,
      :user_type,
      :auto_password,
      :remote_ip,
      :last_logon,
      :login_attempts,
      :role_id,
      :user_id,
      :sex
    ])
    # Keep these required for registration
    |> validate_required([
      :first_name,
      :mobile,
      :email
    ])
    |> CustomDB.validate_mobile_number2()
    |> validate_mobile_number?()
    |> validate_email()
    |> validate_format(:mobile, @number_regex)
    |> update_change(:email, &String.downcase/1)
    |> update_change(:first_name, &String.capitalize/1)
    |> update_change(:last_name, &String.capitalize/1)
    # Pass attrs to skip password validation if not provided
    |> validate_password(attrs, opts)
  end

  def registration_contact_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :first_name,
      :last_name,
      :mobile,
      :email,
      :password,
      :password_hash,
      :status,
      :user_type,
      :auto_password,
      :role_id,
      :sex
    ])
    # Keep these required for registration
    |> validate_required([
      :first_name,
      :mobile,
      :email
    ])
    |> CustomDB.validate_mobile_number2()
    |> validate_mobile_number?()
    |> validate_email()
    |> validate_format(:mobile, @number_regex)
    |> update_change(:email, &String.downcase/1)
    |> update_change(:first_name, &String.capitalize/1)
    |> update_change(:last_name, &String.capitalize/1)
    # Pass attrs to skip password validation if not provided
    |> validate_password(attrs, opts)
    |> validate_email()
    |> maybe_hash_password(opts)
  end

  def self_contact_changeset(user, attrs) do
    user
    |> cast(attrs, [
      :first_name,
      :last_name,
      :mobile,
      :email,
      :sex
    ])
    |> validate_required([
      :first_name,
      :last_name,
      :mobile,
      :email
    ])
    |> CustomDB.validate_mobile_number2()
    |> validate_mobile_number?()
    |> validate_email()
    |> validate_format(:mobile, @number_regex)
    |> update_change(:email, &String.downcase/1)
    |> update_change(:first_name, &String.capitalize/1)
    |> update_change(:last_name, &String.capitalize/1)
  end

  defp validate_password(changeset, _attrs, opts) do
    # Only validate the password if it's present in the changeset
    password = get_change(changeset, :password)

    if password do
      changeset
      |> validate_required([:password])
      |> validate_length(:password, min: 8, max: 30)
      |> validate_format(:password, ~r/[a-z]/, message: "at least one lowercase character")
      |> validate_format(:password, ~r/[A-Z]/, message: "at least one uppercase character")
      |> validate_format(:password, ~r/[!?@#$%^&*_0-9]/,
        message: "at least one digit or punctuation character"
      )
      |> maybe_hash_password(opts)
    else
      changeset
    end
  end

  defp validate_password1(changeset, _attrs, opts) do
    # Only validate the password if it's present in the changeset
    password = get_change(changeset, :password)

    if password do
      changeset
      |> validate_required([:password])
      |> validate_length(:password, min: 8, max: 30)
      |> validate_format(:password, ~r/[a-z]/, message: "at least one lowercase character")
      |> validate_format(:password, ~r/[A-Z]/, message: "at least one uppercase character")
      |> validate_format(:password, ~r/[!?@#$%^&*_0-9]/,
        message: "at least one digit or punctuation character"
      )
      |> maybe_hash_password(opts)
    else
      changeset
    end
  end

  defp validate_password(changeset, opts) do
    changeset
    |> validate_required([:password])
    |> validate_length(:password, min: 8, max: 30)
    # Examples of additional password validation:
    |> validate_format(:password, ~r/[a-z]/, message: "at least one lower case character")
    |> validate_format(:password, ~r/[A-Z]/, message: "at least one upper case character")
    |> validate_format(:password, ~r/[!?@#$%^&*_0-9]/,
      message: "at least one digit or punctuation character"
    )
    |> maybe_hash_password(opts)
  end

  def change_password(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:auto_password, :password, :confirm_password])
    |> validate_required([:password, :confirm_password])
    |> validate_new_password()
    |> validate_password(opts)
  end

  def validate_new_password(changeset) do
    password = get_change(changeset, :password)
    confirm_password = get_change(changeset, :confirm_password)

    if password == confirm_password do
      changeset
    else
      add_error(changeset, :confirm_password, "does not match")
    end
  end

  defp maybe_hash_password(changeset, opts) do
    hash_password? = Keyword.get(opts, :hash_password, true)
    password = get_change(changeset, :password)

    if hash_password? && password && changeset.valid? do
      changeset
      # If using Bcrypt, then further validate it is at most 72 bytes long
      |> validate_length(:password, max: 30, count: :bytes)
      # Hashing could be done with `Ecto.Changeset.prepare_changes/2`, but that
      # would keep the database transaction open longer and hurt performance.
      # |> put_change(:password_hash, Bcrypt.hash_pwd_salt(password))
      |> put_change(:password_hash, Pbkdf2.hash_pwd_salt(password))
      |> delete_change(:password)
    else
      changeset
    end
  end

  defp validate_mobile_number?(changeset) do
    changeset
    |> validate_required([:mobile])
    |> validate_length(:mobile, max: 13)

    #    |> unsafe_validate_unique(:mobile_number, App.Repo)
    #    |> unique_constraint(:mobile_number)
  end

  defp validate_email(changeset) do
    changeset
    |> validate_format(:email, @mail_regex, message: "must have the @ sign and no spaces")
    |> validate_length(:email, max: 160)
    |> unsafe_validate_unique(:email, App.Repo)
    |> unique_constraint(:email)
  end

  @doc """
  A user changeset for changing the password.

  ## Options

    * `:hash_password` - Hashes the password so it can be stored securely
      in the database and ensures the password field is cleared to prevent
      leaks in the logs. If password hashing is not needed and clearing the
      password field is not desired (like when using this changeset for
      validations on a LiveView form), this option can be set to `false`.
      Defaults to `true`.
  """
  def password_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:password])
    |> validate_confirmation(:password, message: "does not match password")
    |> validate_password(opts)
  end

  @doc """
  Confirms the account by setting `confirmed_at`.
  """
  def confirm_changeset(user) do
    now = NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)
    change(user, confirmed_at: now)
  end

  @doc """
  Verifies the password.

  If there is no user or the user doesn't have a password, we call
  `Bcrypt.no_user_verify/0` to avoid timing attacks.
  """
  def valid_password?(%App.Accounts.User{password_hash: password_hash}, password)
      when is_binary(password_hash) and byte_size(password) > 0 do
    # Bcrypt.verify_pass(password, password_hash)
    Pbkdf2.verify_pass(password, password_hash)
  end

  def valid_password?(_, _) do
    # Bcrypt.no_user_verify()
    Pbkdf2.no_user_verify()
    false
  end

  @doc """
  Validates the current password otherwise adds an error to the changeset.
  """
  def validate_current_password(changeset, password) do
    if valid_password?(changeset.data, password) do
      changeset
    else
      add_error(changeset, :current_password, "is not valid")
    end
  end
end
