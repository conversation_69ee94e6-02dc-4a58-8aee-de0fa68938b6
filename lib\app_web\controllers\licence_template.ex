defmodule AppWeb.Auth.LicenceTemplateController do
  use AppWeb, :controller

  alias App.{Utilities, Licenses}

  def show(conn, %{"id" => id}) do
    licence_draft = Utilities.get_draft!(id)

    if is_nil(licence_draft) do
      html = """
      <!DOCTYPE html>
      <html lang="en">
      <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Form Unavailable</title>
      <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100 min-h-screen flex items-center justify-center">
      <div class="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
      <div class="flex items-center justify-center mb-6">
      <svg class="w-12 h-12 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
      </svg>
      </div>
      <h1 class="text-2xl font-bold text-center text-gray-800 mb-2">Form Unavailable</h1>
      <p class="text-gray-600 text-center mb-6">We apologize, but the requested form is currently unavailable. Please try again later or contact support for assistance.</p>
      </div>
      </body>
      </html>
      """

      conn
      |> put_resp_content_type("text/html")
      |> send_resp(200, html)
    else
      get_fields = Licenses.get_application_by_licence!(id)

      IO.inspect(get_fields, label: "get_fields")

      # Add CSS styling for the licence template
      styled_template = add_licence_styling(licence_draft.template)

      map_html =
        get_fields.data
        |> Map.merge(%{
          "inserted_at" => Calendar.strftime(get_fields.inserted_at, "%d %b %Y, %H:%M")
        })
        |> Enum.reduce(styled_template, fn {key, value}, html ->
          string_value =
            case value do
              nil -> ""
              value when is_binary(value) -> value
              _ -> to_string(value)
            end

          String.replace(html, "{{#{key}}}", string_value)
        end)

      conn
      |> put_resp_content_type("text/html")
      |> send_resp(200, map_html)
    end
  end

  # Private function to add CSS styling to the licence template
  defp add_licence_styling(template) do
    css_styles = """
    <style>
      body {
        font-family: 'Times New Roman', serif;
        line-height: 1.6;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #ffffff;
        color: #333333;
      }

      .ql-align-center {
        text-align: center;
      }

      .ql-align-right {
        text-align: right;
      }

      .ql-align-left {
        text-align: left;
      }

      .ql-align-justify {
        text-align: justify;
      }

      p {
        margin-bottom: 15px;
        font-size: 16px;
      }

      strong {
        font-weight: bold;
      }

      em {
        font-style: italic;
      }

      u {
        text-decoration: underline;
      }

      img {
        max-width: 100%;
        height: auto;
      }

      /* Licence template specific styling */
      .licence-container {
        border: 3px solid #2c3e50;
        padding: 40px;
        margin: 20px 0;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }

      .licence-header {
        border-bottom: 2px solid #2c3e50;
        padding-bottom: 20px;
        margin-bottom: 30px;
      }

      .licence-body {
        margin: 30px 0;
      }

      .licence-footer {
        margin-top: 40px;
        border-top: 1px solid #dee2e6;
        padding-top: 20px;
      }

      /* Additional Quill editor styles */
      .ql-size-small {
        font-size: 0.75em;
      }

      .ql-size-large {
        font-size: 1.5em;
      }

      .ql-size-huge {
        font-size: 2.5em;
      }

      .ql-font-serif {
        font-family: Georgia, 'Times New Roman', serif;
      }

      .ql-font-monospace {
        font-family: Monaco, 'Courier New', monospace;
      }

      /* List styles */
      ol, ul {
        padding-left: 1.5em;
        margin-bottom: 15px;
      }

      li {
        margin-bottom: 5px;
      }

      /* Table styles */
      table {
        border-collapse: collapse;
        width: 100%;
        margin-bottom: 15px;
      }

      th, td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
      }

      th {
        background-color: #f2f2f2;
        font-weight: bold;
      }

      /* Blockquote styles */
      blockquote {
        border-left: 4px solid #ccc;
        margin: 0;
        padding-left: 16px;
        font-style: italic;
        color: #666;
      }

      /* Print styles */
      @media print {
        body {
          margin: 0;
          padding: 15px;
        }

        .licence-container {
          border: 2px solid #000;
          box-shadow: none;
          background: white;
        }
      }
    </style>
    """

    # Wrap the template in a proper HTML structure with CSS
    full_html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Licence Template</title>
      #{css_styles}
    </head>
    <body>
      <div class="licence-container">
        #{template}
      </div>
    </body>
    </html>
    """

    full_html
  end
end
