@import "tailwindcss/base";
@import "tailwindcss/components";

@import "./quill.js";
@import "./quill-better-table.css";

.tooltip {
    @apply invisible absolute;
}

.has-tooltip:hover .tooltip {
    @apply visible z-50;
}

@import "tailwindcss/utilities";

/* This file is for your main application CSS */
@layer utilities {
    @media print {
        @page {
            margin: 0; /* Removes default page margin */
        }

        body {
            margin: 0;
        }

        .no-print {
            display: none; /* Hide elements in print */
        }

        .print-only {
            display: block !important; /* Force display in print */
        }
    }

    input[type="checkbox"] {
        @apply rounded border-zinc-300 text-zinc-900 focus:ring-0
    }

    input[type="checkbox"]:checked {
        @apply bg-brand-200
    }

    input[type="checkbox"]:checked:focus, input[type="checkbox"]:checked:hover {
        @apply bg-brand-200 outline-brand-10/50
    }

    .form-group {
        position: relative;
    }

    .form-group > label {
        @apply absolute text-xs font-medium px-1 -top-0.5 left-4 bg-white text-gray-400 rounded
    }

    .form-group select:focus + label {
        @apply text-brand-10
    }

    #filter input, select {
        @apply rounded-lg block w-full px-4 py-2 mt-2 text-gray-700 placeholder-gray-400 bg-white border border-gray-300 rounded-lg focus:border-brand-10 focus:ring-brand-10 focus:outline-none focus:ring focus:ring-opacity-40
    }

    .export-button {
        @apply rounded-lg text-sm border border-gray-400 bg-white py-1 px-2 leading-6 text-zinc-600 hover:text-white hover:bg-brand-10 hover:border-brand-10/70 transition-colors
    }

    .brand-input {
        @apply rounded-lg block w-full px-4 py-2 mt-2 text-gray-700 placeholder-gray-400 bg-white border border-gray-300 rounded-lg focus:border-brand-10 focus:ring-brand-10 focus:outline-none focus:ring focus:ring-opacity-40
    }

    .hidden_scrollbar_container::-webkit-scrollbar {
        @apply transition-all w-2 duration-500 ease-in absolute right-1;
    }

    .hidden_scrollbar_container:hover::-webkit-scrollbar {
        @apply transition-colors duration-300;
    }

    .hidden_scrollbar_container::-webkit-scrollbar-track {
        @apply bg-transparent;
    }

    .dark_scrollbar.hidden_scrollbar_container:hover::-webkit-scrollbar-thumb {
        @apply bg-gray-200
    }

    .hidden_scrollbar_container:hover::-webkit-scrollbar-thumb {
        @apply bg-white/40 rounded-lg;
    }

    .hidden_scrollbar_container::-webkit-scrollbar-thumb:hover {
        @apply bg-white/60;
    }

    .hidden_scrollbar_container::-webkit-scrollbar-thumb {
        @apply bg-transparent;
    }

    /* .hidden_scrollbar_container {
            @apply overflow--scroll;
    } */
    .slide-in-from-top {
        animation: slide-in-top 0.5s ease-in-out;
    }

    @keyframes slide-in-from-top {
        from {
            height: 0;
            overflow: hidden;
            @apply -translate-y-full
        }
        to {
            height: fit-content;
            @apply translate-y-0

        }
    }

    .side-bar .menu .item {
        @apply relative cursor-pointer
    }

    .side-bar .menu .item a {
        text-decoration: none;
        display: flex;
    }


    .side-bar .menu .item a .dropdown {
        /*@apply absolute right-0 p-4 transition-all duration-300 ease;*/
        position: absolute;
        right: 0;
        transition-property: all;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 300ms;
        @apply bg-gray-200 overflow-hidden transition-all duration-300 ease-in-out;
    }

    .side-bar .menu .item .sub-menu {
        /*display: none;*/
        @apply ml-6 pl-2
    }

    .side-bar .menu .item .sub-menu a {
        @apply text-gray-700 px-4 py-2 hover:text-brand-10 rounded-lg hover:bg-zinc-100 relative;
    }

    .side-bar .menu .item .sub-menu a::before {
        content: "";
        @apply h-[2px] w-2 bg-gray-300 top-[50%] -left-2 absolute;
    }

    .smooth-transition-all {
        @apply transition-all ease-in-out duration-500
    }

        @keyframes fade-in-up {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
    
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }
    
        @keyframes fade-in-right {
            0% {
                opacity: 0;
                transform: translateX(20px);
            }
    
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }
    
        @keyframes float {
    
            0%,
            100% {
                transform: translateY(0px);
            }
    
            50% {
                transform: translateY(-10px);
            }
        }
    
        .animate-fade-in-up {
            animation: fade-in-up 0.6s ease-out forwards;
        }
    
        .animate-fade-in-right {
            animation: fade-in-right 0.8s ease-out forwards;
        }
    
        .animate-float {
            animation: float 3s ease-in-out infinite;
        }
    
        .animation-delay-200 {
            animation-delay: 0.2s;
        }
    
        .animation-delay-400 {
            animation-delay: 0.4s;
        }
    
        .animation-delay-600 {
            animation-delay: 0.6s;
        }
    
        .animation-delay-1000 {
            animation-delay: 1s;
        }
    
        .animation-delay-2000 {
            animation-delay: 2s;
        }

        .animation-delay-4000 {
            animation-delay: 4s;
        }

        @keyframes blob {
            0% {
                transform: translate(0px, 0px) scale(1);
            }
            33% {
                transform: translate(30px, -50px) scale(1.1);
            }
            66% {
                transform: translate(-20px, 20px) scale(0.9);
            }
            100% {
                transform: translate(0px, 0px) scale(1);
            }
        }

        .animate-blob {
            animation: blob 7s infinite;
        }
}


.custom-red-checkbox {
    /* Remove default checkbox styling */
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    /* Tailwind equivalent classes */
    width: 1rem; /* h-4 */
    height: 1rem; /* w-4 */
    border: 2px solid #d1d5db; /* border-gray-300 */
    border-radius: 0.25rem; /* rounded */
    cursor: pointer;
}

.custom-red-checkbox:checked {
    background-color: #dc2626; /* checked:bg-red-600 */
    border-color: #dc2626; /* checked:border-red-600 */
    /* Custom red checkmark SVG */
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 16 16' fill='%23dc2626' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3E%3C/svg%3E");
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
}

.custom-red-checkbox:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.5); /* focus:ring-red-500 */
}
.attention_text {
    color: red;
}
.attention_text_button {
    color: blue;
}


.disable_button {
    color: white;
    opacity: 1;
    background-color: #ccc;
    cursor: not-allowed;
}

.disable_button_decline {
    color: black;
    opacity: 1;
    background-color: #ccc;
    cursor: not-allowed;
}

/* Sidebar navigation hover color override */
div[style*="background-color: #4464AD"] a:hover,
div[style*="background-color: #4464AD"] a:hover span,
div[style*="background-color: #4464AD"] a:hover .text-white,
.text-white:hover {
    color: #12110C !important;
}
