defmodule App.Registration do
  @moduledoc """
  The Registration context.
  """

  import Ecto.Query, warn: false
  alias App.Registration.Pages
  alias App.Registration.ApplicationSteps
  alias App.Repo

  def get_reg_steps!(license_id) do
    ApplicationSteps
    |> where([a], a.license_id == ^license_id)
    |> order_by([a], asc: a.number)
    |> preload([:step])
    |> Repo.all()
  end

  def get_license_steps!(license_id) do
    ApplicationSteps
    |> where([a], a.license_id == ^license_id)
    |> order_by([a], asc: a.number)
    |> select([a], a.number)
    |> Repo.all()
  end

  def create_page(attrs \\ %{}) do
    %Pages{}
    |> Pages.changeset(attrs)
    |> Repo.insert()
  end

  def change_page(%Pages{} = page, attrs \\ %{}) do
    Pages.changeset(page, attrs)
  end

  def get_current_step_url!(license_id, step) do
    ApplicationSteps
    |> join(:left, [a], b in assoc(a, :step))
    |> where([a], a.license_id == ^license_id and a.number == ^step)
    |> select([_, b], %{url: b.url, name: b.name})
    |> limit(1)
    |> Repo.one()
  end
end
