defmodule App.Registration do
  @moduledoc """
  The Registration context.
  """

  import Ecto.Query, warn: false
  alias App.Registration.Pages
  alias App.Registration.ApplicationSteps
  alias App.Repo

  def get_reg_steps!(license_id) do
    ApplicationSteps
    |> where([a], a.license_id == ^license_id)
    |> order_by([a], asc: a.number)
    |> preload([:step])
    |> Repo.all()
  end

  def get_license_steps!(license_id) do
    ApplicationSteps
    |> where([a], a.license_id == ^license_id)
    |> order_by([a], asc: a.number)
    |> select([a], a.number)
    |> Repo.all()
  end

  def create_page(attrs \\ %{}) do
    %Pages{}
    |> Pages.changeset(attrs)
    |> Repo.insert()
  end


  def create_license_data(socket, attrs \\ %{}) do
    attrs["license_field_id"]
    |> Enum.reduce(Ecto.Multi.new(), fn license_field_id, multi ->
      Ecto.Multi.run(multi, "record#{license_field_id}", fn _repo, _ ->
        {
          :ok,
          get_by_license_field_id_and_license_id(license_field_id, attrs["license_id"]) ||
            %LicenseData{}
        }
      end)
      |> Ecto.Multi.insert_or_update("insert#{license_field_id}", fn changes ->
        LicenseData.changeset(changes["record#{license_field_id}"], %{
          "status" => 1,
          "license_id" => attrs["license_id"],
          "license_field_id" => license_field_id
        })
      end)
    end)
    |> Ecto.Multi.update_all(
      "remove",
      fn _changes ->
        from(
          a in LicenseData,
          where:
            a.license_id == ^attrs["license_id"] and
              a.license_field_id not in ^attrs["license_field_id"],
          update: [set: [status: 0]]
        )
      end,
      []
    )
    |> Logs.Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.first_name} created license",
      "LICENSE MAINTENANCE ",
      attrs,
      "license Maintenance"
    )
    |> Repo.transaction()
  end


  def change_page(%Pages{} = page, attrs \\ %{}) do
    Pages.changeset(page, attrs)
  end

  def update_page(%Pages{} = page, attrs) do
    page
    |> Pages.changeset(attrs)
    |> Repo.update()
  end

  def get_page!(id), do: Repo.get!(Pages, id)

  def get_current_step_url!(license_id, step) do
    ApplicationSteps
    |> join(:left, [a], b in assoc(a, :step))
    |> where([a], a.license_id == ^license_id and a.number == ^step)
    |> select([_, b], %{url: b.url, name: b.name})
    |> limit(1)
    |> Repo.one()
  end
end
