defmodule App.Registration do
  @moduledoc """
  The Registration context.
  """
  import Ecto.Query, warn: false
  alias App.Registration.Pages
  alias App.Registration.ApplicationSteps
  alias App.Repo

  @spec get_reg_steps!(any()) :: any()
  def get_reg_steps!(license_id) do
    ApplicationSteps
    |> where([a], a.license_id == ^license_id)
    |> order_by([a], asc: a.number)
    |> preload([:step])
    |> Repo.all()
  end

  def get_license_steps!(license_id) do
    ApplicationSteps
    |> where([a], a.license_id == ^license_id)
    |> order_by([a], asc: a.number)
    |> select([a], a.number)
    |> Repo.all()
  end

  def page(attrs \\ %{}) do
    %Pages{}
    |> Pages.changeset(attrs)
    |> Repo.insert()
  end

# def create_pages_data(socket, attrs \\ %{}) do
#   Ecto.Multi.new()
#   |> Ecto.Multi.insert(:create, Pages.changeset(%Pages{}, attrs))
#   |> Logs.Audit.create_system_log_session_live_multi(
#     socket,
#     "User: #{socket.assigns.current_user.first_name} created page name: #{attrs["name"]}",
#     "Page CREATION",
#     attrs,
#     "Page Creation"
#   )
#   |> Repo.transaction()
# end

  def change_page(%Pages{} = page, attrs \\ %{}) do
    Pages.changeset(page, attrs)
  end

  def update_page(%Pages{} = page, attrs) do
    page
    |> Pages.changeset(attrs)
    |> Repo.update()
  end

  def get_page!(id), do: Repo.get!(Pages, id)

  def create_page_data(socket, attrs \\ %{}) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert("create_page", fn _changes ->
      Pages.changeset(%Pages{}, %{
        "name" => attrs["name"],
        "url" => attrs["url"],
        "icon" => attrs["icon"],
        "status" => attrs["status"] || 1,
        # "license_id" => attrs["license_id"]
      })
    end)
    |> Logs.Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.first_name} created page: #{attrs["name"]}",
      "page ",
      attrs,
      "page Maintenance"
    )
    |> Repo.transaction()
  end

  def change_page_status(socket, attrs, page) do
    new_status = if attrs["status"] == "0", do: "Deactivate", else: "Activate"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update_status",
      Pages.changeset(page, %{status: attrs["status"]})
    )
    |> Logs.Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.first_name} #{new_status}d page: #{page.name}",
      "PAGE STATUS CHANGE",
      attrs,
      "Page Maintenance"
    )
    |> Repo.transaction()
  end

  def get_current_step_url!(license_id, step) do
    ApplicationSteps
    |> join(:left, [a], b in assoc(a, :step))
    |> where([a], a.license_id == ^license_id and a.number == ^step)
    |> select([_, b], %{url: b.url, name: b.name})
    |> limit(1)
    |> Repo.one()
  end
end
