defmodule Table do
  use Phoenix.Component
  # import Phoenix.HTML
  # import Phoenix.HTML.Form

  import AppWeb.Gettext
  alias AppWeb.Helps.PaginationComponent
  alias Phoenix.LiveView.JS

  def sidebar(assigns) do
    ~H"""
    <div class="flex flex-col w-1/3 md:w-2/5 border-r border-gray-200">
      <form class="p-4 border-b border-gray-200" phx-change="iSearch">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg
              class="w-4 h-4 text-gray-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                clip-rule="evenodd"
              />
            </svg>
          </div>

          <input
            type="search"
            class="bg-white border border-gray-200 text-gray-700 text-sm rounded-lg block w-full pl-10 p-2.5 focus:ring-2 focus:ring-brand-1 focus:border-brand-1 transition duration-150"
            value={@params["filter"]["isearch"]}
            name="isearch"
            placeholder="Search..."
          />
        </div>
      </form>

      <div class="overflow-y-auto h-full">
        <%= if !Enum.empty?(@table_data) do %>
          <.sidebar_list
            table_data={@table_data}
            live_action={@live_action}
            selected_item={@selected_item}
          />
        <% else %>
          <.sidebar_list_empty />
        <% end %>
      </div>
    </div>
    """
  end

  def sidebar_list_empty(assigns) do
    ~H"""
    <div class="py-12 px-4">
      <div class="text-center">
        <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-red-100 mb-4">
          <svg
            class="w-6 h-6 text-red-500"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>

        <h3 class="text-gray-700 font-medium mb-1">No data available</h3>

        <p class="text-sm text-gray-500">Try adjusting your search criteria</p>
      </div>
    </div>
    """
  end

  def sidebar_list(assigns) do
    ~H"""
    <ul class="divide-y divide-gray-200 dark_scrollbar  hidden_scrollbar_container overflow-y-scroll max-h-[30rem]">
      <%= for james <- @table_data do %>
        <li>
          <div
            class={[
              "flex flex-row p-4 cursor-pointer transition-all duration-150 hover:bg-gray-100",
              @live_action == :show && @selected_item == "#{james.id}" &&
                "bg-gray-50 border-l-4 border-brand-1"
            ]}
            phx-click="view_entry"
            phx-value-data={james.data}
            phx-value-id={james.id}
            phx-value-status={"#{james.status}"}
            phx-value-name={"#{james.name}"}
          >
            <div class="w-full">
              <div class="flex items-center space-x-2">
                <h3 class="text-base font-medium text-gray-800"><%= james.name %></h3>

                <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600">
                  <%= james.type %>
                </span>
              </div>

              <div class="mt-1 flex items-center text-xs text-gray-500">
                <span class="flex items-center animate-background-shine cursor-pointer bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
                  <svg
                    class="w-3 h-3 mr-1"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                      clip-rule="evenodd"
                    />
                  </svg>

                  <%= if james.status == 1 do %>
                    Active
                  <% else %>
                    Inactive
                  <% end %>
                </span>
              </div>
            </div>
          </div>
        </li>
      <% end %>
    </ul>
    """
  end

  def sidebar_show(assigns) do
    ~H"""
    <div id={"selected_item-#{@selected_item}"} class="flex flex-col h-full">
      <div class="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
        <div class="flex items-center space-x-2">
          <h2 class="text-lg font-semibold text-gray-800"><%= @selected_item_name %></h2>

          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            Details
          </span>
        </div>
      </div>

      <div class="flex-1 overflow-auto">
        <%= if @selected_info[:full] do %>
          <.sidebar_data_list
            edit_form={@edit_form}
            changeset={@changeset}
            name={@name}
            selected_info={@selected_info}
          />
        <% else %>
          <.sidebar_data_list
            edit_form={@edit_form}
            changeset={@changeset}
            selected_info={@selected_info}
          />
        <% end %>
      </div>
    </div>
    """
  end

  def sidebar_data_list(assigns) do
    ~H"""
    <.form
      :let={_f}
      for={@changeset}
      id="messages"
      phx-change="validate_input"
      phx-submit="continue"
      class="h-full flex flex-col"
    >
      <div class="flex-1 p-6 overflow-y-auto">
        <%= if @selected_info[:full] do %>
          <textarea
            cols="50"
            rows="20"
            name={@name}
            placeholder="insert your text here..."
            style="width: 100%; height: 100%"
            readonly={@edit_form}
            ,
            class={" form-control border-1 px-4 py-3 mt-2 block min-h-[6rem] w-full rounded-lg border-zinc-300 py-[7px] px-[11px]" <>
                "text-zinc-900 focus:border-zinc-400 focus:outline-none focus:ring-4 focus:ring-zinc-800/5 sm:text-sm sm:leading-6" <>
                "phx-no-feedback:border-zinc-300 phx-no-feedback:focus:border-zinc-400 phx-no-feedback:focus:ring-zinc-800/5" <>
                "border-zinc-300 focus:border-zinc-400 focus:ring-zinc-800/5"}
          ><%= @selected_info.data %></textarea>
        <% else %>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <%= for james <- @selected_info do %>
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700" for={james.id}>
                  <%= Enum.map(String.split(james.head, "_"), &String.capitalize/1) |> Enum.join(" ") %>
                </label>

                <div class="relative">
                  <%= cond do %>
                    <% james.head == "updated_by" -> %>
                      <div class="flex items-center">
                        <span class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <svg
                            class="h-5 w-5 text-gray-400"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </span>

                        <input
                          class="pl-10 shadow-sm bg-gray-50 border border-gray-200 text-gray-800 text-sm rounded-lg block w-full p-2.5"
                          value={james.data}
                          id={james.id}
                          type="text"
                          name={"api_management[#{james.head}]"}
                          readonly
                        />
                      </div>
                    <% james.head == "key" -> %>
                      <div class="flex items-center">
                        <span class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <svg
                            class="h-5 w-5 text-gray-400"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </span>

                        <input
                          class="pl-10 shadow-sm bg-gray-50 border border-gray-200 text-gray-800 text-sm rounded-lg block w-full p-2.5"
                          value={james.data}
                          id={james.id}
                          type="password"
                          name={"api_management[#{james.head}]"}
                          readonly={@edit_form}
                        />
                      </div>
                    <% true -> %>
                      <input
                        class="shadow-sm bg-gray-50 border border-gray-200 text-gray-800 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        value={james.data}
                        id={james.id}
                        type="text"
                        name={"api_management[#{james.head}]"}
                        readonly={@edit_form}
                      />
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>

      <div class="p-4 border-t border-gray-200 bg-gray-50">
        <div class="flex justify-end">
          <%= if @edit_form do %>
            <button
              type="button"
              phx-click="edit"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150"
            >
              <svg
                class="mr-2 h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
              </svg>
              Edit
            </button>
          <% else %>
            <button
              type="submit"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150"
            >
              <svg
                class="mr-2 h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
              Save
            </button>
          <% end %>
        </div>
      </div>
    </.form>
    """
  end

  def empty_sidebar_data(assigns) do
    ~H"""
    <div class="h-full w-full flex flex-col justify-center items-center">
      <div class="text-center px-6 max-w-sm">
        <div class="mx-auto h-24 w-24 text-gray-400 mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
            />
          </svg>
        </div>

        <h3 class="text-lg font-medium text-gray-900 mb-2">No item selected</h3>

        <p class="text-gray-500 mb-6">Select an item from the sidebar to view its details</p>
      </div>
    </div>
    """
  end

  def done_sidebar_data(assigns) do
    ~H"""
    <div class="h-full w-full flex flex-col justify-center items-center bg-gray-50">
      <div class="text-center px-6 max-w-sm">
        <div class="mx-auto h-24 w-24 text-green-500 mb-4">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle
              cx="12"
              cy="12"
              r="11"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-opacity="0.3"
            /> <circle cx="12" cy="12" r="11" stroke="currentColor" stroke-width="1.5" />
            <path
              d="M7 12L10 15L17 8"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>

        <h3 class="text-lg font-medium text-gray-900 mb-2">Success!</h3>

        <p class="text-gray-500 mb-6"><%= @message %></p>

        <%!-- <div class="relative">
          <div class="absolute -right-12 top-0">
            <div class="bg-green-600 text-white px-4 py-2 text-sm rounded-lg transform rotate-12 shadow-lg">
              <%= @message %>
            </div>
          </div>
        </div> --%>
      </div>
    </div>
    """
  end

  @doc ~S"""
  Renders a table with generic styling.

  ## Examples

      <Table.main_table id="users" rows={@users}>
        <:col :let={user} label="id"><%= user.id %></:col>
        <:col :let={user} label="username"><%= user.username %></:col>
      </Table.main_table>
  """
  attr(:id, :string, required: true)
  attr(:rows, :list, required: true)
  attr(:extraction, :boolean, default: true)
  attr(:params, :map, required: true)
  attr(:row_id, :any, default: nil, doc: "the function for generating the row id")
  attr(:data_loader, :boolean, default: false, doc: "the function for generating the row id")
  attr(:row_click, :any, default: nil, doc: "the function for handling phx-click on each row")

  attr(:row_item, :any,
    default: &Function.identity/1,
    doc: "the function for mapping each row before calling the :col and :action slots"
  )

  slot :col, required: true do
    attr(:label, :string)
    attr(:label_class, :string)
    attr(:class, :string)
  end

  slot(:action, doc: "the slot for showing user actions in the last table column")

  def main_table(assigns) do
    ~H"""
    <div class="w-full">
      <div class="flex justify-between items-center w-full gap-10 flex-wrap px-4 sm:px-0">
        <div
          class="sm:flex-auto"
          phx-mounted={
            JS.transition(
              {"transition ease-ease-in-out duration-300 transform", "-translate-x-full opacity-0",
               "translate-x-0 opacity-100"}
            )
          }
        >
          <.iSearch />
        </div>

        <div
          :if={@extraction}
          show
          class="flex justify-end items-center gap-2"
          phx-mounted={
            JS.transition(
              {"transition ease-ease-in-out duration-300 transform", "translate-x-full opacity-0",
               "translate-x-0 opacity-100"}
            )
          }
        >
          <button class="export-button p-2" onclick="history.back()" title="Back">
            <i class="hero-arrow-left w-5 h-5"></i>
          </button>

          <button class="export-button p-2" phx-click="filter" title="Filter">
            <i class="hero-funnel w-5 h-5"></i>
          </button>

          <%= if @data_loader do %>
            <button class="btn btn-primary">
              <svg
                class="size-6 animate-spin"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                stroke="currentColor"
                stroke-width="1"
                stroke-linecap="round"
                stroke-linejoin="round"
                viewBox="0 0 24 24"
              >
                <polyline points="1 4 1 10 7 10"></polyline>

                <polyline points="23 20 23 14 17 14"></polyline>

                <path d="M20.49 9A9 9 0 0 0 6.6 4.11L1 10M23 14l-5.59 5.89A9 9 0 0 1 3.51 15"></path>
              </svg>
            </button>
          <% else %>
            <button
              class="export-button btn btn-primary"
              phx-click="refresh_table"
              title="Refresh Table"
            >
              <svg
                class="size-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                stroke="currentColor"
                stroke-width="1"
                stroke-linecap="round"
                stroke-linejoin="round"
                viewBox="0 0 24 24"
              >
                <path d="M17.59 3.41a9 9 0 1 1-11.18 0M12 7v5h5"></path>
              </svg>
            </button>
          <% end %>

          <button class="export-button" phx-click="export" phx-value-file_type="csv">
            CSV
          </button>

          <button class="export-button" phx-click="export" phx-value-file_type="xlsx">
            Excel
          </button>

          <button class="export-button" phx-click="export" phx-value-file_type="pdf">
            PDF
          </button>
        </div>
      </div>
    </div>
    <!-- Desktop Table View -->
    <div class="hidden md:block mt-4">
      <div
        class="w-full mt-2 transition-transform rounded-none border bg-white shadow-sm shadow ring- ring-black ring-opacity-5 sm:rounded-lg"
        phx-mounted={
          JS.transition(
            {"transition ease-in-out duration-300 transform", "-translate-y-full opacity-0",
             "translate-y-0"}
          )
        }
      >
        <table class="min-w-full divide-y divide-gray-300">
          <thead class="bg-brand-10 ring-x ring-t ring-x-brand-1 ring-t-brand-1 sticky top-0">
            <tr>
              <th
                :for={col <- @col}
                class={"px-1 border-b-2 border-brand-10 py-3 border-b-brand-700 #{col[:label_class]} text-left text-sm text-white font-semibold text-gray-900"}
              >
                <%= col[:label] %>
              </th>

              <th
                :if={@action != []}
                class="relative border-b-2 border-brand-10 py-3.5 pl-3 pr-4 sm:pr-6"
              >
                <span class="sr-only"><%= gettext("Actions") %></span>
              </th>
            </tr>
          </thead>

          <tbody id={"#{@id}-tbody"} class="divide-y divide-gray-200 bg-white">
            <tr
              :for={{row, rowi} <- Enum.with_index(@rows)}
              id={@row_id && @row_id.(row)}
              class={[
                "animate-slide-in-from-top transition-transform duration-300 border-b transition duration-300 ease-in-out hover:bg-neutral-100",
                :math.fmod(rowi, 2) == 0.0 && "bg-neutral-50"
              ]}
            >
              <td
                :for={{col, _i} <- Enum.with_index(@col)}
                phx-click={@row_click && @row_click.(row)}
                class={[
                  "whitespace-nowrap px-3 py-4 text-sm text-gray-500 #{col[:class]}",
                  @row_click && "hover:cursor-pointer"
                ]}
              >
                <%= render_slot(col, @row_item.(row)) %>
              </td>

              <td
                :if={@action != []}
                class="relative whitespace-nowrap py-2 pl-3 pr-4 text-right text-sm font-medium sm:pr-6"
              >
                <%= render_slot(@action, @row_item.(row)) %>
              </td>
            </tr>
          </tbody>
          <.empty_table data_loader={@data_loader} data={@rows} />
        </table>
      </div>

      <div class="w-full">
        <.live_component
          module={PaginationComponent}
          id="PaginationComponentT4"
          params={@params}
          pagination_data={@rows}
        />
      </div>
    </div>
    <!-- Desktop Table View -->
    <div class="block md:hidden mt-4">
      <div
        class="space-y-4 px-4"
        phx-mounted={
          JS.transition(
            {"ease-in duration-300", "-translate-y-full opacity-0", "translate-y-0 opacity-100"}
          )
        }
      >
        <div
          :for={{row, index} <- Enum.with_index(@rows)}
          class={[
            "bg-white rounded-lg border shadow-sm",
            @row_click && "cursor-pointer hover:bg-gray-50",
            rem(index, 2) == 0 && "bg-gray-50/50"
          ]}
          id={@row_id && @row_id.(row)}
          phx-click={@row_click && @row_click.(row)}
        >
          <div class="p-4 space-y-3">
            <div
              :for={{col, _i} <- Enum.with_index(@col)}
              class="flex justify-between items-center gap-4 border-b border-gray-100 last:border-0 pb-2 last:pb-0"
            >
              <span class="text-sm font-medium text-gray-500">
                <%= col[:label] %>
              </span>

              <span class="text-sm text-gray-900 text-right">
                <%= render_slot(col, @row_item.(row)) %>
              </span>
            </div>

            <div :if={@action != []} class="pt-2 flex justify-end">
              <%= render_slot(@action, @row_item.(row)) %>
            </div>
          </div>
        </div>
      </div>

      <div class="w-full">
        <.live_component
          module={PaginationComponent}
          id="PaginationComponentT3"
          params={@params}
          pagination_data={@rows}
        />
      </div>
    </div>
    """
  end

  @doc """
  Renders a responsive table with mobile card view support.

  ## Examples

      <.responsive_table id="users" rows={@users}>
        <:col :let={user} label="id"><%= user.id %></:col>
        <:col :let={user} label="username"><%= user.username %></:col>
      </.responsive_table>
  """
  attr(:id, :string, required: true)
  attr(:rows, :list, required: true)
  attr(:extraction, :boolean, default: true)
  attr(:params, :map, default: %{})
  attr(:row_id, :any, default: nil, doc: "the function for generating the row id")
  attr(:row_click, :any, default: nil, doc: "the function for handling phx-click on each row")

  attr(:row_item, :any,
    default: &Function.identity/1,
    doc: "the function for mapping each row before calling the :col and :action slots"
  )

  slot :col, required: true do
    attr(:label, :string)
    attr(:label_class, :string)
    attr(:class, :string)
  end

  slot(:action, doc: "the slot for showing user actions in the last table column")

  def responsive_table(assigns) do
    ~H"""
    <div class="w-full">
      {# Header Controls #}
      <div class="w-full px-4 sm:px-0">
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div
            class="w-full sm:w-auto"
            phx-mounted={
              JS.transition(
                {"ease-in duration-300", "-translate-x-full opacity-0", "translate-x-0 opacity-100"}
              )
            }
          >
            <div class="relative">
              <input
                type="text"
                name="search"
                class="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-brand-500"
                placeholder="Search..."
              />
              <span class="absolute inset-y-0 left-0 pl-3 flex items-center">
                <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fill-rule="evenodd"
                    d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                    clip-rule="evenodd"
                  />
                </svg>
              </span>
            </div>
          </div>

          <div
            :if={@extraction}
            class="flex gap-2"
            phx-mounted={
              JS.transition(
                {"ease-in duration-300", "translate-x-full opacity-0", "translate-x-0 opacity-100"}
              )
            }
          >
            <button
              class="px-4 py-2 bg-white border rounded-lg hover:bg-gray-50 text-sm font-medium"
              phx-click="export"
              phx-value-file_type="csv"
            >
              CSV
            </button>

            <button
              class="px-4 py-2 bg-white border rounded-lg hover:bg-gray-50 text-sm font-medium"
              phx-click="export"
              phx-value-file_type="xlsx"
            >
              Excel
            </button>

            <button
              class="px-4 py-2 bg-white border rounded-lg hover:bg-gray-50 text-sm font-medium"
              phx-click="export"
              phx-value-file_type="pdf"
            >
              PDF
            </button>
          </div>
        </div>
      </div>
      {# Desktop Table View #}
      <div class="hidden md:block mt-4">
        <div
          class="overflow-x-auto rounded-lg border bg-white shadow"
          phx-mounted={
            JS.transition(
              {"ease-in duration-300", "-translate-y-full opacity-0", "translate-y-0 opacity-100"}
            )
          }
        >
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-brand-50">
              <tr>
                <th
                  :for={col <- @col}
                  class={"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider #{col[:label_class] || 'text-gray-500'}"}
                >
                  <%= col[:label] %>
                </th>

                <th :if={@action != []} class="relative px-6 py-3">
                  <span class="sr-only"><%= gettext("Actions") %></span>
                </th>
              </tr>
            </thead>

            <tbody id={"#{@id}-tbody"} class="divide-y divide-gray-200 bg-white">
              <tr
                :for={{row, index} <- Enum.with_index(@rows)}
                id={@row_id && @row_id.(row)}
                class={[
                  "transition-colors",
                  @row_click && "cursor-pointer hover:bg-gray-50",
                  rem(index, 2) == 0 && "bg-gray-50/50"
                ]}
              >
                <td
                  :for={{col, _i} <- Enum.with_index(@col)}
                  phx-click={@row_click && @row_click.(row)}
                  class={["px-6 py-4 whitespace-nowrap text-sm text-gray-900", col[:class]]}
                >
                  <%= render_slot(col, @row_item.(row)) %>
                </td>

                <td
                  :if={@action != []}
                  class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
                >
                  <%= render_slot(@action, @row_item.(row)) %>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      {# Mobile Card View #}
      <div class="block md:hidden mt-4">
        <div
          class="space-y-4 px-4"
          phx-mounted={
            JS.transition(
              {"ease-in duration-300", "-translate-y-full opacity-0", "translate-y-0 opacity-100"}
            )
          }
        >
          <div
            :for={{row, index} <- Enum.with_index(@rows)}
            class={[
              "bg-white rounded-lg border shadow-sm overflow-hidden",
              @row_click && "cursor-pointer hover:bg-gray-50",
              rem(index, 2) == 0 && "bg-gray-50/50"
            ]}
            id={@row_id && @row_id.(row)}
            phx-click={@row_click && @row_click.(row)}
          >
            <div class="p-4 space-y-3">
              <div
                :for={{col, _i} <- Enum.with_index(@col)}
                class="flex justify-between items-center gap-4 border-b border-gray-100 last:border-0 pb-2 last:pb-0"
              >
                <span class="text-sm font-medium text-gray-500">
                  <%= col[:label] %>
                </span>

                <span class="text-sm text-gray-900 text-right">
                  <%= render_slot(col, @row_item.(row)) %>
                </span>
              </div>

              <div :if={@action != []} class="pt-2 flex justify-end">
                <%= render_slot(@action, @row_item.(row)) %>
              </div>
            </div>
          </div>
        </div>
      </div>
      {# Pagination #}
      <div class="mt-4">
        <.live_component
          module={PaginationComponent}
          id={"#{@id}-pagination"}
          params={@params}
          pagination_data={@rows}
        />
      </div>
    </div>
    """
  end

  attr(:id, :string, required: true)
  attr(:check_all, :boolean, default: false)
  attr(:rows, :list, required: true)
  attr(:params, :map, required: true)
  attr(:row_id, :any, default: nil, doc: "the function for generating the row id")
  attr(:data_loader, :boolean, default: false, doc: "the function for generating the row id")
  attr(:row_click, :any, default: nil, doc: "the function for handling phx-click on each row")

  attr(:row_item, :any,
    default: &Function.identity/1,
    doc: "the function for mapping each row before calling the :col and :action slots"
  )

  slot :col, required: true do
    attr(:label, :string)
  end

  slot(:action, doc: "the slot for showing user actions in the last table column")

  def main_table_with_select(assigns) do
    ~H"""
    <div class="w-full sm:flex sm:items-center">
      <div class="flex justify-between items-center w-full gap-10 flex-wrap px-4 sm:px-0">
        <div class="sm:flex-auto">
          <.iSearch />
        </div>

        <div class="flex justify-end items-center gap-2">
          <button class="export-button" phx-click="export" phx-value-file_type="csv">
            CSV
          </button>

          <button class="export-button" phx-click="export" phx-value-file_type="xlsx">
            Excel
          </button>

          <button class="export-button" phx-click="export" phx-value-file_type="pdf">
            PDF
          </button>
        </div>
      </div>
    </div>

    <div class="w-full mt-2 transition-transform rounded-none border bg-white shadow-sm shadow ring- ring-black ring-opacity-5 overflow-hidden sm:rounded-lg">
      <div class="w-full">
        <table class="w-full divide-y divide-gray-300">
          <thead class="bg-brand-10 ring-x ring-t ring-x-brand-1 ring-t-brand-1 sticky top-0">
            <tr>
              <th class="px-3 border-b-2 border-brand-10 py-3.5 border-b-brand-700 text-left text-sm text-white font-semibold text-gray-900">
                <input
                  type="checkbox"
                  class="tBLcheckbox appearance-none w-4 h-4 border-2 border-brand-10 rounded-md checked:bg-brand-10 checked:border-white transition-colors duration-300 hover:border-gray-100 focus:border-brand-10 focus:ring focus:ring-gray-100"
                  checked={@check_all}
                  phx-click="select_all"
                />
              </th>

              <th
                :for={col <- @col}
                class="px-3 border-b-2 border-brand-10 py-3.5 border-b-brand-700 text-left text-sm text-white font-semibold text-gray-900"
              >
                <%= col[:label] %>
              </th>

              <th
                :if={@action != []}
                class="relative border-b-2 border-brand-10 py-3.5 pl-3 pr-4 sm:pr-6"
              >
                <span class="sr-only"><%= gettext("Actions") %></span>
              </th>
            </tr>
          </thead>

          <tbody id={"#{@id}-tbody"} class="divide-y divide-gray-200 bg-white">
            <tr
              :for={{row, rowi} <- Enum.with_index(@rows)}
              id={@row_id && @row_id.(row)}
              class={[
                "animate-slide-in-from-top transition-transform duration-300 border-b transition duration-300 ease-in-out hover:bg-neutral-100",
                :math.fmod(rowi, 2) == 0.0 && "bg-neutral-50"
              ]}
            >
              <td class="px-3 py-4">
                <input
                  type="checkbox"
                  class="tBLcheckbox appearance-none w-4 h-4 border-2 border-brand-10 rounded-md checked:bg-brand-10 checked:border-brand-10 transition-colors duration-300 focus:outline-none focus:border-brand-10 focus:ring focus:ring-brand-10"
                  name="ids[]"
                  id={"select-row-#{row.id}"}
                  phx-value-id={row.id}
                  checked={@check_all}
                /> <label for={"select-row-#{row.id}"}></label>
              </td>

              <td
                :for={{col, _i} <- Enum.with_index(@col)}
                phx-click={@row_click && @row_click.(row)}
                class={[
                  "whitespace-nowrap px-3 py-4 text-sm text-gray-500 #{col[:class]}",
                  @row_click && "hover:cursor-pointer"
                ]}
              >
                <%= render_slot(col, @row_item.(row)) %>
              </td>

              <td
                :if={@action != []}
                class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6"
              >
                <%= render_slot(@action, @row_item.(row)) %>
              </td>
            </tr>
          </tbody>
          <.empty_table data_loader={@data_loader} data={@rows} />
        </table>
      </div>

      <div class="w-full">
        <.live_component
          module={PaginationComponent}
          id="PaginationComponentT4"
          params={@params}
          pagination_data={@rows}
        />
      </div>
    </div>
    """
  end

  # Define extract_paginated directly in this component
  def extract_paginated(assigns) do
    assigns = assign_new(assigns, :row_item, fn -> & &1 end)
    assigns = assign_new(assigns, :row_id, fn -> nil end)
    assigns = assign_new(assigns, :row_click, fn -> nil end)
    assigns = assign_new(assigns, :page, fn -> 1 end)
    assigns = assign_new(assigns, :per_page, fn -> 10 end)
    assigns = assign_new(assigns, :total_entries, fn -> length(assigns.rows) end)

    total_pages = max(ceil(assigns.total_entries / assigns.per_page), 1)
    current_page = min(max(assigns.page, 1), total_pages)

    start_index = (current_page - 1) * assigns.per_page
    paginated_rows = Enum.slice(assigns.rows, start_index, assigns.per_page)

    assigns =
      assign(assigns,
        paginated_rows: paginated_rows,
        total_pages: total_pages,
        current_page: current_page
      )

    ~H"""
    <div class="border rounded-xl shadow-sm shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
      <table class="w-full text-sm text-left text-gray-500">
        <thead class="bg-brand-10 ring-x ring-t ring-x-brand-1 ring-t-brand-1 sticky top-0">
          <tr>
            <th
              :for={col <- @col}
              scope="col"
              class="px-3 border-b-2 border-brand-10 py-3.5 border-b-brand-10 text-left text-sm text-white font-semibold text-gray-900"
            >
              <%= col[:label] %>
            </th>

            <th
              :if={@description != []}
              scope="col"
              class="px-3 border-b-2 border-brand-10 py-3.5 border-b-brand-10 text-left text-sm text-white font-semibold text-gray-900"
            >
              <%= gettext("Description") %>
            </th>
          </tr>
        </thead>

        <tbody id={"#{@id}-tbody"} class="divide-y divide-gray-200 bg-white">
          <tr
            :for={{row, rowi} <- Enum.with_index(@paginated_rows)}
            id={@row_id && @row_id.(row)}
            class={[
              "border-b transition duration-300 ease-in-out hover:bg-neutral-100",
              :math.fmod(rowi, 2) == 0.0 && "bg-neutral-50"
            ]}
          >
            <td
              :for={{col, _i} <- Enum.with_index(@col)}
              class={[
                "whitespace-nowrap px-3 py-4 text-sm text-gray-500 #{Map.get(col, :class, "")}",
                @row_click && "hover:cursor-pointer"
              ]}
            >
              <%= render_slot(col, @row_item.(row)) %>
            </td>

            <td
              :if={@description != []}
              class="relative whitespace-nowrap py-4 pl-3 pr-4 text-sm font-medium sm:pr-6"
            >
              <%= render_slot(@description, @row_item.(row)) %>
            </td>
          </tr>
        </tbody>
        <.empty_table data_loader={false} data={@paginated_rows} />
      </table>
      <!-- Pagination Controls -->
      <div class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
        <div class="flex flex-1 justify-between sm:hidden">
          <button
            phx-click="paginate"
            phx-target={@myself}
            phx-value-page={max(@current_page - 1, 1)}
            disabled={@current_page == 1}
            class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
          >
            Previous
          </button>

          <button
            phx-click="paginate"
            phx-target={@myself}
            phx-value-page={min(@current_page + 1, @total_pages)}
            disabled={@current_page == @total_pages || @total_pages == 0}
            class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
          >
            Next
          </button>
        </div>

        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              Showing
              <span class="font-medium">
                <%= (@current_page - 1) * @per_page + min(1, length(@paginated_rows)) %>
              </span>
              to
              <span class="font-medium">
                <%= (@current_page - 1) * @per_page + length(@paginated_rows) %>
              </span>
              of <span class="font-medium"><%= @total_entries %></span>
              results
            </p>
          </div>

          <div>
            <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
              <button
                phx-click="paginate"
                phx-target={@myself}
                phx-value-page={max(@current_page - 1, 1)}
                disabled={@current_page == 1}
                class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
              >
                <span class="sr-only">Previous</span>
                <!-- Heroicon name: mini/chevron-left -->
                <svg
                  class="h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>

              <%= for page_num <- max(1, @current_page - 2)..min(@total_pages, @current_page + 2) do %>
                <button
                  phx-click="paginate"
                  phx-target={@myself}
                  phx-value-page={page_num}
                  aria-current={if page_num == @current_page, do: "page", else: nil}
                  class={[
                    "relative inline-flex items-center px-4 py-2 text-sm font-semibold ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0",
                    page_num == @current_page &&
                      "z-10 bg-brand-1 text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-brand-10"
                  ]}
                >
                  <%= page_num %>
                </button>
              <% end %>

              <button
                phx-click="paginate"
                phx-target={@myself}
                phx-value-page={min(@current_page + 1, @total_pages)}
                disabled={@current_page == @total_pages || @total_pages == 0}
                class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
              >
                <span class="sr-only">Next</span>
                <!-- Heroicon name: mini/chevron-right -->
                <svg
                  class="h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
    """
  end

  def iSearch(assigns) do
    assigns = assign_new(assigns, :params, fn -> %{"filter" => %{"isearch" => ""}} end)

    ~H"""
    <form class="max-w-sm my-2" phx-change="iSearch">
      <div class="relative formkit-field">
        <label for="member_email" class="hidden block mb-2 text-sm font-medium text-gray-900">
          Email address
        </label>

        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-300">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>

        <input
          type="search"
          class="formkit-input brand-input !pl-12"
          value={@params["filter"]["isearch"]}
          name="isearch"
          placeholder="Search..."
          aria-describedby="basic-addon3"
        />
      </div>
    </form>
    """
  end

  defp empty_table(assigns) do
    assigns = assign_new(assigns, :data_loader, fn -> true end)

    ~H"""
    <tr style="text-align: center">
      <%= if @data_loader do %>
        <td valign="top" colspan="20" class="text-center">
          <div class="text-center">
            <div role="status">
              <svg
                aria-hidden="true"
                class="inline w-8 h-8 mr-2 text-gray-200 animate-spin  fill-blue-600"
                viewBox="0 0 100 101"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                  fill="currentColor"
                />
                <path
                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                  fill="currentFill"
                />
              </svg>
              <span class="sr-only">Loading...</span>
            </div>
          </div>
        </td>
      <% else %>
        <%= if Enum.empty?(@data) do %>
          <td valign="top" colspan="20" class="text-rose-500 p-3">No data available in table</td>
        <% end %>
      <% end %>
    </tr>
    """
  end

  def table_numeric_status(assigns) do
    assigns = assign_new(assigns, :status, fn -> 1 end)

    ~H"""
    <div>
      <%= if @status == 1 do %>
        <span class="animate-background-shine cursor-pointer bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Active
        </span>
      <% end %>

      <%= if @status == 0 do %>
        <span class="animate-background-shine cursor-pointer bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Disabled
        </span>
      <% end %>

      <%= if @status == 2 do %>
        <span class="animate-background-shine cursor-pointer bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Blocked
        </span>
      <% end %>

      <%= if @status == 3 do %>
        <span class="animate-background-shine cursor-pointer bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Pending payment
        </span>
      <% end %>

      <%= if @status == 4 do %>
        <span class="animate-background-shine cursor-pointer bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Pending approval
        </span>
      <% end %>

      <%= if @status == 6 do %>
        <span class="animate-background-shine cursor-pointer bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Rejected
        </span>
      <% end %>

      <%= if @status > 6 do %>
        <span class="animate-background-shine cursor-pointer bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Unknown
        </span>
      <% end %>
    </div>
    """
  end

  def client_default_status(assigns) do
    assigns =
      assigns
      |> assign_new(:status, fn -> 1 end)
      |> assign_new(:approval_status, fn -> true end)
      |> assign_new(:condition_tracking, fn -> false end)
      |> assign_new(:approved, fn -> false end)

    ~H"""
    <div>
      <%= if @approved do %>
        <span class="bg-green-500 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Approved
        </span>
      <% end %>

      <%= if @status == 1 and @approval_status == false do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#00BCD4,45%,#22e6ff,55%,#00BCD4)] bg-[length:250%_100%] text-[#FFFFFF] text-xs font-medium px-3 py-1 text-xs rounded">
          Submitted
        </span>
      <% end %>

      <%= if @status == 0 and @approval_status == false  do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#00BCD4,45%,#22e6ff,55%,#00BCD4)] bg-[length:250%_100%] text-[#FFFFFF] text-xs font-medium px-3 py-1 text-xs rounded">
          Draft
        </span>
      <% end %>

      <%= if @status == -1 do %>
        <span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Returned
        </span>
      <% end %>

      <%= if @status >= 1 and @approval_status == true and !@approved do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#00BCD4,45%,#22e6ff,55%,#00BCD4)] bg-[length:250%_100%] text-[#FFFFFF] text-xs font-medium px-3 py-1 text-xs rounded">
          Under Consideration
        </span>
      <% end %>
    </div>
    """
  end

  def table_approval_status(assigns) do
    assigns = assign_new(assigns, :status, fn -> 1 end)
    assigns = assign_new(assigns, :condition_tracking, fn -> false end)
    assigns = assign_new(assigns, :approved, fn -> false end)
    assigns = assign_new(assigns, :name, fn -> "Default" end)

    badge_style =
      cond do
        assigns.approved ->
          %{
            classes: "bg-green-500 text-green-800",
            animation: "",
            label: "Approved"
          }

        assigns.condition_tracking ->
          %{
            classes: "bg-purple-100 text-purple-800",
            animation: "animate-background-shine cursor-pointer",
            label: "In Conditional Tracking"
          }

        true ->
          case assigns.status do
            -1 ->
              %{
                classes: "bg-red-100 text-red-800",
                animation: "",
                label: "Returned to Applicant"
              }

            status when status > 20 ->
              %{
                classes: "bg-[#9E9E9E] text-white",
                animation: "",
                label: "Unregistered"
              }

            1 ->
              %{
                classes:
                  "bg-[linear-gradient(110deg,#00BCD4,45%,#22e6ff,55%,#00BCD4)] bg-[length:250%_100%] text-[#FFFFFF]",
                animation: "animate-background-shine cursor-pointer",
                label: "Under #{assigns.name} Review"
              }

            _ ->
              {bg_classes, text_color} = {"bg-green-500", "text-[#fff]"}

              %{
                classes: "#{bg_classes} #{text_color}",
                animation: "animate-background-shine cursor-pointer",
                label: "Pending #{assigns.name} Approval"
              }
          end
      end

    assigns = assign(assigns, :badge_style, badge_style)

    ~H"""
    <div>
      <span class={"#{@badge_style.classes} #{@badge_style.animation} text-xs font-medium mr-2 px-2.5 py-0.5 rounded"}>
        <%= @badge_style.label %>
      </span>
    </div>
    """
  end

  def client_approval_status(assigns) do
    assigns =
      assigns
      |> assign_new(:status, fn -> 1 end)
      |> assign_new(:approval_status, fn -> true end)
      |> assign_new(:condition_tracking, fn -> false end)
      |> assign_new(:approved, fn -> false end)

    ~H"""
    <div>
      <%= if @approved do %>
        <span class="bg-green-500 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Approved
        </span>
      <% end %>

      <%= if @condition_tracking do %>
        <span class="bg-purple-100 text-purple-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          In Conditional Tracking
        </span>
      <% end %>

      <%= if @status == -1 do %>
        <span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Declined
        </span>
      <% end %>

      <%= if @status == 0 and @approval_status == false  do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#00BCD4,45%,#22e6ff,55%,#00BCD4)] bg-[length:250%_100%] text-[#FFFFFF] text-xs font-medium px-3 py-1 text-xs rounded">
          Draft
        </span>
      <% end %>

      <%= if @status == 1 and @approval_status == false do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#00BCD4,45%,#22e6ff,55%,#00BCD4)] bg-[length:250%_100%] text-[#FFFFFF] text-xs font-medium px-3 py-1 text-xs rounded">
          Submitted
        </span>
      <% end %>

      <%= if @status == 1 and @approval_status == true do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#00BCD4,45%,#22e6ff,55%,#00BCD4)] bg-[length:250%_100%] text-[#FFFFFF] text-xs font-medium px-3 py-1 text-xs rounded">
          Under Finance Review
        </span>
      <% end %>

      <%= if @status == 2 do %>
        <span class="bg-[#4CAF50] text-white text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Pending Analyst Approval
        </span>
      <% end %>

      <%= if @status == 3 do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#FFEB3B,45%,#22e6ff,55%,#FFEB3B)] bg-[length:250%_100%] text-[#fff] text-xs font-medium px-3 py-1 text-xs rounded">
          Pending Analyst Approval
        </span>
      <% end %>

      <%= if @status == 4 do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#FFEB3B,45%,#22e6ff,55%,#FFEB3B)] bg-[length:250%_100%] text-[#fff] text-xs font-medium px-3 py-1 text-xs rounded">
          Pending Manager Approval
        </span>
      <% end %>

      <%= if @status == 5 do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#4CAF50,45%,#8BC34A,55%,#4CAF50)] bg-[length:250%_100%] text-[#fff] text-xs font-medium px-3 py-1 rounded">
          Pending Assistant Director Approval
        </span>
      <% end %>

      <%= if @status == 6 do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#2196F3,45%,#03A9F4,55%,#2196F3)] bg-[length:250%_100%] text-[#fff] text-xs font-medium px-3 py-1 rounded">
          Pending Director Approval
        </span>
      <% end %>

      <%= if @status == 7 do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#9C27B0,45%,#E91E63,55%,#9C27B0)] bg-[length:250%_100%] text-[#fff] text-xs font-medium px-3 py-1 rounded">
          Pending LC Approval
        </span>
      <% end %>

      <%= if @status == 8 do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#9C27B0,45%,#E91E63,55%,#9C27B0)] bg-[length:250%_100%] text-[#fff] text-xs font-medium px-3 py-1 rounded">
          Pending Board Approval
        </span>
      <% end %>

      <%= if @status > 20 do %>
        <span class="bg-[#9E9E9E] text-white text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Unregistered
        </span>
      <% end %>
    </div>
    """
  end

  def transaction_numeric_status(assigns) do
    assigns = assign_new(assigns, :status, fn -> 1 end)

    ~H"""
    <div>
      <%= if @status == 0 do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#FFBF00,45%,#ffff00,55%,#FFBF00)] bg-[length:250%_100%] text-[#4F008C] text-xs font-medium px-3 py-1 text-xs rounded">
          Pending Approval
        </span>
      <% end %>

      <%= if @status == 1 do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#00BCD4,45%,#22e6ff,55%,#00BCD4)] bg-[length:250%_100%] text-[#FFFFFF] text-xs font-medium px-3 py-1 text-xs rounded">
          Pending
        </span>
      <% end %>

      <%= if @status == 2 do %>
        <span class="bg-[#4CAF50] text-white text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Payment Complete
        </span>
      <% end %>

      <%= if @status == 3 do %>
        <span class="bg-[#F44336] text-white text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Payment Failed
        </span>
      <% end %>

      <%= if @status == 4 do %>
        <span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Declined
        </span>
      <% end %>

      <%= if @status == 5 do %>
        <span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Processing
        </span>
      <% end %>

      <%= if @status == 6 do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#FFEB3B,45%,#22e6ff,55%,#FFEB3B)] bg-[length:250%_100%] text-[#000] text-xs font-medium px-3 py-1 text-xs rounded">
          Partly Paid
        </span>
      <% end %>

      <%= if @status > 6 do %>
        <span class="bg-[#9E9E9E] text-white text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Unknown
        </span>
      <% end %>
    </div>
    """
  end
  def license_numeric_status(assigns) do
    assigns = assign_new(assigns, :status, fn -> 1 end)

    ~H"""
    <div>
      <%= if @status == 0 do %>
  <span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Revoked
        </span>
      <% end %>

      <%= if @status == 1 do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#00BCD4,45%,#22e6ff,55%,#00BCD4)] bg-[length:250%_100%] text-[#FFFFFF] text-xs font-medium px-3 py-1 text-xs rounded">
          Actibve
        </span>
      <% end %>

      <%= if @status == 2 do %>
        <span class="bg-[#4CAF50] text-white text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Expired
        </span>
      <% end %>

      <%= if @status == 3 do %>
        <span class="bg-[#F44336] text-white text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Payment Failed
        </span>
      <% end %>

      <%= if @status == 4 do %>
        <span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Declined
        </span>
      <% end %>

      <%= if @status == 5 do %>
        <span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Processing
        </span>
      <% end %>

      <%= if @status == 6 do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#FFEB3B,45%,#22e6ff,55%,#FFEB3B)] bg-[length:250%_100%] text-[#000] text-xs font-medium px-3 py-1 text-xs rounded">
          Partly Paid
        </span>
      <% end %>

      <%= if @status > 6 do %>
        <span class="bg-[#9E9E9E] text-white text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Unknown
        </span>
      <% end %>
    </div>
    """
  end

  def table_string_status(assigns) do
    assigns = assign_new(assigns, :status, fn -> "FAILED-1" end)

    ~H"""
    <div>
      <%= if @status in ["COMPLETE", "SUCCESSFULLY", "S"] do %>
        <span class="bg-[#4CAF50] text-white text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          COMPLETED
        </span>
      <% end %>

      <%= if @status in ["FAIL", "FAILED", "F"] do %>
        <span class="bg-[#F44336] text-white text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          FAILED
        </span>
      <% end %>

      <%= if @status == "P" do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#FFBF00,45%,#ffff00,55%,#FFBF00)] bg-[length:250%_100%] text-[#4F008C] text-xs font-medium px-3 py-1 text-xs rounded">
          Pending Approval
        </span>
      <% end %>

      <%= if @status == "PENDING" do %>
        <span class="animate-background-shine cursor-pointer bg-[linear-gradient(110deg,#FFBF00,45%,#ffff00,55%,#FFBF00)] bg-[length:250%_100%] text-[#4F008C] text-xs font-medium px-3 py-1 text-xs rounded">
          PENDING
        </span>
      <% end %>

      <%= if @status == "A" do %>
        <span class="bg-[#4CAF50] text-white text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Active
        </span>
      <% end %>

      <%= if @status == "D" do %>
        <span class="bg-[#F44336] text-white text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Disabled
        </span>
      <% end %>
    </div>
    """
  end

  def table_boolean_status(assigns) do
    assigns = assign_new(assigns, :status, fn -> 1 end)

    ~H"""
    <div>
      <%= if @status == true do %>
        <span class="bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Active
        </span>
      <% end %>

      <%= if @status == false do %>
        <span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Disabled
        </span>
      <% end %>

      <%= if @status not in [true, false] do %>
        <span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Unknown
        </span>
      <% end %>
    </div>
    """
  end

  @doc ~S"""
  Renders a table with generic styling.

  ## Examples

      <Table.main_table id="users" rows={@users}>
        <:col :let={user} label="id"><%= user.id %></:col>
        <:col :let={user} label="username"><%= user.username %></:col>
      </Table.main_table>
  """
  attr(:id, :string, required: true)
  attr(:rows, :list, required: true)
  attr(:row_id, :any, default: nil, doc: "the function for generating the row id")
  attr(:row_click, :any, default: nil, doc: "the function for handling phx-click on each row")

  attr(:row_item, :any,
    default: &Function.identity/1,
    doc: "the function for mapping each row before calling the :col and :action slots"
  )

  slot :col, required: true do
    attr(:label, :string)
    attr(:class, :string)
  end

  slot(:description, doc: "the slot for showing user actions in the last table column")
  slot(:record_title)

  def extract(assigns) do
    ~H"""
    <div class="border rounded-xl shadow-sm shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
      <table class="w-full text-sm text-left text-gray-500">
        <thead class="text-xs bg-brand-1">
          <tr>
            <th
              :for={col <- @col}
              scope="col"
              class="px-3 border-b-2 border-brand-10 py-3.5 border-b-brand-10 text-left text-sm text-white font-semibold text-gray-900"
            >
              <%= col[:label] %>
            </th>

            <th
              :if={@description != []}
              scope="col"
              class="px-3 border-b-2 border-brand-10 py-3.5 border-b-brand-10 text-left text-sm text-white font-semibold text-gray-900"
            >
              <%= gettext("Description") %>
            </th>
          </tr>
        </thead>

        <tbody id={"#{@id}-tbody"} class="divide-y divide-gray-200 bg-white">
          <tr
            :for={{row, rowi} <- Enum.with_index(@rows)}
            id={@row_id && @row_id.(row)}
            class={[
              "border-b transition duration-300 ease-in-out hover:bg-neutral-100",
              :math.fmod(rowi, 2) == 0.0 && "bg-neutral-50"
            ]}
          >
            <td
              :for={{col, _i} <- Enum.with_index(@col)}
              class={[
                "whitespace-nowrap px-3 py-4 text-sm text-gray-500 #{col[:class]}",
                @row_click && "hover:cursor-pointer"
              ]}
            >
              <%= render_slot(col, @row_item.(row)) %>
            </td>

            <td
              :if={@description != []}
              class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6"
            >
              <%= render_slot(@description, @row_item.(row)) %>
            </td>
          </tr>
        </tbody>
        <.empty_table data_loader={false} data={@rows} />
      </table>
    </div>
    """
  end

  def status_access_roles(assigns) do
    assigns = assign_new(assigns, :status, fn -> 1 end)

    ~H"""
    <div>
      <%= if @status == 1 do %>
        <span class="bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Active
        </span>
      <% end %>

      <%= if @status == 0 do %>
        <span class="bg-blue-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Pending approval
        </span>
      <% end %>

      <%= if @status == 2 do %>
        <span class="bg-gray-100 text-gray-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Inactive
        </span>
      <% end %>

      <%= if @status == 4 do %>
        <span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Declined
        </span>
      <% end %>

      <%= if @status == 3 do %>
        <span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
          Deleted
        </span>
      <% end %>
    </div>
    """
  end

  def sidebar_show_rich_text(assigns) do
    ~H"""
    <div id={"selected_item-#{@selected_item}"} class="flex flex-col h-full">
      <div class="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center space-x-2">
          <h2 class="text-lg font-semibold text-gray-800"><%= @selected_item_name %></h2>

          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            Template
          </span>
        </div>
      </div>

      <div class="flex-1 overflow-auto">
        <.sidebar_data_list_rich_text
          edit_form={@edit_form}
          changeset={@changeset}
          name={@name}
          selected_info={@selected_info}
        />
      </div>
    </div>
    """
  end

  def sidebar_data_list_rich_text(assigns) do
    ~H"""
    <.form
      :let={_f}
      for={@changeset}
      id="summary_draft_form"
      phx-change="validate_input"
      phx-submit="continue"
      class="h-full flex flex-col"
    >
      <div class="flex-1 p-4 bg-white" id="editor-container" phx-update="ignore">
        <div
          id="editor"
          phx-hook="RichTextEditor"
          phx-update="ignore"
          class={"relative w-full h-full border rounded-lg overflow-hidden " <>
                if(!@edit_form, do: "border-blue-300 ring-2 ring-blue-100", else: "border-gray-300")}
        >
        </div>
      </div>

      <div class="p-4 border-t border-gray-200 bg-gray-50">
        <div class="flex justify-end">
          <%= if @edit_form do %>
            <button
              type="button"
              phx-click="edit"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150"
            >
              <svg
                class="mr-2 h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
              </svg>
              Edit
            </button>
          <% else %>
            <div class="flex space-x-3">
              <button
                type="button"
                phx-click="close_confirmation_model"
                class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150"
              >
                Cancel
              </button>

              <button
                type="submit"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150"
              >
                <svg
                  class="mr-2 h-4 w-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
                Save
              </button>
            </div>
          <% end %>
        </div>
      </div>
    </.form>
    """
  end
end
