import Config

# config/runtime.exs is executed for all environments, including
# during releases. It is executed after compilation and before the
# system starts, so it is typically used to load production configuration
# and secrets from environment variables or elsewhere. Do not define
# any compile-time configuration in here, as it won't be applied.
# The block below contains prod specific runtime configuration.

# ## Using releases
#
# If you use `mix release`, you need to explicitly enable the server
# by passing the PHX_SERVER=true when you start it:
#
#     PHX_SERVER=true bin/app start
#
# Alternatively, you can use `mix phx.gen.release` to generate a `bin/server`
# script that automatically sets the env var above.
if System.get_env("PHX_SERVER") do
  config :app, AppWeb.Endpoint, server: true
end

if config_env() == :prod do
  database_url =
    System.get_env("CLIENT_DATABASE_URL") ||
      raise """
      environment variable CLIENT_DATABASE_URL is missing.
      For example: ecto://USER:PASS@HOST/DATABASE
      """

  logs_database_url =
    System.get_env("LOGS_DATABASE_URL") ||
      raise """
      environment variable LOGS_DATABASE_URL is missing.
      For example: ecto://USER:PASS@HOST/DATABASE
      """

  maybe_ipv6 = if System.get_env("ECTO_IPV6") in ~w(true 1), do: [:inet6], else: []

  config :app, App.Repo,
    # ssl: true,
    url: database_url,
    pool_size: String.to_integer(System.get_env("POOL_SIZE") || "10"),
    socket_options: maybe_ipv6

  config :app, Logs.LogRepo,
    # ssl: true,
    url: logs_database_url,
    pool_size: String.to_integer(System.get_env("POOL_SIZE") || "20"),
    socket_options: maybe_ipv6

  secret_key_base =
    System.get_env("SECRET_KEY_BASE") ||
      raise """
      environment variable SECRET_KEY_BASE is missing.
      You can generate one by calling: mix phx.gen.secret
      """

  host = System.get_env("PHX_HOST") || "example.com"
  port = String.to_integer(System.get_env("PORT") || "4000")

  config :app, :dns_cluster_query, System.get_env("DNS_CLUSTER_QUERY")

  config :app, AppWeb.Endpoint,
    url: [host: host, port: 443, scheme: "https"],
    http: [
      ip: {0, 0, 0, 0},
      port: port
    ],
    # ,
    secret_key_base: secret_key_base

  #    force_ssl: [rewrite_on: [:x_forwarded_proto]]

  # ------------------------------- quantum jobs config -----------------------------#
  config :app, App.JobScheduler,
    overlap: false,
    timeout: :timer.hours(6),
    timezone: "Africa/Cairo",
    jobs: []
end
