export const PrintContent = {
    async printDiv(divName) {
        const element = document.getElementById(divName);
        if (!element) {
            console.error(`Element with ID '${divName}' not found`);
            return false;
        }

        try {
            const printContents = element.innerHTML;
            const originalContents = document.body.innerHTML;
            
            // Add print-specific styles
            const style = document.createElement('style');
            style.innerHTML = '@media print { body { margin: 0; padding: 0; } }';
            document.head.appendChild(style);
            
            document.body.innerHTML = printContents;
            
            await window.print();
            
            // Restore original content
            document.body.innerHTML = originalContents;
            style.remove();
            
            return true;
        } catch (error) {
            console.error('Print error:', error);
            return false;
        }
    },
    mounted() {
        window.printDiv = this;
    },
    
    destroyed() {
        if (window.printDiv === this) {
            window.printDiv = undefined;
        }
    }
};

export const PrintIframe = {
    mounted() {
        this.iframeId = this.el.getAttribute("data-iframe-id");
        this.isLoading = false;

        this.printIframe = async () => {
            if (this.isLoading) return;
            this.isLoading = true;
            
            try {
                const iframe = document.getElementById(this.iframeId);
                if (!iframe) throw new Error(`Iframe '${this.iframeId}' not found`);

                const iframeWindow = iframe.contentWindow;
                if (!iframeWindow) throw new Error('Cannot access iframe content');

                await new Promise((resolve) => {
                    if (iframe.complete) {
                        resolve();
                    } else {
                        iframe.onload = resolve;
                    }
                });

                iframeWindow.focus();
                await iframeWindow.print();
                
                // Trigger optional callback
                this.el.dispatchEvent(new CustomEvent('printComplete'));
                
            } catch (error) {
                console.error('Print iframe error:', error);
                this.el.dispatchEvent(new CustomEvent('printError', { detail: error }));
            } finally {
                this.isLoading = false;
            }
        };

        this.el.addEventListener("click", this.printIframe);
    },

    destroyed() {
        if (this.el) {
            this.el.removeEventListener("click", this.printIframe);
        }
    }
};

