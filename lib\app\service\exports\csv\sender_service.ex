defmodule App.Service.Export.CsvSenderService do
  @moduledoc false
  alias App.Service.Table.ServiceSender
  alias App.Service.Export.Functions

  @headers [
    "SENDER",
    "CLIENT NAME",
    "CREATED BY",
    "CREATED AT",
    "LAST MODIFIED"
  ]

  def index(assigns, payload) do
    ServiceSender.export(assigns, payload)
    |> Stream.map(
      &[
        &1.sender,
        &1.client_name,
        &1.created_by,
        &1.inserted_at,
        &1.updated_at
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Sender"],
              ["", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
