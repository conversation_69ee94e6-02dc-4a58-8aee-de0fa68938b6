defmodule AppWeb.Components.Notifications do
  use AppWeb, :live_view

  on_mount({AppWeb.UserAuth, :mount_current_user})

  alias App.Utilities

  @impl true
  def render(assigns) do
    ~H"""
    <!-- Notifications dropdown -->
    <div x-data="{open: false}" class="relative z-50">
      <!-- Notification Bell Button -->
      <div
        @click="open = !open"
        class="flex items-center p-2 cursor-pointer transition duration-300 hover:opacity-80"
        id="notification-menu-button"
        aria-expanded="false"
        aria-haspopup="true"
      >
        <span class="sr-only">Open notification menu</span>
        <button
          type="button"
          class="relative flex justify-center items-center h-8 w-8 text-sm font-medium rounded-full border border-gray-300 bg-white text-gray-800 shadow-sm hover:bg-brand-10 hover:border-gray-400 transition-all duration-200 "
        >
          <svg
            class="flex-shrink-0 size-5"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>

            <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
          </svg>

          <span class="absolute top-0 end-0 inline-flex items-center justify-center py-0.5 px-1.5 rounded-full text-xs font-bold transform -translate-y-1/2 translate-x-1/2 bg-red-500 text-white min-w-[1rem] min-h-[1rem] text-[10px]">
            <%= cond do %>
              <% length(@user_notifications) == 0 -> %>
                0
              <% length(@user_notifications) > 10 -> %>
                10+
              <% true -> %>
                <%= length(@user_notifications) %>
            <% end %>
          </span>
        </button>
      </div>
      <!-- Notification Dropdown Panel -->
      <div
        x-show="open"
        @click.outside="open = false"
        class="absolute w-80 max-w-md right-0 mt-3 origin-top-right flex flex-col rounded-lg bg-white shadow-xl ring-1 ring-gray-200 focus:outline-none overflow-hidden"
        role="menu"
        x-transition:enter="transition ease-out duration-200"
        x-transition:enter-start="opacity-0 scale-95"
        x-transition:enter-end="opacity-100 scale-100"
        x-transition:leave="transition ease-in duration-100"
        x-transition:leave-start="opacity-100 scale-100"
        x-transition:leave-end="opacity-0 scale-95"
        aria-orientation="vertical"
        aria-labelledby="notification-menu-button"
        tabindex="-1"
      >
        <!-- Header -->
        <div class="bg-gradient-to-r from-brand-10 to-indigo-700 text-white p-4 rounded-t-lg">
          <h1 class="text-lg text-center font-bold">Notifications</h1>
        </div>
        <!-- Notification List -->
        <div class="max-h-96 overflow-y-auto">
          <%= if @user_notifications != [] do %>
            <%= for notification <- @user_notifications do %>
              <div
                phx-hook="ReadNotification"
                id="ReadNotification"
                class="border-b border-gray-100 last:border-b-0"
              >
                <.link
                  navigate={notification.page_url}
                  @click={"readNotificationHook.notify('read_notification', #{notification.id});open = ! open"}
                  class="flex items-start gap-3 p-4 text-sm text-gray-900 hover:bg-gray-50 transition duration-150"
                  role="menuitem"
                  tabindex="-1"
                  id={"user-menu-item-#{notification.id}"}
                >
                  <!-- Notification Icon -->
                  <div class="flex-shrink-0 mt-1">
                    <div class="bg-blue-100 text-blue-600 p-2 rounded-full">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                  </div>
                  <!-- Notification Content -->
                  <div class="flex-1">
                    <h2 class="text-base font-semibold line-clamp-2">
                      <%= notification.message %>
                    </h2>

                    <p class="text-xs text-gray-500 mt-1">
                      <%= Calendar.strftime(
                        NaiveDateTime.add(notification.inserted_at, 7200, :second),
                        "%d %B %Y %H:%M"
                      ) %>
                    </p>
                  </div>
                </.link>
              </div>
            <% end %>
          <% else %>
            <!-- Empty State -->
            <div class="flex flex-col items-center justify-center py-10 px-4">
              <div class="bg-gray-100 p-4 rounded-full mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-8 w-8 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.437L4 17h5m6 0a2 2 0 11-4 0h4z"
                  />
                </svg>
              </div>

              <h2 class="text-base text-gray-500 font-medium text-center">
                No Unread Notifications
              </h2>

              <p class="text-sm text-gray-400 text-center mt-1">
                You're all caught up!
              </p>
            </div>
          <% end %>
        </div>
        <!-- Footer -->
        <div class="p-3 bg-gray-50 border-t border-gray-100">
          <.link
            navigate={~p"/notifications"}
            @click="open = false"
            class="block w-full py-2 px-4 text-center text-sm font-medium text-white bg-brand-10 rounded-md hover:bg-blue-700 transition duration-150"
          >
            View All Notifications
          </.link>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def mount(_params, _session, %{assigns: assigns} = socket) do
    {
      :ok,
      assign(socket, :user_notifications, get_data(assigns))
    }
  end

  @impl true
  def handle_event("read_notification", params, socket) do
    Utilities.get_notification!(params["id"])
    |> Utilities.update_notification(Map.put(params, "status", true), socket)

    {:noreply, assign(socket, :user_notifications, get_data(socket.assigns))}
  end

  defp get_data(assigns) do
    if !is_nil(assigns.current_user) do
      IO.inspect(assigns.current_user)

      cond do
        assigns.role_department == 8 ->
          Utilities.get_notification_by_user_id(assigns.current_user.id)

        true ->
          Utilities.get_notification_by_role_id(
            assigns.role_department,
            assigns.current_user.id
          )
      end
    end
  end
end
