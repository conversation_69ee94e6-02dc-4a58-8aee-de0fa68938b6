defmodule Notification.SetUp.Messages do
  @moduledoc false
  alias Notification.{
    Notification.MessageDraft
  }

  alias Notification.Repo

  def init do
    [
      %{
        message: """
        Hello {{first_name}} {{last_name}},
        Welcome to All Pay Bulk Upload System
        Your login Credentials are:
        Username: {{username}}
        Password: {{password}}.
        """,
        service_type: "USER_CREATION"
      },
      %{
        message: """
        Hello {{merchant_name}},
        Welcome to AllPay! Your merchant account with code {{merchant_code}} was successfully created. Login Credentials are:
        Username: {{username}}
        Password: {{password}}.
        """,
        service_type: "MERCHANT_CREATION"
      },
      %{
        message: """
        Dear {{first_name}} {{last_name}},
        Your new Password is {{password}}.
        """,
        service_type: "PASSWORD_RESET"
      },
      %{
        message: """
        Dear {{merchant_name}},
        Your account has been funded with {{amount}}.
        Receipt No: {{receipt_no}}
        New Bal: {{running_balance}}.
        """,
        service_type: "FUND_MERCHANT_ACCOUNT"
      }
    ]
    |> Enum.each(fn data ->
      Repo.insert!(%MessageDraft{
        message: data[:message],
        service_type: data[:service_type]
      })
    end)
  end
end
