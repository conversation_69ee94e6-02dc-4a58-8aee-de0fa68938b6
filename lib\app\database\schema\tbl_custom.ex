defmodule App.Database.CustomDB do
  @moduledoc false
  use AppWeb, :schema

  @columns [
    :farmer_nrc
  ]

  schema "custom_fields" do
    field(:start_date, :date, virtual: true)
    field(:end_date, :date, virtual: true)
    field(:mobile_number, :string, virtual: true)
    field(:mobile, :string, virtual: true)
    field(:farmer_nrc, :string, virtual: true)
    field(:id_type, :string, virtual: true)

    field(:course_type_id, :integer, virtual: true)
    field(:level_id, :integer, virtual: true)
    field(:fee_id, {:array, :integer}, virtual: true)
  end

  def filter(struct, attrs) do
    cast(struct, attrs, [:start_date, :end_date])
  end

  def fee_mapping(struct, attrs) do
    cast(struct, attrs, [:course_type_id, :fee_id, :level_id])
  end

  def add_error_to(struct, message, field \\ "", params \\ %{}) do
    cast(struct, params, @columns)
    |> add_error(field, message)
  end

  def validate_mobile_number(
        %Ecto.Changeset{
          valid?: true,
          changes: %{
            mobile_number: phone
          }
        } = changeset
      ) do
    case validate_mobile_call(phone) do
      {:ok, %{phone_number: phone}} ->
        put_change(changeset, :mobile_number, "#{phone.country_code}#{phone.national_number}")

      {:error, %{status: _status}} ->
        add_error(changeset, :mobile_number, " number #{phone} is invalid")
    end
  end

  def validate_mobile_number(changeset), do: changeset

  def validate_mobile_number2(
        %Ecto.Changeset{
          valid?: true,
          changes: %{
            mobile: phone
          }
        } = changeset
      ) do
    case validate_mobile_call(phone) do
      {:ok, %{phone_number: phone}} ->
        put_change(changeset, :mobile, "#{phone.country_code}#{phone.national_number}")

      {:error, %{status: _status}} ->
        add_error(changeset, :mobile, " number #{phone} is invalid")
    end
  end

  def validate_mobile_number2(changeset), do: changeset

  def validate_mobile_call(phone) do
    sub_str2 = String.slice(String.trim("#{phone}"), 0..2)
    sub_str3 = String.slice(String.trim("#{phone}"), 0..1)

    phone =
      cond do
        sub_str2 in ~w(096 076 097 095 077 075) ->
          "26#{phone}"

        sub_str3 in ~w(96 76 97 95 77 75) ->
          "260#{phone}"

        true ->
          phone
      end

    with {:ok, phone_num} <- Phone.parse(phone),
         {:ok, phone_number} <- ExPhoneNumber.parse(phone, phone_num.a2),
         true <- ExPhoneNumber.is_possible_number?(phone_number) do
      {:ok, %{country: phone_num.country, phone_number: phone_number, status: "PENDING"}}
    else
      {:error, _reason} -> {:error, %{status: "Invalid phone number"}}
      false -> {:error, %{status: "Invalid phone number"}}
    end
  end

  # def valid_reference(changeset, old_reference \\ nil) do
  #   reference = get_change(changeset, :reference)

  #   if !is_nil(reference) or !is_nil(old_reference) do
  #     changeset
  #   else
  #     changeset
  #     |> delete_change(:reference)
  #     |> put_change(:reference, Transaction.transaction_reference_number())
  #   end
  # end

  def valid_reference_st(changeset, old_reference \\ nil) do
    reference = get_change(changeset, :reference)

    if !is_nil(reference) or !is_nil(old_reference) do
      changeset
    else
      changeset
      |> delete_change(:reference)
      |> put_change(:reference, "#{:os.system_time()}")
    end
  end

  def validate_date_before(changeset) do
    start_date = get_change(changeset, :start_date)
    end_date = get_change(changeset, :end_date)

    cond do
      start_date != nil && end_date != nil ->
        if Timex.before?(start_date, end_date) do
          changeset
        else
          add_error(changeset, :start_date, "should be less then end date")
        end

      end_date == nil ->
        validate_required(changeset, [:end_date])

      start_date == nil ->
        validate_required(changeset, [:start_date])

      true ->
        validate_required(changeset, [:start_date])
        |> validate_required([:end_date])
    end
  end

  def trim_data(changeset, target, size) do
    response = get_change(changeset, target)

    if response do
      changeset
      |> delete_change(target)
      |> put_change(target, String.slice(response, 0, size))
    else
      changeset
    end
  end
end
