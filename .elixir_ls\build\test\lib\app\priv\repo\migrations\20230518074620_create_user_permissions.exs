defmodule App.Repo.Migrations.CreateUserPermissions do
  use Ecto.Migration

  def change do
    create table(:user_permissions) do
      add :user_id, references(:tbl_users, on_delete: :delete_all), null: false
      add :permission_id, references(:permissions, on_delete: :delete_all), null: false
      add :granted_by, references(:tbl_users, on_delete: :nilify_all)
      add :reason, :text
      add :expires_at, :naive_datetime

      timestamps()
    end

    create unique_index(:user_permissions, [:user_id, :permission_id])
    create index(:user_permissions, [:user_id])
    create index(:user_permissions, [:permission_id])
    create index(:user_permissions, [:expires_at])
  end
end
