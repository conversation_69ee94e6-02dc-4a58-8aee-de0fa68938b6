defmodule App.SummaryDrafts do
  @moduledoc """
  The RepresentativeDrafts context.
  """

  import Ecto.Query, warn: false
  alias App.Repo
  alias App.SummaryDrafts.SummaryDraft
  alias Logs.Audit

  # App.SummaryDrafts.init()
  # App.SummaryDrafts.init1()

  @doc """
  Returns the list of summary_draft.

  ## Examples

      iex> list_summary_draft()
      [%SummaryDraft{}, ...]

  """
  def get_summary_draft!(id) do
    SummaryDraft
    |> where([a], a.license_id == ^id)
    |> select([a], %{
      template: a.template,
      service: a.service
    })
    |> limit(1)
    |> Repo.one()
  end

  def list_draft_dealer(id) do
    SummaryDraft
    |> where([a], a.license_id == ^id)
    |> select([a], %{
      template: a.template,
      service: a.service
    })
    |> limit(1)
    |> Repo.one()
  end

  def render(template, data, record) when is_binary(template) and is_map(data) do
    {rendered_template, _record} =
      Enum.reduce(data, {template, record}, fn {key, value}, {acc, rec} ->
        {String.replace(acc, "{{#{key}}}", to_string(value)), rec}
      end)

    rendered_template
  end

  def get_licence_drafts_preload!(id) do
    SummaryDraft
    |> where([a], a.id == ^id)
    |> preload([:license])
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Gets a single summary_draft.

  Raises `Ecto.NoResultsError` if the Representative draft does not exist.

  ## Examples

      iex> get_summary_draft!(123)
      %SummaryDraft{}

      iex> get_summary_draft!(456)
      ** (Ecto.NoResultsError)

  """
  def get_summary_draft!(id), do: Repo.get!(SummaryDraft, id)

  @doc """
  Creates a summary_draft.

  ## Examples

      iex> create_summary_draft(%{field: value})
      {:ok, %SummaryDraft{}}

      iex> create_summary_draft(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_summary_draft(attrs \\ %{}) do
    %SummaryDraft{}
    |> SummaryDraft.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a summary_draft.

  ## Examples

      iex> update_summary_draft(summary_draft, %{field: new_value})
      {:ok, %SummaryDraft{}}

      iex> update_summary_draft(summary_draft, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_summary_draft(%SummaryDraft{} = summary_draft, attrs) do
    summary_draft
    |> SummaryDraft.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a summary_draft.

  ## Examples

      iex> delete_summary_draft(summary_draft)
      {:ok, %SummaryDraft{}}

      iex> delete_summary_draft(summary_draft)
      {:error, %Ecto.Changeset{}}

  """
  def delete_summary_draft(%SummaryDraft{} = summary_draft) do
    Repo.delete(summary_draft)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking summary_draft changes.

  ## Examples

      iex> change_summary_draft(summary_draft)
      %Ecto.Changeset{data: %SummaryDraft{}}

  """
  def change_summary_draft1(%SummaryDraft{} = summary_draft, attrs \\ %{}) do
    SummaryDraft.changeset(summary_draft, attrs)
  end

  def change_summary_draft(attrs \\ %{}) do
    SummaryDraft.changeset(%SummaryDraft{}, attrs)
  end

  def update_draft(%SummaryDraft{} = summary_draft, attrs, socket) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "representative_licence_draft",
      SummaryDraft.changeset(summary_draft, attrs)
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} Edited Representative Licence Draft",
      "UPDATE",
      attrs,
      "Representative Licence Draft Configuration"
    )
    |> Repo.transaction()
  end

  #  def change_licence_draft(attrs \\ %{}) do
  #    LicenceDraft.changeset(%LicenceDraft{}, attrs)
  #  end
end
