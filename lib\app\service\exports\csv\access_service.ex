defmodule App.Service.Export.CsvAccessService do
  @moduledoc false
  alias App.Services.Table.AccessRoles
  alias App.Service.Export.Functions

  @headers [
    "DATE",
    "NAME",
    "DESCRIPTION",
    "USER COUNT",
    "STATUS"
  ]

  # access_service

  def index(payload) do
    AccessRoles.export(payload)
    |> Stream.map(
      &[
        &1.inserted_at,
        &1.name,
        &1.description,
        &1.user,
        Functions.department_roles_management_status(&1.status)
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Access Roles Management"],
              ["", "", "", "", ""],
              @headers,
              ["", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
