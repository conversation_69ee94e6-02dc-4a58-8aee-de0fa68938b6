defmodule Notification.Sms do
  @moduledoc """
  The MessagesDraft context.
  """

  alias Logs.Audit
  import Ecto.Query, warn: false
  alias Notification.Notification.SmsLogs
  alias Notify.NotifyRepo, as: Repo

  def get_message!(id) do
    SmsLogs
    |> where([a], a.id == ^id)
    |> select([a], %{
      id: a.id,
      initiator: a.initiator_id,
      country: a.country,
      smsc_msg_id: a.smsc_msg_id,
      sent_at: a.sent_at
    })
    |> Repo.one()
    |> case do
      nil -> nil
      message -> Map.put(message, :initiator, Accounts.get_lastname_by_id(message.initiator))
    end
  end

  def pending_msg_count(client_id) do
    SmsLogs
    |> where(
      [a],
      a.client_id == ^client_id and a.status in ~w(PENDING UNKNOWN_ERROR NETWORK_FAILURE)
    )
    |> Repo.aggregate(:sum, :count)
  end

  def confirm_client_type(client, count, include_log_count? \\ true)
  def confirm_client_type(%{payment_type: "POSTPAID"}, _count, _include_log_count?), do: true

  def confirm_client_type(%{id: id}, count, include_log_count?) do
    total_pending = if include_log_count?, do: pending_msg_count(id)
    total_count = (total_pending || 0) + count

    Purchases.current_bundle(id)
    |> confirm_bundle_limit(total_count)
  end

  def confirm_client_type(_, _, _), do: false

  defp confirm_bundle_limit(%{bundle: bundle}, count) do
    case Decimal.compare(bundle, count) do
      bundle_limit when bundle_limit in [:gt, :eq] -> true
      _ -> false
    end
  end

  def prepare_single_sms(remote_ip, socket, contact, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "sms",
      SmsLogs.changeset(
        %SmsLogs{},
        Map.merge(attrs, %{
          "client_id" => contact.client_id,
          "initiator_id" => contact.id,
          "remote_ip" => remote_ip,
          "type" => "SINGLE"
        })
      )
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email}
        Initiated a Single SMS to mobile number: #{attrs["mobile"]}",
      "SINGLE SMS",
      attrs,
      "Messaging"
    )
    |> Repo.transaction()
  end

  def bulk_upload_messages(socket, client, entries, attrs) do
    multi =
      Ecto.Multi.new()
      |> Audit.create_system_log_session_live_multi(
        socket,
        "User: #{socket.assigns.current_user.email}
        Initiated a Broadcast SMS from sender: #{attrs["sender"]}",
        "BROADCAST SMS",
        attrs,
        "Messaging"
      )

    Enum.with_index(entries)
    |> Enum.reduce(multi, fn {entry, idx}, multi ->
      Ecto.Multi.insert(
        multi,
        {:messages, idx},
        SmsLogs.changeset(
          %SmsLogs{},
          %{
            "client_id" => client.id,
            "initiator_id" => socket.assigns.current_user.id,
            "remote_ip" => socket.assigns.browser_info["remote_ip"],
            "mobile" => entry["col1"],
            "type" => "BULK",
            "message" => attrs["message"],
            "sender" => attrs["sender"],
            "sender_id" => attrs["sender_id"],
            "source" => attrs["source"]
          }
        )
      )
    end)
    |> Repo.transaction(timeout: :infinity)
  end

  def bulk_multicast_messages(socket, client, entries, attrs) do
    multi =
      Ecto.Multi.new()
      |> Audit.create_system_log_session_live_multi(
        socket,
        "User: #{socket.assigns.current_user.email}
        Initiated Multicast SMS from sender: #{attrs["sender"]}",
        "MULTICAST SMS",
        attrs,
        "Messaging"
      )

    Enum.with_index(entries)
    |> Enum.reduce(multi, fn {entry, idx}, multi ->
      Ecto.Multi.insert(
        multi,
        {:messages, idx},
        SmsLogs.changeset(
          %SmsLogs{},
          %{
            "client_id" => client.id,
            "initiator_id" => socket.assigns.current_user.id,
            "remote_ip" => socket.assigns.browser_info["remote_ip"],
            "mobile" => entry["col1"],
            "message" => entry["col2"],
            "type" => "BULK",
            "sender" => attrs["sender"],
            "sender_id" => attrs["sender_id"],
            "source" => attrs["source"]
          }
        )
      )
    end)
    |> Repo.transaction(timeout: :infinity)
  end

  def change_sms_logs(%SmsLogs{} = sms, attrs \\ %{}) do
    SmsLogs.changeset(sms, attrs)
  end

  def send_change_sms_logs(%SmsLogs{} = sms, attrs \\ %{}) do
    SmsLogs.send_changeset(sms, attrs)
  end

  def change_sms_bulk_logs(%SmsLogs{} = sms, attrs \\ %{}) do
    SmsLogs.upload_changeset(sms, attrs)
  end

  def change_sms_logs_upload(attrs \\ %{}) do
    SmsLogs.upload_changeset(%SmsLogs{}, attrs)
  end

  def change_sms_schedule_multicast_changeset(attrs \\ %{}) do
    SmsLogs.schedule_multicast_changeset(%SmsLogs{}, attrs)
  end

  def change_sms_multicast_changeset(attrs \\ %{}) do
    SmsLogs.multicast_changeset(%SmsLogs{}, attrs)
  end

  def change_sms_logs_schedule(attrs \\ %{}) do
    SmsLogs.schedule_changeset(%SmsLogs{}, attrs)
  end

  defp concat_multi_sm_params(params, mobile, index, msg) do
    Map.put(params, "mobile", "#{mobile}")
    |> Map.put("message", "#{Enum.at(msg, index)}")
  end

  # ---------------------- file persistence -----------------------#
  defp is_valide_file(%{"mobile" => mobile}) do
    if upload = mobile do
      case Path.extname(upload.filename) do
        ext when ext in ~w(.xlsx .XLSX .xls .XLS) ->
          destin_path = persist(upload)

          case Xlsxir.multi_extract(destin_path, 0, false, extract_to: :memory) do
            {:ok, table_id} ->
              row_count = Xlsxir.get_info(table_id, :rows)
              Xlsxir.close(table_id)

              case row_count do
                rows when rows in 1..300_000 ->
                  {:ok, upload.filename, destin_path, row_count}

                _ ->
                  {:error,
                   "Mobile number limit exceeded. maximum limit is 300,000 mobile numbers per file"}
              end

            {:error, reason} ->
              {:error, reason}
          end

        _ ->
          {:error, "Invalid File Format"}
      end
    else
      {:error, "No File Uploaded"}
    end
  end

  defp persist(%Plug.Upload{filename: filename, path: path}) do
    destin_path = Path.join(@data_dir, filename)

    destin_path =
      with true <- File.exists?(destin_path) do
        extname = Path.extname(filename)

        file =
          filename
          |> Path.basename(Path.extname(filename))

        filename = "#{file}_#{:os.system_time(:seconds)}#{extname}"
        Path.join(@data_dir, filename)
      else
        false ->
          destin_path
      end

    File.cp(path, destin_path)
    destin_path
  end

  def perform(remote_ip, contact, attrs, "single_sms") do
    contact = for {key, val} <- contact, into: %{}, do: {String.to_atom(key), val}
    process_single_sm(remote_ip, contact, attrs)
  end

  def process_single_sm(remote_ip, contact, attrs) do
    prepare_single_sm(remote_ip, contact, attrs)
    |> Repo.transaction()
    |> case do
      {:ok, %{sm: sm, log: _log}} ->
        with "SENT" <- sm.status do
          Task.start(fn ->
            PbsSms.Workers.SubmitSmWorker.perform(sm)
          end)
        end

        {:info, "SMS Submission Successful"}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        reason = traverse_errors(failed_value.errors) |> List.first()
        {:error, reason}
    end
  end

  defp prepare_single_sm(remote_ip, contact, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "sm",
      SmsLogs.changeset(
        %SmsLogs{},
        Map.merge(attrs, %{
          "client_id" => contact.id,
          "initiator_id" => contact.id,
          "remote_ip" => remote_ip,
          "type" => "SINGLE"
        })
      )
    )
  end

  def process_broadcast_sm(params, user_id, client_id, remote_ip, path) do
    {:ok, recipients} = extract_xlsx(path)

    prepare_broadcast_sm(params, user_id, client_id, remote_ip, recipients)
    |> Repo.transaction(timeout: :infinity)
    |> case do
      {:ok, _multi_sms_records} ->
        {:ok, "successfully logged broadcast recipients"}

      {:error, _, changeset, _} ->
        reason = traverse_errors(changeset.errors) |> List.first()
        {:error, reason}
    end
  end

  # ------------------ xlsx extract ------------------------------------#
  defp extract_xlsx(path) do
    case Xlsxir.multi_extract(path, 0, false, extract_to: :memory) do
      {:ok, id} ->
        recipients =
          Xlsxir.get_col(id, "A")
          |> Enum.reject(&(String.trim("#{&1}") == ""))
          |> Enum.uniq()

        Xlsxir.close(id)
        {:ok, recipients}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp multi_extract_xlsx(path) do
    case Xlsxir.multi_extract(path, 0, false, extract_to: :memory) do
      {:ok, id} ->
        recipients =
          Xlsxir.get_col(id, "A")
          |> Enum.reject(&(String.trim("#{&1}") == ""))

        messages =
          Xlsxir.get_col(id, "B")
          |> Enum.reject(&(String.trim("#{&1}") == ""))

        Xlsxir.close(id)
        {:ok, recipients, messages}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp handle_multi_req(conn, params, enqueue) do
    contact = conn.assigns.client

    with {:ok, filename, destin_path, _rows} <- is_valide_file(params) do
      {:ok, mobiles, msg} = multi_extract_xlsx(destin_path)

      cond do
        length(mobiles) == length(msg) ->
          cond do
            Enum.any?(msg, &(String.length(inspect(&1)) > 1_200)) ->
              {:error, "INVALID_MESSAGE_LENGTH"}

            true ->
              total_messages = App.Service.Functions.SMS.MulticastUpload.handle_msg_count(msg)

              case confirm_client_type(Repo.get_by(Client, id: contact.client_id), total_messages) do
                true ->
                  params = Map.put(params, "filename", filename)
                  remote_ip = Tuple.to_list(conn.remote_ip) |> Enum.join(".")
                  enqueue.(contact.client_id, contact.id, params, remote_ip, destin_path)
                  {:info, "Upload successful for multicast sms"}

                false ->
                  {:error, "INSUFFICIENT_BUNDLE_LIMIT"}
              end
          end

        true ->
          {:error, "RECIPIENTS_AND_MESSAGES_NOT_MATCH"}
      end
    else
      {:error, reason} ->
        {:error, reason}
    end
  end

  defp prepare_broadcast_sm(params, initiator_id, client_id, remote_ip, recipients) do
    recipients
    |> Stream.map(&Map.put(params, "mobile", "#{&1}"))
    |> Stream.with_index()
    |> Stream.map(fn {result, index} ->
      changeset =
        %SmsLogs{
          client_id: client_id,
          initiator_id: initiator_id,
          remote_ip: remote_ip,
          type: "BULK"
        }
        |> SmsLogs.changeset(result)

      Ecto.Multi.insert(Ecto.Multi.new(), Integer.to_string(index), changeset)
    end)
    |> Enum.reduce(Ecto.Multi.new(), &Ecto.Multi.append/2)
  end

  defp enqueue_broadcast_sm(client_id, user_id, params, remote_ip, path) do
    schedule_datetime = schedule_date_time(params)
    schedule_datetime = Timex.shift(schedule_datetime, hours: -2)

    Exq.enqueue_at(
      Exq,
      "bulk",
      schedule_datetime,
      Notification.MessagesDrafts,
      [client_id, user_id, params, remote_ip, path, :broadcast]
    )
  end

  defp is_valide_broadcast(%{"message" => text, "sender_id" => sender}) do
    with false <- String.trim(sender) == "",
         false <- String.trim(text) == "",
         true <- String.length(text) <= 1200 do
      true
    else
      false ->
        {:error, "Message length can't be more than 1,200"}

      true ->
        {:error, "Message or Sender can't be blank"}
        # true -> {:error, "Message can't be blank"}
    end
  end

  # ------------------------ Schedule DateTime -------------------------#
  defp schedule_date_time(params) do
    {_, time, _} =
      case params["date"] do
        nil ->
          {:ok, DateTime.utc_now() |> DateTime.truncate(:second), 0}

        _ ->
          DateTime.from_iso8601("#{params["date"]} #{params["time"]}:00Z")
      end

    time
  end

  def traverse_errors(errors) do
    for {key, {msg, _opts}} <- errors, do: "#{String.upcase(to_string(key))} #{msg}"
  end

  #    def transactional_sms(recipient) do
  #      recipient = for {key, val} <- recipient, into: %{}, do: {String.to_atom(key), val}
  #      {:ok, _resp} = SubmitSmWorker.perform(recipient)
  #    end
end
