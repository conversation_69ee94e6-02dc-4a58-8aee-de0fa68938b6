defmodule AppWeb.Components.FormNav.WizardFormNav do
  @moduledoc false
  use AppWeb, :live_component

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <h2 class="sr-only">Steps</h2>
      <div>
        <ol class={"grid grid-cols-1 backdrop-blur-sm divide-x overflow-hidden rounded-lg text-sm font-medium text-center border border-gray-300 text-gray-500 sm:text-base #{ "grid-cols-#{length(@menu)}" }"}>
          <%= for james <- @menu do %>
            <li class={"#{ if james["position"] != @first["position"] || james["position"] != @last["position"]  do
              "relative"
              end} flex items-center justify-center gap-2 p-4 transition-all duration-500 transform hover:scale-105 #{ if james["position"] <= @current_position do
              "text-white bg-brand-10 animate-step-fade"
              end}"}>
              <p class="leading-none">
                <strong class="block font-medium"><%= james["name"] %></strong>
              </p>
              <%= if james["position"] == @current_position do %>
                <svg
                  class="w-3 h-3 text-white ms-2 sm:ms-4 rtl:rotate-180 animate-bounce"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 12 10"
                >
                  <path
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="m7 9 4-4-4-4M1 9l4-4-4-4"
                  />
                </svg>
              <% end %>
            </li>
          <% end %>
        </ol>
      </div>
    </div>
    """
  end

  @impl true
  def update(%{menu_list: menu, current_position: _} = assigns, socket) do
    list = Enum.sort(menu, &(&1["position"] < &2["position"]))

    {
      :ok,
      socket
      |> assign(assigns)
      |> assign(menu: list)
      |> assign(first: List.first(list))
      |> assign(last: List.last(list))
    }
  end
end
