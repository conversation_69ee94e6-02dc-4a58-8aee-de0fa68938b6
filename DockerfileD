# Build arguments
ARG ELIXIR_VERSION=1.14.5
ARG OTP_VERSION=26.2.2
ARG DEBIAN_VERSION=bullseye-20240130-slim
ARG BUILDER_IMAGE="hexpm/elixir:${ELIXIR_VERSION}-erlang-${OTP_VERSION}-debian-${DEBIAN_VERSION}"

# Assets builder stage
FROM node:20.18 as assets_builder
WORKDIR /app/assets
COPY assets .
RUN npm install

# App builder stage
FROM ${BUILDER_IMAGE} as app_builder

# Install build dependencies with standard apt commands
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    git \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Set memory-related environment variables for mix and erlang
ENV ERL_FLAGS="+MBas aoffcbf +MHas aoffcbf +MBlmbcs 512 +MHlmbcs 512 +MMmcs 30"
ENV MIX_ENV=prod

# Install hex + rebar with retry logic
RUN mix local.hex --force && \
    mix local.rebar --force

# Copy only necessary files for dependency installation
COPY mix.exs mix.lock ./
COPY config config

# Get and compile dependencies with memory optimization
RUN mix deps.get --only $MIX_ENV && \
    mix deps.compile --no-debug-info

# Copy application files
COPY priv priv
COPY lib lib
COPY --from=assets_builder /app/assets ./assets

# Compile assets
RUN mix assets.deploy

# Compile the release with memory optimization
RUN mix compile --no-debug-info

# Copy runtime config and release files
COPY config/runtime.exs config/
COPY rel rel

# Create release with memory optimization
RUN mix release --overwrite

# Final stage - Use Debian Bullseye
FROM debian:${DEBIAN_VERSION}

ENV DEBIAN_FRONTEND=noninteractive

# Set the locale
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# Install runtime dependencies including wkhtmltopdf
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    openssl \
    ca-certificates \
    inotify-tools \
    wkhtmltopdf \
    locales \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
RUN chown nobody /app

# Environment variables
ENV MIX_ENV="prod"
ARG CLIENT_DATABASE_URL=ecto://postgres:4df7c642b5a74fc8@srv-captain--postgres/pbs_sms_gateway_client
ARG NOTIFY_DATABASE_URL=ecto://postgres:4df7c642b5a74fc8@srv-captain--postgres/pbs_sms_gateway_notifications
ARG STATISTICS_DATABASE_URL=ecto://postgres:4df7c642b5a74fc8@srv-captain--postgres/pbs_sms_gateway_statistics
ARG LOGS_DATABASE_URL=ecto://postgres:4df7c642b5a74fc8@srv-captain--postgres/pbs_sms_gateway_logs
ARG PHX_SERVER=true
ARG SECRET_KEY_BASE=Sxvuv1H57WW0E2ls7EdJq2JgFlrOa0EZbVpXiJHY9DORIiRP4AuWTFugzq+b8PUq
ARG PORT=80
ARG S_PORT=443

ENV CLIENT_DATABASE_URL=$CLIENT_DATABASE_URL
ENV NOTIFY_DATABASE_URL=$NOTIFY_DATABASE_URL
ENV STATISTICS_DATABASE_URL=$STATISTICS_DATABASE_URL
ENV LOGS_DATABASE_URL=$LOGS_DATABASE_URL
ENV PHX_SERVER=$PHX_SERVER
ENV SECRET_KEY_BASE=$SECRET_KEY_BASE
ENV PORT=$PORT
ENV S_PORT=$S_PORT

# Copy release
COPY --from=app_builder --chown=nobody:root /app/_build/${MIX_ENV}/rel/pbs_gw_client_rel ./

USER nobody

CMD ["/app/bin/pbs_gw_client_rel", "start"]
