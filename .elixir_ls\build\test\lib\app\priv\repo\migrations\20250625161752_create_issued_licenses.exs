defmodule App.Repo.Migrations.CreateIssuedLicenses do
  use Ecto.Migration

  def change do
    create table(:issued_licenses) do
      add :status, :integer, default: 1
      add :reason, :string
      add :expiring_date, :date
      add :certificate_id, references(:licence_certificates, on_delete: :nothing)
      add :application_id, references(:user_license_mapping, on_delete: :nothing)
      add :holder_id, references(:tbl_users, on_delete: :nothing)
      add :license_id, references(:licenses, on_delete: :nothing)
      timestamps(type: :utc_datetime)
    end
  end
end
