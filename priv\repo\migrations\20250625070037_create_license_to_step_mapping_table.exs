defmodule App.Repo.Migrations.CreateLicenseToStepMappingTable do
  use Ecto.Migration

  def change do
    create table(:license_steps_mapping) do
      add :number, :integer, default: 1
      add :license_id, references(:licenses, on_delete: :nothing)
      add :step_id, references(:registration_pages, on_delete: :nothing)
      add :status, :integer, default: 0

      timestamps(type: :utc_datetime)
    end

    alter table(:user_license_mapping) do
      add :current_step, :integer, default: 1
    end

    create unique_index(:license_steps_mapping, [:license_id, :step_id, :number])
  end
end
