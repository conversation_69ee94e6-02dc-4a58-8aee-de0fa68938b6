defmodule App.SetUp.ApprovalLevels do
  alias App.Licenses.ApprovalLevels
  alias App.Repo

  defp run_seeds_db(schema), do: Repo.insert!(schema)

  def init do
    run_seeds_db(%ApprovalLevels{
      approval_status: 1,
      status: 1,
      department_id: 4,
      role_id: 8,
      categories_id: 3
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 2,
      status: 1,
      department_id: 3,
      role_id: 4,
      categories_id: 3
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 3,
      status: 1,
      department_id: 3,
      count_down: 1,
      genarate_summary: 1,
      role_id: 5,
      categories_id: 3
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 4,
      status: 1,
      department_id: 3,
      role_id: 4,
      categories_id: 3
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 5,
      status: 1,
      department_id: 3,
      role_id: 5,
      categories_id: 3
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 6,
      status: 1,
      department_id: 3,
      role_id: 7,
      categories_id: 3
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 7,
      status: 1,
      department_id: 3,
      role_id: 6,
      categories_id: 3
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 8,
      status: 1,
      department_id: 3,
      role_id: 17,
      categories_id: 3,
      condition_tracking: 1
    })

    run_seeds_db(%ApprovalLevels{
      approval_status: 9,
      status: 1,
      department_id: 3,
      role_id: 18,
      categories_id: 3
    })
  end
end
