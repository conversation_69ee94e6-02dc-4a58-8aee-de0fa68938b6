defmodule App.Service.Export.CsvDepartmentSRolesManagementService do
  @moduledoc false
  alias App.Service.Table.DepartmentRoles

  alias App.Service.Export.Functions

  @headers [
    "DATE",
    "NAME",
    "DESCRIPTION",
    "ACCESS ROLE COUNT",
    "STATUS"
  ]

  def index(payload) do
    DepartmentRoles.export(payload)
    |> Stream.map(
      &[
        &1.inserted_at,
        &1.name,
        &1.description,
        &1.access_roles,
        Functions.department_roles_management_status(&1.status)
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Departement Roles Management"],
              ["", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
