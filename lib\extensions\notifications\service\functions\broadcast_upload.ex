defmodule App.Service.Functions.SMS.BroadcastUpload do
  use AppWeb, :file_function

  alias Notification.MessagesDrafts
  alias Notification.Notification.SmsLogs
  alias App.Database.CustomDB

  def execute(socket, path, params) do
    Extract.validate_excel_and_csv(path)
    |> case do
      {:ok, col_count, file} ->
        cond do
          col_count == 0 ->
            {:error, :start, "No records found in the uploaded file."}

          col_count < 1 ->
            {:error, :start, "System expecting 1 columns but only got #{col_count}."}

          col_count > 1 ->
            {:error, :start, "System expecting 1 columns but got #{col_count}."}

          true ->
            file
            |> case do
              {:ok, txn} ->
                extraction_process(path, txn, params, socket)

              {:error, message} ->
                {:error, :start, message}
            end
        end

      error ->
        error
    end
  end

  defp extraction_process(path, txn, params, socket) do
    msg_count =
      MessagesDrafts.confirm_client_type(
        socket.assigns.client,
        SmsLogs.msg_count(params["message"]) * length(txn)
      )

    txn
    |> Extract.add_index()
    |> Enum.map(fn x ->
      cond do
        String.trim(x["col1"]) == "" ->
          message(0, "Mobile Number cannot be blank", x)

        !validate_mobile_number(to_string(x["col1"])) ->
          message(0, "Invalid Phone Number", x)

        true ->
          case msg_count do
            true -> message(1, "Valid record", x)
            false -> message(0, "INSUFFICIENT_BUNDLE_LIMIT", x)
          end
      end
    end)
    |> Extract.finalize_process(
      path,
      # successful message, encase the process is a success
      "All Records in the file are valid, proceed to update them.",
      # failed message, encase the process failed
      "One or more transactions in the file are invalid, kindly update them before you can proceed."
    )
  end

  def process_file(socket, client, attrs, params) do
    entries = Jason.decode!(attrs["entries"])

    MessagesDrafts.bulk_upload_messages(socket, client, entries, params)
    |> case do
      {:ok, txn} ->
        {:ok, "Successfully added #{Enum.count(entries)} messages", txn}

      {:error, message} ->
        {:error, message}
    end
  end

  def message(status, message, params) do
    %{
      status: status,
      col1: params["col1"],
      key: params["key"] + 1,
      message: message
    }
  end

  defp validate_mobile_number(mobile) do
    CustomDB.validate_mobile_call(mobile)
    |> case do
      {:error, _} -> false
      {:ok, _} -> true
    end
  end
end
