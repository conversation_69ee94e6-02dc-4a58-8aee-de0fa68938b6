defmodule AppWeb.LicenceDetailsLive.UploadfileComponent do
  use AppWeb, :live_component
  alias App.LicenseConditions
  alias App.Licenses.{LicenseConditionsMapping}
  alias App.Files.UploadedFile
  on_mount({AppWeb.UserAuth, :mount_current_user})

  @impl true
  def render(assigns) do
    ~H"""
    <div class="conditions-container">
      <%= if @condition == nil do %>
        <label class="inline-block bg-yellow-100 text-yellow-800 text-sm font-medium px-3 py-1 rounded-full">
          No Action needed
        </label>
      <% else %>
        <h1 class="text-lg font-medium mb-4">
          Recommended condition(s) to be met:
          <label class="inline font-medium">"<%= @condition.name %>"</label>
        </h1>
        <center>
          <p>"<%= @condition.description %>"</p>
        </center>

        <.simple_form
          for={@form}
          id="condition-form"
          phx-submit="upload"
          phx-target={@myself}
          phx-change="validate"
        >
          <label
            phx-drop-target={@uploads.file.ref}
            class="w-full border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500"
            aria-label="Upload file"
          >
            <%= if @uploads.file.entries == [] do %>
              <div class="flex flex-col items-center justify-center py-4">
                <svg
                  aria-hidden="true"
                  class="w-8 h-8 mb-2 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                  >
                  </path>
                </svg>
                <p class="mb-1 text-xs text-gray-500 dark:text-gray-400">
                  <span class="font-semibold">Click to upload</span> or drag and drop the <b>file</b>
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">png, jpg, jpeg</p>
              </div>
            <% end %>
            <%= if @uploads.file.entries != [] do %>
              <div class="p-3 flex justify-center items-center">
                <!-- Center the entire preview area -->
                <%= for entry <- @uploads.file.entries do %>
                  <div class="relative flex items-center rounded-lg bg-white shadow-md overflow-hidden max-w-xs w-full">
                    <!-- Increased flexibility with max-w-xs -->
                    <div class="w-full h-48 flex items-center justify-center">
                      <!-- Fixed height for consistency, centered content -->
                      <.live_img_preview entry={entry} class="max-w-full max-h-full object-contain" />
                      <!-- Scale file to fit while maintaining aspect ratio -->
                    </div>
                    <div class="ml-3 mt-2">
                      <h2 class="text-sm font-semibold"><%= entry.client_name %></h2>
                      <p class="mt-1 text-xs text-gray-500">
                        <progress value={entry.progress} max="100"><%= entry.progress %>%</progress>
                      </p>
                      <%= for err <- upload_errors(@uploads.file, entry) do %>
                        <p class="mt-1 text-rose-500 text-xs"><%= error_to_string(err) %></p>
                      <% end %>
                    </div>
                    <button
                      type="button"
                      phx-click="cancel-entry"
                      phx-value-ref={entry.ref}
                      phx-target={@myself}
                      class="p-1 rounded-full text-gray-400 hover:text-red-500 focus:outline-none"
                    >
                      <svg
                        class="h-4 w-4"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                  </div>
                <% end %>
              </div>
            <% end %>
            <.live_file_input upload={@uploads.file} class="hidden" />
          </label>
          <:actions>
            <.button
              class="w-full sm:w-auto mx-auto mt-3 text-sm py-1 px-3"
              phx-disable-with="Saving..."
            >
              Upload
            </.button>
          </:actions>
        </.simple_form>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{action: uploaded_file} = assigns, socket) do
    changeset = App.Licenses.change_upload_file(%UploadedFile{})

    {:ok,
     socket
     |> assign(assigns)
     |> assign_form(changeset)
     |> assign(:uploaded_files, [])
     |> assign(
       :condition,
       App.LicenseConditions.query_condition_by_user_id(assigns.application_id)
     )
     |> assign(
       :upload_form,
       to_form(App.Licenses.change_upload_file(%UploadedFile{}), as: "upload")
     )
     |> allow_upload(:file,
       accept: ~w(.jpg .jpeg .png .pdf .docx .doc .xlsx),
       max_entries: 1,
       max_file_size: 10_000_000
     )
     |> assign(db_files: App.Licenses.get_uploaded_files_by_application!(assigns.current_user.id))
     |> allow_upload(:file,
       accept: :any,
       max_entries: 5,
       max_file_size: 5 * 1024 * 1024,
       auto_upload: true
     )}
  end

  @impl true
  def handle_event("validate", params, socket) do
    changeset =
      %UploadedFile{}
      |> App.Licenses.change_upload_file(params)
      |> Map.put(:action, :validate)

    socket
    |> assign_form(changeset)
    |> noreply()
  end

  #  def handle_event("cancel-entry", params, socket) do
  #    cancel_upload(params, socket)
  #  end

  def handle_event("cancel-entry", %{"ref" => ref}, socket) do
    {:noreply, cancel_upload(socket, :file, ref)}
  end

  def cancel_upload(%{"ref" => ref}, socket) do
    {:noreply, cancel_upload(socket, :file, ref)}
  end

  def handle_event("upload", params, socket) do
    new_path = LiveFunctions.static_path("/uploads/approval_docs")

    file =
      consume_uploaded_entries(socket, :file, fn %{path: path}, entry ->
        file_name = "/#{Path.rootname(entry.client_name)}.#{LiveFunctions.ext(entry)}"
        dest = Path.join(new_path, file_name)

        if File.exists?(new_path <> "/") do
          File.cp(path, dest)
          {:ok, dest}
        else
          File.mkdir_p(new_path <> "/")
          File.cp_r(path, dest)
          {:ok, dest}
        end
      end)

    send(self(), {:upload_file, Enum.at(file, 0)})

    {
      :noreply,
      socket
      |> assign(loader: true)
    }
  end

  defp upload_file(socket, file) do
    # IO.inspect socket, label: "BOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOO"
    App.Licenses.upload_level_file_condition(socket, file)
    |> case do
      {:ok, _} ->
        socket
        |> assign(loader: false)
        |> success_message("File Successfully Uploaded.")

      {:error, error} ->
        socket
        |> assign(loader: false)
        |> error_message(error)
    end
  end

  def success_message(%{assigns: _assigns} = socket, message) do
    send(self(), {:get_data, socket.assigns.params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      # |> push_navigate(to: ~p"/license/applications")
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "success"
      )
    }
  end

  def error_message(%{assigns: _assigns} = socket, message) do
    send(self(), {:get_data, socket.assigns.params})

    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "error"
      )
    }
  end

  defp notify_parent(msg) do
    send(self(), {__MODULE__, msg})
  end

  def error_to_string(:too_large), do: "Too large"
  def error_to_string(:not_accepted), do: "You have selected an unacceptable file type"
  def error_to_string(:too_many_files), do: "You have selected too many files"
end
