defmodule App.Settings do
  @moduledoc """
  The Settings context.
  """

  import Ecto.Query, warn: false
  alias App.{Repo, Accounts.SenderId}

  alias App.Settings.Setting
  alias Logs.Audit

  alias App.Settings.SettingsConfig
  alias App.Settings.Service

  @topic "settings"

  def subscribe, do: Phoenix.PubSub.subscribe(App.PubSub, @topic)

  @doc """
  Returns the list of settings.

  ## Examples

      iex> list_settings()
      [%Setting{}, ...]

  """
  def list_settings do
    Setting
    |> order_by(asc: :id)
    |> Repo.all()
  end

  @doc """
  Gets a single setting.

  Raises `Ecto.NoResultsError` if the Setting does not exist.

  ## Examples

      iex> get_setting!(123)
      %Setting{}

      iex> get_setting!(456)
      ** (Ecto.NoResultsError)

  """
  def get_setting!(id), do: Repo.get!(Setting, id)

  def check_setting_status(name) do
    settings = Repo.get_by(Setting, name: name)
    if settings == nil, do: false, else: settings.status
  end

  def search_for_name(query) do
    Setting
    |> where([a], like(a.name, ^"%#{query}%"))
    |> order_by(asc: :id)
    |> Repo.all()
  end

  @doc """
  Creates a setting.

  ## Examples

      iex> create_setting(%{field: value})
      {:ok, %Setting{}}

      iex> create_setting(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_setting(attrs \\ %{}) do
    %Setting{}
    |> Setting.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a setting.

  ## Examples

      iex> update_setting(setting, %{field: new_value})
      {:ok, %Setting{}}

      iex> update_setting(setting, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_setting(%Setting{} = setting, attrs) do
    setting
    |> Setting.changeset(attrs)
    |> Repo.update()
  end

  @spec update_settings_status(
          %{
            :assigns => %{
              :browser_info => nil | maybe_improper_list() | map(),
              optional(any()) => any()
            },
            optional(any()) => any()
          },
          any(),
          any()
        ) :: {:error, any()} | {:ok, any()}
  def update_settings_status(socket, id, value, attrs \\ %{}, multiple \\ Ecto.Multi.new()) do
    status = if value == "true", do: true, else: false
    settings = get_setting!(id)

    Ecto.Multi.update(
      multiple,
      :setting,
      Setting.changeset(settings, Map.merge(attrs, %{"status" => status}))
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "Updated Settings [#{settings.name}] switch to #{status}",
      "UPDATE",
      attrs,
      "Settings"
    )
    |> Repo.transaction()
    |> notify_subs(:updated)
  end

  # def update_settings_status(socket, id, value, attrs \\ %{}, multiple \\ Ecto.Multi.new()) do

  #   status = if value, do: true, else: false
  #   settings = get_setting!(id)

  #   Ecto.Multi.update(
  #     multiple,
  #     :setting,
  #     Setting.changeset(settings, Map.merge(attrs, %{status: status}))
  #   )
  #   |> Audit.create_system_log_session_live_multi(
  #     socket,
  #     "Updated Settings [#{settings.name}] switch to #{status}",
  #     "UPDATE",
  #     attrs,
  #     "Settings"
  #   )
  #   |> Repo.transaction()
  #   |> notify_subs(:updated)
  # end

  @doc """
  Deletes a setting.

  ## Examples

      iex> delete_setting(setting)
      {:ok, %Setting{}}

      iex> delete_setting(setting)
      {:error, %Ecto.Changeset{}}

  """
  def delete_setting(%Setting{} = setting) do
    Repo.delete(setting)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking setting changes.

  ## Examples

      iex> change_setting(setting)
      %Ecto.Changeset{data: %Setting{}}

  """
  def change_setting(%Setting{} = setting, attrs \\ %{}) do
    Setting.changeset(setting, attrs)
  end

  @doc """
  Returns the list of settings_config.

  ## Examples

      iex> list_settings_config()
      [%SettingsConfig{}, ...]

  """
  def list_settings_config do
    SettingsConfig
    |> order_by(asc: :id)
    |> Repo.all()
  end

  def search_for_name_configuration(query) do
    SettingsConfig
    |> where([a], like(a.name, ^"%#{query}%"))
    |> order_by(asc: :id)
    |> Repo.all()
  end

  def get_configuration_by_name!(name) do
    SettingsConfig
    |> where([a], like(a.name, ^name))
    |> select([a], %{amount: a.value})
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Gets a single settings_config.

  Raises `Ecto.NoResultsError` if the Settings config does not exist.

  ## Examples

      iex> get_settings_config!(123)
      %SettingsConfig{}

      iex> get_settings_config!(456)
      ** (Ecto.NoResultsError)

  """
  def get_settings_config!(id), do: Repo.get!(SettingsConfig, id)

  def get_setting_configuration(name) do
    settings =
      if data = Cachex.get!(:system, "SettingConfiguration:#{name}") do
        data
      else
        data = Repo.get_by(SettingsConfig, name: name)
        Cachex.put(:system, "SettingConfiguration:#{name}", data, expire: :timer.minutes(5))
        data
      end

    if settings == nil, do: %SettingsConfig{}, else: settings
  end

  @doc """
  Creates a settings_config.

  ## Examples

      iex> create_settings_config(%{field: value})
      {:ok, %SettingsConfig{}}

      iex> create_settings_config(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_settings_config(attrs \\ %{}) do
    %SettingsConfig{}
    |> SettingsConfig.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a settings_config.

  ## Examples

      iex> update_settings_config(settings_config, %{field: new_value})
      {:ok, %SettingsConfig{}}

      iex> update_settings_config(settings_config, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_settings_config(%SettingsConfig{} = settings_config, attrs) do
    settings_config
    |> SettingsConfig.changeset(attrs)
    |> Repo.update()
  end

  def update_config_settings_value(list, socket) do
    Enum.with_index(list)
    |> Enum.reduce(Ecto.Multi.new(), fn {attrs, index}, multi ->
      Ecto.Multi.run(multi, {:settings, index}, fn _repo, _ ->
        {:ok, Repo.get(SettingsConfig, attrs.id)}
      end)
      |> Ecto.Multi.update({:update, index}, fn all ->
        settings = all[{:settings, index}]

        SettingsConfig.changeset(settings, attrs)
      end)
    end)
    |> Audit.create_system_log_session_live_multi(
      socket,
      "Updated advanced settings",
      "UPDATE",
      list,
      "Settings"
    )
    |> Repo.transaction()
    |> notify_subs(:updated)
  end

  @doc """
  Deletes a settings_config.

  ## Examples

      iex> delete_settings_config(settings_config)
      {:ok, %SettingsConfig{}}

      iex> delete_settings_config(settings_config)
      {:error, %Ecto.Changeset{}}

  """
  def delete_settings_config(%SettingsConfig{} = settings_config) do
    Repo.delete(settings_config)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking settings_config changes.

  ## Examples

      iex> change_settings_config(settings_config)
      %Ecto.Changeset{data: %SettingsConfig{}}

  """
  def change_settings_config(%SettingsConfig{} = settings_config, attrs \\ %{}) do
    SettingsConfig.changeset(settings_config, attrs)
  end

  def date_time_calculator(setting) do
    value = NumberF.to_int(setting.value || "0.0")

    case setting.value_type do
      "years" -> value * 365.3
      "days" -> value * 60 * 60 * 24
      "hours" -> value * 60 * 60
      "minutes" -> value * 60
      "seconds" -> value
      "percentage" -> value / 100
      _ -> value
    end
  end

  def notify_subs({:ok, result}, event) do
    Phoenix.PubSub.broadcast(App.PubSub, @topic, {__MODULE__, event, result})
    {:ok, result}
  end

  def notify_subs({:error, result}, _event), do: {:error, result}

  def notify_subs({:error, _failed_operation, failed_value, _changes_so_far}, _event),
    do: {:error, failed_value.errors}
end
