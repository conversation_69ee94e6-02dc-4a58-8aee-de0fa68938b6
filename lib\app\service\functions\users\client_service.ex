defmodule App.Service.ServiceClientMaintenance.Functions do
  alias App.Accounts

  def index(socket, attrs, function \\ "change_status") do
    # profile = Accounts.get_profile!(attrs["id"])
    client = App.Accounts.get_user!(attrs["id"])

    cond do
      # function == "change_status" ->
      #   Accounts.change_profile_status(socket, attrs, profile)
      function == "change_status_client" ->
        App.Accounts.change_cllent_status(socket, attrs, client)
    end
  end

  def create(socket, attrs) do
    Accounts.create_profile(socket, attrs)
    # |> case do
    #   {:ok, data} -> {:ok, data}
    #   {:error, error} -> {:error, error}
    #   {:error, _, error, _} -> {:error, error}
    # end
  end

  def update(socket, client, contact, attrs) do
    Accounts.update_profile(socket, client, contact, attrs)
    |> case do
      {:ok, data} -> {:ok, data}
      {:error, error} -> {:error, error}
      {:error, _, error, _} -> {:error, error}
    end
  end
end
