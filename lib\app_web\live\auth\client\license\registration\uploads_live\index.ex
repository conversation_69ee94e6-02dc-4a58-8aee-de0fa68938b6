defmodule AppWeb.Auth.Registration.UploadsLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias App.Licenses
  alias App.Validators.DynamicFormValidator
  alias AppWeb.Auth.RegistrationLive.Entry

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("license_registration", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Licence Registration", "Accessed Licence Registration Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      socket =
        assign(socket, data: [])
        |> assign(data_loader: true)
        |> assign(params: params)
        |> assign(live_socket_id: session["live_socket_id"])

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Licence Registration", "Accessed Licence Registration Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_data, params})

    {
      :noreply,
      socket
      |> assign(:params, params)
      |> apply_action(socket.assigns.live_action, params)
    }
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      {:get_data, params} ->
        get_update(socket, params)

      {AppWeb.LiveFunctions, message} ->
        send(self(), {:get_data, %{}})

        {
          :noreply,
          socket
          |> put_flash(:info, message)
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }
    end
  end

  @impl true
  def handle_event("cancel-entry", params, socket) do
    cancel_upload(params, socket)
  end

  @impl true
  def handle_event(target, attrs, %{assigns: assigns} = socket) do
    case target do
      #
      "submit_correction" ->
        submit_correction(attrs, socket)

      "doc_save" ->
        Entry.document_uploads(attrs["license_mapping"] || %{}, socket)
        |> case do
          {:ok, %{"license_registration" => record}} ->
            socket
            |> next_position(record)

          {:error, error} ->
            LiveFunctions.sweet_alert(
              socket,
              "Error processing registration: #{inspect(error)}",
              "error"
            )
        end
        |> noreply()

      "validate_doc" ->
        Entry.validate_doc(attrs, socket)

      "remove_file" ->
        remove_file(socket, attrs)

      "back" ->
        {:noreply, pre_position(socket)}
    end
  end

  def cancel_upload(%{"ref" => ref}, socket) do
    get_field =
      socket.assigns.upload_fields
      |> Enum.find(fn field ->
        Enum.any?(socket.assigns.uploads[field].entries, fn entry -> entry.ref == ref end)
      end)

    if get_field do
      {:noreply, cancel_upload(socket, get_field, ref)}
    else
      {:noreply, socket}
    end
  end

  def validate_doc(%{"_target" => [field]}, %{assigns: assigns} = socket) do
    upload_field = String.to_atom(field)

    attrs =
      assigns.upload_fields
      |> Enum.reduce(%{}, fn key, acc ->
        # check existing entries
        existing_entries =
          assigns.uploads[key]
          |> Map.get(:entries, [])
          |> Enum.map(& &1.client_name)
          |> Enum.at(0)

        # If the current key matches field validating, fetch the entries
        updated_entries =
          if key == upload_field do
            assigns.uploads[upload_field]
            |> Map.get(:entries, [])
            |> Enum.map(& &1.client_name)
            |> Enum.at(0)
          else
            existing_entries
          end

        Map.put(acc, key, updated_entries)
      end)

    # Use our dynamic validator for uploads
    changeset =
      DynamicFormValidator.validate_step(
        attrs,
        assigns.available_uploads,
        step: assigns.current_position
      )
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :upload_form, to_form(changeset, as: "upload"))}
  end

  def consume_upload(socket, upload_type, _attrs) do
    new_path = LiveFunctions.static_path("/uploads/licence_registration")

    consume_uploaded_entries(socket, upload_type, fn %{path: path}, entry ->
      file_name = "/#{Path.rootname(entry.client_name)}.#{LiveFunctions.ext(entry)}"
      dest = Path.join(new_path, file_name)

      if File.exists?(new_path <> "/") do
        File.cp(path, dest)
        {:ok, dest}
      else
        File.mkdir_p(new_path <> "/")
        File.cp_r(path, dest)
        {:ok, dest}
      end
    end)
  end

  defp remove_file(socket, attrs) do
    Licenses.update_data_key(
      socket,
      Map.drop(socket.assigns.record.data || %{}, [attrs["field"]])
    )
    |> case do
      {:ok, record} ->
        send(self(), {:get_data, socket.assigns.params})

        {
          :noreply,
          socket
          |> assign(:record, record)
        }

      {:error, error} ->
        LiveFunctions.sweet_alert(
          socket,
          "Error removing file: #{inspect(error)}",
          "error"
        )

        {:noreply, socket}
    end
  end

  def submit_correction(attrs, socket) do
    case Licenses.update_license_correction(socket, socket.assigns.record.id, attrs) do
      {:ok, _record} ->
        {
          :noreply,
          push_navigate(
            LiveFunctions.sweet_alert(socket, "Application updated Successfully", "success"),
            to: ~p"/dashboard"
          )
          |> assign(:doc_details, %{})
        }

      {:error, message} ->
        LiveFunctions.sweet_alert(socket, message, "error")

        {
          :noreply,
          assign(socket, data_loader: false)
        }
    end
  end

  defp get_update(socket, params) do
    Entry.init(socket, params, "uploads")
  end

  defp next_position(socket, record) do
    get_next_page =
      socket.assigns.steps
      |> Enum.find(fn step -> step.number > socket.assigns.current_position end)

    push_navigate(socket, to: "#{get_next_page.step.url}#{record.license_id}")
  end

  defp pre_position(socket) do
    get_prev_page =
      socket.assigns.steps
      |> Enum.filter(fn step -> step.number < socket.assigns.current_position end)
      |> Enum.max_by(& &1.number, fn -> nil end)

    Licenses.update_current_step(socket.assigns.record, get_prev_page.number)
    |> case do
      {:ok, record} ->
        push_navigate(socket, to: "#{get_prev_page.step.url}#{record.license_id}")

      {:error, _error} ->
        :error
    end
  end

  defp pre_position(socket), do: socket

  def error_to_string(:too_large), do: "Too large"
  def error_to_string(:not_accepted), do: "You have selected an unacceptable file type"
  def error_to_string(:too_many_files), do: "You have selected too many files"
end
