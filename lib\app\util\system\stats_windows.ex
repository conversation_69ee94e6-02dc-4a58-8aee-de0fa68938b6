defmodule StatsWindows do
  def memory_stats do
    command = "powershell"

    # Correct PowerShell command to get the memory stats
    args = [
      "-Command",
      "($os = Get-WmiObject -Class Win32_OperatingSystem); $os.TotalVisibleMemorySize, $os.FreePhysicalMemory"
    ]

    case System.cmd(command, args) do
      {output, 0} ->
        # Clean up the output by filtering out non-relevant lines
        cleaned_output =
          output
          # Split by line breaks
          |> String.split("\r\n")
          # Keep only lines with digits (Total and Free memory)
          |> Enum.filter(&String.match?(&1, ~r/^\d+$/))

        case cleaned_output do
          [total_memory_str, free_memory_str] ->
            # Convert to integers
            total_memory = String.to_integer(total_memory_str)
            free_memory = String.to_integer(free_memory_str)

            # Calculate used memory
            used_memory = total_memory - free_memory

            # Convert to GB (1 GB = 1024 * 1024 KB), round to 1 decimal place
            total_memory_gb = Float.round(total_memory / (1024 * 1024), 1)
            used_memory_gb = Float.round(used_memory / (1024 * 1024), 1)
            free_memory_gb = Float.round(free_memory / (1024 * 1024), 1)

            # Return memory stats
            %{
              used_memory: used_memory_gb,
              total_memory: total_memory_gb,
              free_memory: free_memory_gb
            }

          _ ->
            IO.inspect("Unable to parse memory stats")
            %{used_memory: 0, total_memory: 0, free_memory: 0}
        end

      _ ->
        IO.inspect("Unable to fetch memory stats")
        %{used_memory: 0, total_memory: 0, free_memory: 0}
    end
  end

  def cpu_stats do
    command = "powershell"

    args = [
      "-Command",
      "$cpu = Get-CimInstance Win32_Processor; " <>
        "Write-Host $cpu.NumberOfLogicalProcessors; " <>
        "if ($cpu.LoadPercentage -eq $null) { Write-Host 0 } else { Write-Host $cpu.LoadPercentage }"
    ]

    case System.cmd(command, args) do
      {output, 0} ->
        cleaned_output =
          output
          # Changed from "\r\n" to "\n"
          |> String.split("\n", trim: true)
          # Keep only numbers
          |> Enum.filter(&String.match?(&1, ~r/^\d+$/))

        case cleaned_output do
          # Ensure at least two values
          [cores_str, usage_str | _] ->
            %{
              total_cpu_cores: String.to_integer(cores_str),
              used_cpu_cores: String.to_integer(usage_str)
            }

          _ ->
            IO.puts("Unexpected output format after processing: #{inspect(cleaned_output)}")
            %{total_cpu_cores: 0, used_cpu_cores: 0}
        end

      {error_output, _} ->
        IO.puts("PowerShell command failed: #{inspect(error_output)}")
        %{total_cpu_cores: 0, used_cpu_cores: 0}
    end
  end

  def storage_stats do
    command = "powershell"
    # Alternative command using Get-PSDrive
    args = [
      "-Command",
      """
      $drive = Get-PSDrive C | Select-Object Used,Free;
      Write-Output $drive.Used;
      Write-Output ($drive.Used + $drive.Free)
      """
    ]

    case System.cmd(command, args) do
      {output, 0} ->
        # Clean and parse the output
        values =
          output
          |> String.split("\r\n", trim: true)
          |> Enum.filter(&String.match?(&1, ~r/^\d+$/))
          |> Enum.take(2)

        case values do
          [used_str, total_str] ->
            used_bytes = String.to_integer(used_str)
            total_bytes = String.to_integer(total_str)

            # Get the appropriate unit based on the larger value
            {larger_value, unit} = format_bytes(max(used_bytes, total_bytes))

            # Convert both values to the same unit
            used_converted = SystemStats.bytes_to_unit(used_bytes, unit)
            total_converted = SystemStats.bytes_to_unit(total_bytes, unit)

            %{
              used_storage: total_converted,
              total_storage: used_converted,
              used_unit: unit,
              total_unit: unit,
              storage_usage_percentage: Float.round(used_bytes / total_bytes * 100, 1)
            }

          _ ->
            IO.inspect(values, label: "Unexpected storage values")
            default_storage_stats()
        end

      error ->
        IO.inspect(error, label: "Storage command error")
        default_storage_stats()
    end
  end

  defp default_storage_stats do
    %{
      used_storage: 0,
      total_storage: 0,
      used_unit: "B",
      total_unit: "B",
      storage_usage_percentage: 0
    }
  end

  defp format_bytes(bytes) do
    cond do
      # TB
      bytes > 1024 * 1024 * 1024 * 1024 ->
        {Float.round(bytes / (1024 * 1024 * 1024 * 1024), 2), "TB"}

      # GB
      bytes > 1024 * 1024 * 1024 ->
        {Float.round(bytes / (1024 * 1024 * 1024), 2), "GB"}

      # MB
      bytes > 1024 * 1024 ->
        {Float.round(bytes / (1024 * 1024), 2), "MB"}

      # KB
      bytes > 1024 ->
        {Float.round(bytes / 1024, 2), "KB"}

      true ->
        {bytes, "B"}
    end
  end

  # Windows OS info using PowerShell command
  def os_info do
    command = "powershell"
    args = ["(Get-WmiObject -Class Win32_OperatingSystem).Caption"]

    case System.cmd(command, args) do
      {result, 0} -> String.trim(result)
      _ -> "Unsupported OS"
    end
  end
end
