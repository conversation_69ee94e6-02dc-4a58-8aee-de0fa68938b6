defmodule App.SummaryDataFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `App.SummaryData` context.
  """

  @doc """
  Generate a summarised_data.
  """
  def summarised_data_fixture(attrs \\ %{}) do
    {:ok, summarised_data} =
      attrs
      |> Enum.into(%{
        license_field_id: 42,
        license_id: 42,
        status: 42
      })
      |> App.SummaryData.create_summarised_data()

    summarised_data
  end
end
