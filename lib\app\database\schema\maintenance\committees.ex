defmodule App.Maintenance.Committees do
  use Ecto.Schema
  import Ecto.Changeset

  schema "committees" do
    field :name, :string
    field :description, :string
    field :status, :integer, default: 1

    timestamps()
  end

  @doc false
  def changeset(committees, attrs) do
    committees
    |> cast(attrs, [:name, :description, :status])
    |> validate_required([:name, :description])
    |> unique_constraint(:name)
    |> unsafe_validate_unique([:name], App.Repo)
  end
end
