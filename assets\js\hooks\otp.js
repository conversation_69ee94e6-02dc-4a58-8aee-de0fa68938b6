export const OTPInput = {
    mounted() {
        this.inputs = document.querySelectorAll('#otp > *[id]');
        this.handlers = [];

        this.inputs.forEach((input, index) => {
            const handler = (event) => this.handleInput(event, index);
            input.addEventListener('keydown', handler);
            this.handlers.push({ element: input, handler });
        });
    },

    destroyed() {
        // Clean up event listeners
        this.handlers.forEach(({ element, handler }) => {
            element.removeEventListener('keydown', handler);
        });
        this.handlers = [];
        this.inputs = null;
    },

    handleInput(event, currentIndex) {
        const isBackspace = event.key === 'Backspace';
        const isNumber = /^[0-9]$/.test(event.key);
        const isLetter = /^[A-Za-z]$/.test(event.key);

        if (isBackspace) {
            event.preventDefault();
            this.inputs[currentIndex].value = '';
            if (currentIndex > 0) {
                this.inputs[currentIndex - 1].focus();
            }
            return;
        }

        if (!isNumber && !isLetter) {
            event.preventDefault();
            return;
        }

        if (currentIndex === this.inputs.length - 1 && this.inputs[currentIndex].value) {
            return;
        }

        this.inputs[currentIndex].value = event.key.toUpperCase();
        if (currentIndex < this.inputs.length - 1) {
            this.inputs[currentIndex + 1].focus();
        }
        event.preventDefault();
    }
};

