defmodule App.Repo.Migrations.CreateTblErrorMaster do
  use Ecto.Migration

  def change do
    create table(:tbl_error_master) do
      add(:code, :string)
      add(:error_desc, :string)
      add(:maker_id, references(:tbl_users, on_delete: :nothing))
      add(:checker_id, references(:tbl_users, on_delete: :nothing))

      timestamps()
    end

    create(index(:tbl_error_master, [:maker_id]))
    create(index(:tbl_error_master, [:checker_id]))
    create(unique_index(:tbl_error_master, [:code]))
  end
end
