<div class="px-4 sm:px-6 lg:px-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">Message Drafts</h1>
      <p class="mt-2 text-sm text-gray-700">Configurations</p>
    </div>
    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
      <.header>
        <:actions>
          <.button phx-click="filter" class="btn btn-primary">Filter</.button>
        </:actions>
      </.header>
    </div>
  </div>

  <div class="flex flex-row justify-between bg-white">
    <Table.sidebar
      live_action={@live_action}
      table_data={@data}
      selected_item={@selected_item}
      params={@params}
    />
    <div class="w-full px-5 flex flex-col justify-between h-[70vh]">
      <Table.sidebar_show
        :if={@live_action == :show}
        selected_info={@selected_info}
        edit_form={@edit_form}
        selected_item={@selected_item}
        selected_item_name={@selected_item_name}
        name="message_drafts"
        changeset={@changeset}
      />
      <Table.empty_sidebar_data :if={@live_action == :index} />
      <Table.done_sidebar_data :if={@live_action == :done} message={assigns[:info_message]} />
    </div>
  </div>
  <.live_component
    module={PaginationComponent}
    id="PaginationComponent1"
    params={@params}
    pagination_data={@data}
  />
</div>

<Model.confirmation_model
  :if={@live_action == :confirm}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
