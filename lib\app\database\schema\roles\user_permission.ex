defmodule App.Roles.UserPermission do
  use AppWeb, :schema
  alias App.Accounts.User
  alias App.Roles.Permission

  schema "user_permissions" do
    belongs_to :user, User
    belongs_to :permission, Permission

    belongs_to :granted_by_user, User, foreign_key: :granted_by
    field :reason, :string
    field :expires_at, :naive_datetime

    timestamps()
  end

  def changeset(user_permission, attrs) do
    user_permission
    |> cast(attrs, [:user_id, :permission_id, :granted_by, :reason, :expires_at])
    |> validate_required([:user_id, :permission_id])
    |> unique_constraint([:user_id, :permission_id])
    |> foreign_key_constraint(:user_id)
    |> foreign_key_constraint(:permission_id)
  end
end
