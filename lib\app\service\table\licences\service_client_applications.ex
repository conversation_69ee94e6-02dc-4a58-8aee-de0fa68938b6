defmodule App.Service.Table.ClientLicenceApplications do
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false

  alias App.Utilities
  alias App.Licenses.LicenseMapping
  alias App.Repo

  @pagination [page_size: 10]

  def index(assigns, params) do
    compose_query(params, assigns)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def export(assigns, params) do
    compose_query(params, assigns)
    |> Repo.all()
  end

  def compose_query(params, _assigns) do
    LicenseMapping
    |> join(:left, [a], b in assoc(a, :license))
    |> where([a, b], a.user_id == ^params["user_id"])
    |> check_status(params["status"])
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  defp check_status(query, status) do
    approval_levels = Utilities.count_approval_levels()

    case status do
      "draft" ->
        where(query, [a], a.status == 0 and a.approval_status == false)

      "all" ->
        where(
          query,
          [a],
          a.approval_status == false or a.approval_status == true or a.approved == false or
            a.approved == true
        )

      "submitted" ->
        where(query, [a], a.approval_status == false and a.status >= 1 and a.approved == false)

      "under_consideration" ->
        where(query, [a], a.approval_status == true and a.status >= 1 and a.approved == false)

      "resubmitted" ->
        where(query, [a], a.approval_status == true and a.status >= 1 and a.approved == false)

      "returned" ->
        where(query, [a], a.status == -1 and a.approved == false)

      "rejected" ->
        where(query, [a], a.status == -1 and a.approved == false)

      "approved" ->
        where(
          query,
          [a],
          a.approval_status == true and
            a.status >= ^approval_levels
        )
    end
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"record_name", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.record_name, ^sanitize_term(value))
        )

      {"status", value}, query when byte_size(value) > 0 ->
        where(query, [a], a.status == type(^value, :integer))

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a, b], %{
      id: a.id,
      license_id: a.license_id,
      licence_name: b.name,
      record_name: a.record_name,
      condition_tracking: a.condition_tracking,
      certificate_id: b.certificate_id,
      form_number: b.form_number,
      status: a.status,
      approved: a.approved,
      current_step: a.current_step,
      data: a.data,
      approval_status: a.approval_status,
      inserted_at: a.inserted_at,
      updated_at: a.updated_at
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b],
      fragment("lower(?) LIKE lower(?)", a.record_name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", b.name, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
