defmodule AppWeb.Dashboard.TotalDealers do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})
  on_mount({AppWeb.UserAuth, :ensure_authenticated})

  alias App.Licenses

  def render(assigns) do
    ~H"""
    <.stats_card
      title="Submitted to Supervisor"
      icon="hero-credit-card"
      stats_value="0"
      stats_desc="Total Number of Applications Submitted to Supervisor"
    />
    """
  end

  def mount(_params, _session, socket) do
    {:ok, assign(socket, :total, Licenses.count_applications_by_status("supervisor"))}
  end
end
