defmodule App.Service.Functions.SMS.MulticastUpload do
  use AppWeb, :file_function

  alias Notification.MessagesDrafts

  alias Notification.Notification.SmsLogs
  alias App.Database.CustomDB

  def execute(socket, path, params) do
    Extract.validate_excel_and_csv(path)
    |> case do
      {:ok, col_count, file} ->
        cond do
          col_count == 0 ->
            {:error, :start, "No records found in the uploaded file."}

          col_count < 2 ->
            {:error, :start, "System expecting 2 columns but only got #{col_count}."}

          col_count > 2 ->
            {:error, :start, "System expecting 2 columns but only #{col_count}."}

          true ->
            file
            |> case do
              {:ok, txn} ->
                extraction_process(socket, path, txn, params)

              {:error, message} ->
                {:error, :start, message}
            end
        end

      error ->
        error
    end
  end

  defp extraction_process(socket, path, txn, _params) do
    total_messages = handle_msg_count(txn)

    txn
    |> Extract.add_index()
    |> Enum.map(fn x ->
      cond do
        String.trim(x["col1"]) == "" ->
          message(0, "Mobile Number cannot be blank", x)

        !validate_mobile_number(to_string(x["col1"])) ->
          message(0, "Invalid Phone Number", x)

        String.trim(x["col2"]) == "" ->
          message(0, "Message cannot be blank", x)

        String.length(x["col2"]) > 960 ->
          message(0, "Message cannot exceed 960 characters", x)

        true ->
          case MessagesDrafts.confirm_client_type(socket.assigns.client, total_messages) do
            false ->
              message(0, "INSUFFICIENT_BUNDLE_LIMIT", x)

            true ->
              message(1, "Valid record", x)
          end
      end
    end)
    |> Extract.finalize_process(
      path,
      # successful message, encase the process is a success
      "All Records in the file are valid, proceed to update them.",
      # failed message, encase the process failed
      "One or more transactions in the file are invalid, kindly update them before you can proceed."
    )
  end

  def process_file(socket, client, attrs, params) do
    entries = Jason.decode!(attrs["entries"])

    MessagesDrafts.bulk_multicast_messages(socket, client, entries, params)
    |> case do
      {:ok, txn} ->
        {:ok, "Successfully added #{Enum.count(entries)} messages", txn}

      {:error, message} ->
        {:error, message}
    end
  end

  def handle_msg_count(messages) do
    messages
    |> Task.async_stream(fn text -> SmsLogs.msg_count(text["col2"]) end, max_concurrency: 500)
    |> Enum.reduce(0, fn {:ok, num}, acc -> num + acc end)
  end

  def message(status, message, params) do
    %{
      status: status,
      col1: params["col1"],
      col2: params["col2"],
      key: params["key"] + 1,
      message: message
    }
  end

  defp validate_mobile_number(mobile) do
    CustomDB.validate_mobile_call(mobile)
    |> case do
      {:error, _} -> false
      {:ok, _} -> true
    end
  end
end
