defmodule AppWeb.Auth.FinanceLive.Index do
  @moduledoc false
  use AppWeb, :live_view
  alias AppWeb.Auth.ViewApplicationsLive.Index, as: Main
  alias App.{CustomContext, Accounts}
  alias App.Service.LicenceApplications.Functions

  @impl true
  def mount(params, session, %{assigns: assigns} = socket) do
    if Audit.page_access("finance-view", assigns.permissions) do
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Licence Applications Details", "Accessed Licence Applications Details Page", "/"},
          {true, assigns.current_user.id}
        )
      end)

      socket =
        socket
        |> assign(data_loader: true)
        |> assign(params: params)
        |> assign(confirmation_model: false)
        |> assign(confirmation_model_title: "Are you sure?")
        |> assign(confirmation_model_text: "")
        |> assign(confirmation_model_agree: "")
        |> assign(confirmation_model_reject: "close_confirmation_model")
        |> assign(confirmation_model_params: "")
        |> assign(confirmation_model_icon: "exclamation_circle")
        |> assign(session_id: session["live_socket_id"])

      {:ok, socket}
    else
      Task.start(fn ->
        Audit.create_access(
          session,
          {"Licence Applications Details", "Accessed Licence Applications Details Page", "/"},
          {false, assigns.current_user.id}
        )
      end)

      {:ok,
       push_navigate(
         LiveFunctions.sweet_alert(socket, "You are not allowed to access this page", "error"),
         to: ~p"/dashboard"
       )}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_data, params})

    {:noreply,
     socket
     |> assign(:params, params)
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _) do
    socket
    |> assign(:page_title, "Licence Details")
  end

  @impl true
  def handle_info({App.Jobs.JobCheckBalance, :account_verified, _data}, socket) do
    Process.send_after(self(), :navigate_to_main, 3000)

    socket
    |> assign(loader: false)
    |> Main.success_message("Application Successfully Validated.")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      {:get_data, _params} ->
        Main.get_update(socket)
        |> case do
          {:noreply, socket_data} ->
            {:noreply,
             assign(socket_data,
               account_total: Accounts.get_account_total(socket_data.assigns.record.id)
             )}
        end

      {:confirm_amount, params} ->
        confirm_amount(socket, params)

      {AppWeb.LiveFunctions, message} ->
        send(self(), {:get_data, %{}})

        {
          :noreply,
          socket
          |> put_flash(:info, message)
          |> assign(:live_action, :index)
          |> assign(:page_title, "")
        }

        {:noreply, socket}

      :navigate_to_main ->
        redirect(socket, to: ~p"/license/applications/New")
        |> noreply()
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, %{assigns: _assigns} = socket) do
    case target do
      "view_licence" ->
        {:noreply,
         assign(socket, :live_action, :view_licence_form)
         |> assign(:page_title, "Licence Form")
         |> assign(:licence_form, ~p"/license/template/#{value["id"]}")}

      "close_model" ->
        {:noreply, assign(socket, :live_action, :index)}

      "view_associate" ->
        socket =
          socket
          |> assign(:page_title, "View Associate")
          |> assign(:live_action, :view_associate)
          |> assign(:associate, ~p"/finance/association/#{value["id"]}")

        {:noreply, socket}

      "view_document" ->
        socket =
          socket
          |> assign(:page_title, "View Document")
          |> assign(:live_action, :view_document)
          |> assign(:doc_name, value["doc_name"])
          |> assign(
            :document,
            String.replace(value["path"], Path.join(:code.priv_dir(:app), "static/"), "")
          )

        {:noreply, socket}

      "check_amount" ->
        {
          :noreply,
          assign(socket, :live_action, :confirmation_amount)
          |> assign(
            :confirmation_model_text,
            "Please Enter the Amount in Proof of Payment (POP) below: "
          )
          |> assign(
            :confirmation_model_params,
            Map.merge(value, %{
              "action" => "check_amount"
            })
          )
          |> assign(:confirmation_model_agree, "confirm_amount")
        }

      "confirm_amount" ->
        send(self(), {:confirm_amount, value})

        socket
        |> assign(confirmation_model_title: "")
        |> assign(:confirmation_model_icon, "loading")
        |> assign(
          :confirmation_model_text,
          "Processing Confirmation."
        )
        |> noreply()

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :live_action, :index)
        }
    end
  end

  defp confirm_amount(socket, params) do
    attrs = Jason.decode!(params["params"])

    Functions.index(
      socket,
      Map.merge(
        attrs,
        %{
          "amount_paid" => params["amount_paid"]
        }
      ),
      socket.assigns.record,
      socket.assigns.associates,
      attrs["action"]
    )
    |> case do
      {:ok, %{"update_account" => account}} ->
        CustomContext.subscribe("VERIFY_AMOUNT:#{account.id}")

        {
          :noreply,
          socket
          |> assign(confirmation_model_title: "")
          |> assign(:confirmation_model_icon, "loading")
          |> assign(
            :confirmation_model_text,
            "Validating Amounts."
          )
        }

      {:error, error} ->
        socket
        |> assign(loader: false)
        |> Main.error_message(error)
    end
  end
end
