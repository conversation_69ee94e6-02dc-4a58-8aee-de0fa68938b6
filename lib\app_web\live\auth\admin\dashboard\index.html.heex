<div class="min-h-screen bg-white p-6">
  <div class="max-w-7xl mx-auto">
    <!-- Dashboard Header -->
    <div class="sm:flex sm:items-center mb-10">
      <div class="sm:flex-auto rounded-2xl p-6 border border-gray-200">
        <h1 class="text-4xl font-bold mb-2 text-gray-900">Admin Dashboard</h1>

        <p class="text-gray-600">Welcome back! Here's what's happening today.</p>
      </div>

      <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none" phx-update="ignore" id="time2">
        <div class="px-6 py-3 rounded-xl shadow-lg border border-gray-200">
          <span phx-hook="LocalTime" id="time" class="text-lg text-gray-900 font-semibold"></span>
        </div>
      </div>
    </div>
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="bg-white shadow-sm rounded-2xl p-6 transform hover:scale-105 transition-all duration-300 border border-gray-200">
        <div class="flex items-center">
          <div class="bg-gradient-to-br from-blue-500 to-indigo-600 p-3 rounded-xl">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-semibold text-gray-900">Total License Applications</h3>
            <p class="text-2xl font-bold text-gray-700">0</p>
            <p class="text-sm text-gray-500">All license applications</p>
          </div>
        </div>
      </div>
      <%= if "applications-new_license-view" in @permissions do %>
        <%= live_render(@socket, AppWeb.Dashboard.Admin.NewApplications,
          id: "applications-new_license-view"
        ) %>
      <% end %>

      <%= if "applications-returned_applicant-view" in @permissions do %>
        <%= live_render(@socket, AppWeb.Dashboard.Admin.ReturnedToApplicant,
          id: "applications-returned_applicant-view"
        ) %>
      <% end %>

      <%= if "applications-submitted_to_supervisor-view" in @permissions do %>
        <%= live_render(@socket, AppWeb.Dashboard.Admin.SubmittedSupervisor,
          id: "applications-submitted_to_supervisor-view"
        ) %>
      <% end %>

      <%= if "applications-submit_market_operations-view" in @permissions do %>
        <%= live_render(@socket, AppWeb.Dashboard.Admin.SubmittedMarketOperations,
          id: "applications-submit_market_operations-view"
        ) %>
      <% end %>
    </div>
    <!-- Recent Licenses Table -->
    <div class="bg-white/95 backdrop-blur-sm shadow-2xl rounded-2xl mb-8 border border-white/50 overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-xl font-bold text-gray-900">Recent Licenses</h2>

        <p class="text-gray-600 text-sm">Last 10 applications submitted</p>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full">
          <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
            <tr>
              <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                License Name
              </th>

              <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                User
              </th>

              <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Status
              </th>

              <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Date
              </th>
            </tr>
          </thead>

          <tbody class="bg-white divide-y divide-gray-200">
            <%= for license <- @licenses do %>
              <tr class="hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-200">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  <%= license.record_name %>
                </td>

                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  <%= license.fname %>
                </td>

                <td class="px-6 py-4 whitespace-nowrap">
                  <%= if license.approved == false do %>
                    <span class="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs px-3 py-1 rounded-full font-semibold shadow-lg">
                      Pending
                    </span>
                  <% else %>
                    <span class="bg-gradient-to-r from-green-400 to-emerald-500 text-white text-xs px-3 py-1 rounded-full font-semibold shadow-lg">
                      Approved
                    </span>
                  <% end %>
                </td>

                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                  <%= Calendar.strftime(license.inserted_at, "%d %B %Y") %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
