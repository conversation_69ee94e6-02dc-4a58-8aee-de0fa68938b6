defmodule Logs.Audit.System do
  use AppWeb, :schema

  schema "logs_system" do
    field :action, :string
    field :attrs, :string
    field :browser_details, :string
    field :device_type, :string
    field :device_uuid, :string
    field :full_browser_name, :string
    field :ip_address, :string
    field :known_browser, :boolean, default: false
    field :narration, :string
    field :service, :string
    field :session_id, :string
    field :system_platform_name, :string
    field :user_id, :integer

    timestamps()
  end

  @doc false
  def changeset(system, attrs) do
    system
    |> cast(attrs, [
      :narration,
      :action,
      :attrs,
      :service,
      :device_uuid,
      :session_id,
      :ip_address,
      :full_browser_name,
      :browser_details,
      :system_platform_name,
      :device_type,
      :known_browser,
      :user_id
    ])
    |> trim_data(:attrs, 6_000)
  end

  defp trim_data(changeset, target, size) do
    response = get_change(changeset, target)

    if response do
      changeset
      |> delete_change(target)
      |> put_change(target, String.slice(response, 0, size))
    else
      changeset
    end
  end
end
