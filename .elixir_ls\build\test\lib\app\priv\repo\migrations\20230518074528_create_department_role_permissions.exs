defmodule App.Repo.Migrations.CreateDepartmentRolePermissions do
  use Ecto.Migration

  def change do
    create table(:department_role_permissions) do
      add :department_role_id, references(:department_roles, on_delete: :delete_all), null: false
      add :permission_id, references(:permissions, on_delete: :delete_all), null: false

      timestamps()
    end

    create unique_index(:department_role_permissions, [:department_role_id, :permission_id])
    create index(:department_role_permissions, [:department_role_id])
    create index(:department_role_permissions, [:permission_id])
  end
end
