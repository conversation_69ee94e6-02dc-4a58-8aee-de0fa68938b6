defmodule App.SetUp.Service do
  @moduledoc false
  alias App.Settings

  def init do
    [
      %{
        "host" => "messaging.airtel.co.zm",
        "port" => "9001",
        "password" => "pr0b4s3",
        "system_id" => "probase",
        "mobile_regex" => "^(26097|26077|26096|26076)",
        "service_name" => "AIRTEL",
        "status" => "D"
      },
      %{
        "host" => "***********",
        "port" => "2775",
        "password" => "prob@123",
        "system_id" => "probase",
        "mobile_regex" => "^(26075|26095)",
        "service_name" => "ZAMTEL",
        "status" => "D"
      },
      %{
        "host" => "cpassmessaging.mtn.zm",
        "port" => "30099",
        "password" => "Zn1J1wDb",
        "system_id" => "yGUcQcgg2cgnt11",
        "mobile_regex" => "^(26075)",
        "service_name" => "MTN",
        "status" => "D",
        "sender_ids" => [
          "TONTOZO",
          "UNITURTLE",
          "PANAROTTIS",
          "ONEZLOTTO",
          "MULUNGUSHI",
          "LOLCFINANCE",
          "UNKAGO",
          "SBZSMARTPAY",
          "NRFA",
          "NWSC",
          "NHIMA",
          "FABANK",
          "BAYPORT",
          "ABSA",
          "ATLASMARA",
          "ZICB",
          "ITNOTPZM",
          "ITNOTP",
          "FNB",
          "ACCESS BANK"
        ]
      }
    ]
    |> Enum.each(fn attrs ->
      if struct = Settings.get_service_by_service_name(attrs["service_name"]) do
        Settings.update_service(
          struct,
          %{
            "host" => attrs["host"],
            "port" => attrs["port"],
            "password" => attrs["password"],
            "system_id" => attrs["system_id"],
            "mobile_regex" => attrs["mobile_regex"],
            "service_name" => attrs["service_name"],
            "status" => attrs["status"],
            "sender_ids" => attrs["sender_ids"]
          }
        )
      else
        Settings.create_service(%{
          "host" => attrs["host"],
          "port" => attrs["port"],
          "password" => attrs["password"],
          "system_id" => attrs["system_id"],
          "mobile_regex" => attrs["mobile_regex"],
          "service_name" => attrs["service_name"],
          "status" => attrs["status"],
          "sender_ids" => attrs["sender_ids"]
        })
      end
    end)

    :ok
  end
end
