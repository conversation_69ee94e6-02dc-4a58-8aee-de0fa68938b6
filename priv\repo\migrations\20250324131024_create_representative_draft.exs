defmodule App.Repo.Migrations.CreateRepresentativeDraft do
  use Ecto.Migration

  def change do
    create table(:summary_drafts) do
      add :template, :text
      add :service, :string
      add :status, :boolean
      add :name, :string
      add :license_id, :integer
      # add :license_id, references(:licenses, on_delete: :nothing)
      add :template_data, :text

      timestamps(type: :utc_datetime)
    end
  end
end
