defmodule AppWeb.Auth.Navigation do
  @moduledoc false
  use AppWeb, :live_view

  on_mount({AppWeb.UserAuth, :mount_current_user})
  on_mount({AppWeb.UserAuth, :ensure_authenticated})

  @impl true
  def render(assigns) do
    ~H"""
    <div
      phx-hook="ActiveNav"
      id={"activeNav#{:os.system_time}"}
      style="color: white; background-color: #4464AD;"
    >
      <.menu_item
        icon="hero-home"
        text="Dashboard"
        link={~p"/dashboard"}
        active={"/dashboard" == @current_url}
      />
      <.menu_item
        :if={@current_user.user_type == "CLIENT"}
        icon="hero-document-plus"
        text="Apply"
        link={~p"/apply"}
        active={"/apply" == @current_url}
      />
      <.dropdown_menu_item
        :if={@current_user.user_type == "CLIENT"}
        text="My Applications"
        icon="hero-clipboard-document-list"
        items={[
          %{
            :link => ~p"/client/applications/#{"all"}",
            :permission => "client_applications-view" in @permissions,
            :text => "All",
            :active => "/client/applications/#{"all"}" == @current_url
          },
          %{
            :link => ~p"/client/applications/#{"draft"}",
            :permission => "client_applications-view" in @permissions,
            :text => "Draft",
            :active => "/client/applications/#{"draft"}" == @current_url
          },
          %{
            :link => ~p"/client/applications/#{"submitted"}",
            :permission => "client_applications-view" in @permissions,
            :text => "Submitted",
            :active => "/client/applications/#{"submitted"}" == @current_url
          },
          %{
            :link => ~p"/client/applications/#{"resubmitted"}",
            :permission => "client_applications-view" in @permissions,
            :text => "Resubmitted",
            :active => "/client/applications/#{"resubmitted"}" == @current_url
          },
          %{
            :link => ~p"/client/applications/#{"returned"}",
            :permission => "client_applications-view" in @permissions,
            :text => "Returned",
            :active => "/client/applications/#{"returned"}" == @current_url
          },
          %{
            :link => ~p"/client/applications/#{"under_consideration"}",
            :permission => "client_applications-view" in @permissions,
            :text => "Under Consideration",
            :active => "/client/applications/#{"under_consideration"}" == @current_url
          },
          %{
            :link => ~p"/client/applications/#{"approved"}",
            :permission => "client_applications-view" in @permissions,
            :text => "Outcome",
            :active => "/client/applications/#{"approved"}" == @current_url
          }
        ]}
      />
      <.dropdown_menu_item
        text="Users"
        icon="hero-user-group"
        items={[
          %{
            :link => ~p"/users/system_admin",
            :permission => "user_view" in @permissions,
            :text => "System Admin",
            :active => "/users/system_admin" == @current_url
          },
          %{
            :link => ~p"/client/maintenance",
            :permission => "user_view" in @permissions,
            :text => "Client",
            :active => "/client/maintenance" == @current_url
          }
        ]}
      />
      <.dropdown_menu_item
        text="Applications"
        icon="hero-user-plus"
        items={[
          %{
            :link => ~p"/license/applications/#{"New"}",
            :permission => "applications-new_license-view" in @permissions,
            :text => "New License Applications",
            :active => "/license/applications/#{"New"}" == @current_url
          },
          %{
            :link => ~p"/license/applications/#{"Market_Operations"}",
            :permission => "applications-submit_market_operations-view" in @permissions,
            :text => "Submitted to Market Operations",
            :active => "/license/applications/#{"Market_Operations"}" == @current_url
          },
          %{
            :link => ~p"/license/applications/#{"Returned_to_Applicant"}",
            :permission => "applications-returned_applicant-view" in @permissions,
            :text => "Returned to Applicant",
            :active => "/license/applications/#{"Returned_to_Applicant"}" == @current_url
          },
          %{
            :link => ~p"/license/applications/#{"Submitted_to_Supervisor"}",
            :permission => "applications-submitted_to_supervisor-view" in @permissions,
            :text => "Submitted to Supervisor",
            :active => "/license/applications/#{"Submitted_to_Supervisor"}" == @current_url
          },
          %{
            :link => ~p"/license/applications/#{"Subordinate"}",
            :permission => "applications-submitted_to_subordinate-view" in @permissions,
            :text => "Submitted to Subordinate",
            :active => "/license/applications/#{"Subordinate"}" == @current_url
          },
          %{
            :link => ~p"/license/applications/#{"LC"}",
            :permission => "applications-submitted_to_lc-view" in @permissions,
            :text => "Submitted to LC",
            :active => "/license/applications/#{"LC"}" == @current_url
          },
          %{
            :link => ~p"/license/applications/#{"Board"}",
            :permission => "applications-submitted_to_board-view" in @permissions,
            :text => "Submitted to Board",
            :active => "/license/applications/#{"Board"}" == @current_url
          },
          %{
            :link => ~p"/license/applications/#{"Returned_from_Supervisor"}",
            :permission => "applications-returned_from_supervisor-view" in @permissions,
            :text => "Returned from Supervisor ",
            :active => "/license/applications/#{"Returned_from_Supervisor"}" == @current_url
          },
          %{
            :link => ~p"/license/applications/conditional",
            :permission => "conditional_application-view" in @permissions,
            :text => "Conditional",
            :active => "/license/applications/conditional" == @current_url
          },
          %{
            :link => ~p"/license/applications/#{"Processed"}",
            :permission => "license_issued-view" in @permissions,
            :text => "Processed",
            :active => "/license/applications/#{"Processed"}" == @current_url
          }
        ]}
      />
      <.menu_item
        :if={@current_user.user_type == "CLIENT"}
        icon="hero-clipboard-document-check"
        text="Notice of Changes"
        permission={"settings-view" in @permissions}
        link={~p"/license/updates"}
        active={"/license/updates" == @current_url}
      />
      <.dropdown_menu_item
        :if={@current_user.user_type == "CLIENT" && @current_user.registration_type == "BUSINESS"}
        text="Representatives"
        icon="hero-user-group"
        items={[
          %{
            :link => ~p"/client/representatives/management",
            :permission => "settings-view" in @permissions,
            :text => "List",
            :active => "/client/representatives/management" == @current_url
          },
          %{
            :link => ~p"/client/representatives/pending",
            :permission => "settings-view" in @permissions,
            :text => "Pending Attachments",
            :active => "/client/representatives/pending" == @current_url
          }
        ]}
      />
      <.dropdown_menu_item
        text="Maintenance"
        icon="hero-square-2-stack"
        items={[
          %{
            :link => ~p"/maintenance/committee",
            :permission => "committees-view" in @permissions,
            :text => "Committees",
            :active => "/maintenance/committee" == @current_url
          },
          %{
            :link => ~p"/license/categories",
            :permission => "license_categories-view" in @permissions,
            :text => "Application Categories",
            :active => "/license/categories" == @current_url
          },
          %{
            :link => ~p"/license/creation",
            :permission => "licenses-view" in @permissions,
            :text => "Licenses",
            :active => "/license/creation" == @current_url
          },

          %{
            :link => ~p"/steps/step/mapping",
            :permission => "steps_mapping-view" in @permissions,
            :text => "Application Steps",
            :active => "/steps/step/mapping" == @current_url
          },
          %{
            :link => ~p"/steps/registration/page",
            :permission => "page-view" in @permissions,
            :text => "registration page",
            :active => "/steps/registration/page  " == @current_url
          },

          %{
            :link => ~p"/maintenance/conditions",
            :permission => "conditions-view" in @permissions,
            :text => "Conditions",
            :active => "/maintenance/conditions" == @current_url
          }
        ]}
      />

      <.dropdown_menu_item
        text="Reports"
        icon="hero-check-badge"
        items={[
          %{
            :link => ~p"/report/issued_licenses",
            :permission => "approval_levels-view" in @permissions,
            :text => "issued Licenses",
            :active => "/report/issued_licenses" == @current_url
          }
        ]}
      />

      <.dropdown_menu_item
        text="Approval Levels"
        icon="hero-check-badge"
        items={[
          %{
            :link => ~p"/maintenance/approval_levels",
            :permission => "approval_levels-view" in @permissions,
            :text => "Application Levels",
            :active => "/maintenance/approval_levels" == @current_url
          },
          %{
            :link => ~p"/maintenance/conditional_levels",
            :permission => "approval_levels-view" in @permissions,
            :text => "Conditional Levels",
            :active => "/maintenance/conditional_levels" == @current_url
          }
        ]}
      />

      <.dropdown_menu_item
        text="Configurations"
        icon="hero-cog-8-tooth"
        items={[
          %{
            :link => ~p"/maintenance/api",
            :permission => "api_maintenance_view" in @permissions,
            :text => "API Management",
            :active => "/maintenance/api" == @current_url
          },
          %{
            :link => ~p"/maintenance/licence_drafts",
            :permission => "api_maintenance_view" in @permissions,
            :text => "Application Forms",
            :active => "/maintenance/licence_drafts" == @current_url
          },
          %{
            :link => ~p"/message/drafts",
            :permission => "message_draft_view" in @permissions,
            :text => "Message Drafts",
            :active => "/message/drafts" == @current_url
          },
          %{
            :link => ~p"/maintenance/certificate_drafts",
            :permission => "api_maintenance_view" in @permissions,
            :text => "Certificate Drafts",
            :active => "/maintenance/certificate_drafts" == @current_url
          },
          %{
            :link => ~p"/maintenance/summary/drafts",
            :permission => "api_maintenance_view" in @permissions,
            :text => "Summary Drafts",
            :active => "/maintenance/summary/drafts" == @current_url
          },
          %{
            :link => ~p"/settings/error_codes",
            :permission => "error-view" in @permissions,
            :text => "Error Codes",
            :active => "/settings/error_codes" == @current_url
          }
        ]}
      />
      <.dropdown_menu_item
        text="Roles & Permissions"
        icon="hero-shield-check"
        items={[
          %{
            :link => ~p"/roles/system_roles",
            :permission => "department_roles-view" in @permissions,
            :text => "Departmental",
            :active => "/roles/system_roles" == @current_url
          },
          %{
            :link => ~p"/roles/access_roles/#{@role_department}",
            :permission => "access_roles-view" in @permissions,
            :text => "Access",
            :active => "/roles/access_roles/#{@role_department}" == @current_url
          }
        ]}
      />
      <.dropdown_menu_item
        text="Logs"
        icon="hero-queue-list"
        items={[
          %{
            :link => ~p"/logs/device",
            :permission => "device_view" in @permissions,
            :text => "Devices",
            :active => "/logs/device" == @current_url
          },
          %{
            :link => ~p"/logs/api",
            :permission => "api_logs-view" in @permissions,
            :text => "Api Logs",
            :active => "/logs/api" == @current_url
          },
          %{
            :link => ~p"/logs/system",
            :permission => "audit_logs-view" in @permissions,
            :text => "System Logs",
            :active => "/logs/system" == @current_url
          },
          %{
            :link => ~p"/logs/session",
            :permission => "session_logs-view" in @permissions,
            :text => "Session Logs",
            :active => "/logs/session" == @current_url
          }
        ]}
      />
    </div>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    socket
    |> assign(:current_url, "/")
    |> ok()
  end

  @impl true
  def handle_event("change_nav", params, socket) do
    assign(socket, current_url: params["url"])
    |> noreply()
  end

  @doc """
  <%= for nav <- @navigation do %>
        <%= if nav[:content_list] do %>
          <%= if nav[:permissions] == "*" or check_member?(@permissions, nav[:permissions]) do %>
            <LeftSideNav.dropdown title={nav[:title]} icon={:icon}>
              <%= for child1 <- nav[:content_list] do %>
                <%= if check_member?(@permissions, child1[:permissions]) do %>
                  <LeftSideNav.dropdown_item title={child1[:title]} navigate={child1[:navigate]} />
                <% end %>
              <% end %>
            </LeftSideNav.dropdown>
          <% end %>
        <% else %>
          <%= if nav[:permissions] == "*" or check_member?(@permissions, nav[:permissions]) do %>
            <LeftSideNav.item
              title={nav[:title]}
              icon={nav[:icon]}
              navigate={nav[:navigate]}
              active={nav[:navigate] == @current_url}
            />
          <% end %>
        <% end %>
      <% end %>
  """
end
