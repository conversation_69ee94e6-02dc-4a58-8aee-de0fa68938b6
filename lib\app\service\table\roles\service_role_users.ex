defmodule App.Service.Table.RoleUsers do
  @moduledoc false
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false
  alias App.{Accounts.User, Repo}

  @pagination [page_size: 10]

  def index(params) do
    compose_query(params)
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
  end

  def export(params) do
    compose_query(params["filter"])
    |> Repo.all()
  end

  defp compose_query(params) do
    User
    |> where([a], a.role_id == ^params["role_id"])
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a], %{
      email: a.email,
      id: a.id,
      first_name: a.first_name,
      inserted_at: a.inserted_at,
      last_name: a.last_name,
      status: a.status,
      last_login_date: fragment("TO_CHAR(?, 'DD MON YYYY HH24:MI:SS')", a.last_logon)
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      fragment("lower(?) LIKE lower(?)", a.email, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.last_name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.first_name, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
