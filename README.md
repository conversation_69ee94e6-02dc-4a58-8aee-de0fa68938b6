# Probase SMS Gateway Client

A robust SMS gateway client application built with Phoenix Framework that enables sending, receiving, and managing SMS messages at scale.

## Features

- SMS Message Management
  * Send single and bulk SMS messages
  * Message queuing and scheduling
  * Delivery reports tracking
  * Message templates
  * Contact management

- System Integration
  * RESTful API endpoints
  * Multiple carrier support
  * Webhooks for real-time updates

## Prerequisites

- Elixir 1.14 or later
- Phoenix Framework 1.7+
- PostgreSQL 12+
- Node.js 16+ (for asset compilation)

## Installation

1. Clone the repository
2. Install dependencies:
```bash
mix deps.get
mix setup
npm install --prefix assets
```

3. Configure your database in `config/dev.exs`
4. Setup the database:
```bash
mix ecto.setup
```

## Running the Application

Development environment:
```bash
mix phx.server
```

Visit [`localhost:5000`](http://localhost:5000) from your browser.

## Configuration

1. SMS Provider Settings:
   * Configure your SMS provider credentials in `config/config.exs`
   * Supported providers can be configured in `config/providers.exs`

2. Database Settings:
   * Update database configurations in the respective environment files
   * (`config/dev.exs`, `config/prod.exs`)

## API Documentation

API endpoints are available at `/api/docs` when running in development mode.

Common endpoints:
- POST `/api/messages/send` - Send single SMS
- POST `/api/messages/bulk` - Send bulk SMS
- GET `/api/messages` - List messages
- GET `/api/reports` - View delivery reports

## Production Deployment

1. Build release:
```bash
MIX_ENV=prod mix release
```

2. Configure production settings:
   * Set required environment variables
   * Update `config/prod.exs`
   * Configure your reverse proxy (nginx recommended)

3. Deploy using your preferred method (systemd, docker, etc.)

## Monitoring & Maintenance

- Check application logs: `{install_path}/logs`
- Monitor system metrics through the dashboard
- Regular database backups recommended
- Set up alerts for critical events

## Security Considerations

- Keep your API keys secure
- Use SSL/TLS in production
- Regular security updates
- Access control through role-based authentication

## Support

For technical support and bug reports:
- Submit issues through the project repository
- Contact technical support team
- Check documentation for common issues

## License

Copyright © 2023 Probase
