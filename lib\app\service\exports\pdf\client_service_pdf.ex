defmodule App.Service.Export.ClientServicePdf do
  @moduledoc false

  alias App.Service.Table.Client

  alias App.Service.Export.{
    Functions
  }

  def index(payload) do
    results =
      Client.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "username" => data.username,
          "first_name" => data.first_name,
          "last_name" => data.last_name,
          "email" => data.email,
          "registered_date" =>
            Calendar.strftime(
              NaiveDateTime.add(data.registered_date, 7200, :second),
              "%d %B %Y %H:%M:%S"
            ),
          "date" =>
            Calendar.strftime(
              NaiveDateTime.add(data.date, 7200, :second),
              "%d %B %Y %H:%M:%S"
            ),
          "status" => Functions.table_numeric_status(data.status)
        }
      end)
      |> Enum.map(fn data ->
        """
         <tr>
            <td style="text-align: center;">{{ username }}</td>
            <td style="text-align: center;">{{ first_name }}</td>
            <td style="text-align: center;">{{ last_name }}</td>
            <td style="text-align: center;">{{ email }}</td>
            <td style="text-align: center;">{{ registerd_date }}</td>
            <td style="text-align: center;">{{ date }}</td>
            <td style="text-align: center;">{{ status }}</td>

         </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/client_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "User",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end
