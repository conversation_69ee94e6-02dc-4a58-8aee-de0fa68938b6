defmodule AppWeb.PageLive.FormComponent do
  use AppWeb, :live_component
  alias App.Registration
  alias App.Registration.Pages

  @icons [
    {"Share", "share"},
    {"Shield", "shield"},
    {"Refresh", "refresh"},
    {"circle", "x-circle"},
    {"Office Building", "office-building"},
    {"Location Marker", "location-marker"},
    {"Document Text", "document-text"},
    {"Users", "users"},
    {"Briefcase", "briefcase"},
    {"User", "user"},
    {"Chart Bar", "chart-bar"}
  ]

  # alias App.Service.ServiceLicenseMaintenance.Functions

  # Render form
  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.simple_form
        for={@form}
        id="page-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-2 gap-4">
          <.input
            field={@form[:name]}
            type="text"
            placeholder="Enter a name"
            required
            label={raw(~c"Name <span class='text-rose-500'>*</span>")}
          />

          <.input
            field={@form[:url]}
            type="text"
            label={raw(~c"URL <span class='text-rose-500'>*</span>")}
            placeholder="e.g. /license/registration/fields/"
            required
          />

           <.input
            field={@form[:color]}
            type="color"
            label={raw(~c"Color <span class='text-rose-500'>*</span>")}
            required
          />

          <.input
            field={@form[:icon]}
            type="select"
            prompt="Select"
            options={@icons}
            label={raw(~c"Icon <span class='text-rose-500'>*</span>")}
            required
          />


            <div class="col-span-2">
          </div>
        </div>

        <:actions>
          <div class="align-left">
            <.button type="button" phx-click="close_model">Close</.button>
            <.button type="submit" phx-disable-with="submitting...">Submit</.button>
          </div>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{action: action} = assigns, socket) do
    c_mount(action, assigns, socket)
  end

  defp c_mount(:edit, %{page: page} = assigns, socket) do
    changeset = Registration.change_page(page)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:icons, @icons)
     |> assign_form(changeset)}
  end

  defp c_mount(:new, %{page: page} = assigns, socket) do
    changeset = Registration.change_page(page)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:icons, @icons)
     |> assign_form(changeset)}
  end

  @impl true
  def handle_event("validate", %{"pages" => params}, socket) do
    changeset =
      socket.assigns.page
      |> Registration.change_page(params)
      |> Map.put(:action, :validate)

    socket
    |> assign_form(changeset)
    |> noreply()
  end

  def handle_event("save", %{"pages" => params}, socket) do
    save_page(socket, socket.assigns.action, params)
    |> noreply()
  end

  defp save_page(socket, :edit, params) do
    case Registration.update_page(socket.assigns.page, params) do
      {:ok, page} ->
        notify_parent({:saved, page, "Page Updated Successfully"})
        socket

      {:error, %Ecto.Changeset{} = changeset} ->
        assign_form(socket, changeset)
    end
  end

  defp save_page(socket, :new, params) do
    case Registration.create_page(params) do
      {:ok, page} ->
        notify_parent({:saved, page, "Page Created Successfully"})
        socket

      {:error, %Ecto.Changeset{} = changeset} ->
        assign_form(socket, changeset)
    end
  end

  defp notify_parent(msg) do
    send(self(), {__MODULE__, msg})
  end
end
