defmodule App.Repo.Migrations.CreateUploadedFiles do
  use Ecto.Migration

  def change do
    create table(:uploaded_files) do
      add :filename, :string
      add :path, :string
      add :type, :string
      add :level, :integer, default: 0
      add :user_id, references(:tbl_users, on_delete: :nothing), null: false
      add :application_id, references(:user_license_mapping, on_delete: :nothing)

      timestamps()
    end
  end
end
