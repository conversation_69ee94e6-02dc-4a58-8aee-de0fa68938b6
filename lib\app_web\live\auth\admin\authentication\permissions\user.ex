defmodule AppWeb.Authenticated.Authentication.PermissionsLive.User do
  @moduledoc false
  use AppWeb, :live_view

  alias App.{Accounts, Roles}

  @impl true
  def mount(%{"role" => user_id}, _session, socket) do
    user =
      Accounts.get_user!(user_id)
      |> App.Repo.preload(:role)

    # Get all relevant permissions
    role_permissions =
      if user.role, do: Roles.get_access_role_with_permissions(user.role_id).permissions, else: []

    user_specific_permissions = Roles.get_user_specific_permissions(user_id)
    available_permissions = Roles.get_available_permissions_for_user(user_id, user.role_id)

    # Group permissions by source
    grouped_permissions =
      group_permissions_by_source(
        role_permissions,
        user_specific_permissions,
        available_permissions
      )

    {:ok,
     socket
     |> assign(:user, user)
     |> assign(:search_query, "")
     |> assign(:grouped_permissions, grouped_permissions)
     |> assign(
       :selected_additional,
       MapSet.new(Enum.map(user_specific_permissions, & &1.permission_id))
     )
     |> assign(
       :page_title,
       "Manage Permissions - #{user.first_name} #{user.last_name} (#{user.email})"
     )}
  end

  @impl true
  def handle_event("toggle_permission", %{"permission_id" => permission_id}, socket) do
    permission_id = String.to_integer(permission_id)
    selected = socket.assigns.selected_additional

    new_selected =
      if MapSet.member?(selected, permission_id) do
        MapSet.delete(selected, permission_id)
      else
        MapSet.put(selected, permission_id)
      end

    {:noreply, assign(socket, :selected_additional, new_selected)}
  end

  @impl true
  def handle_event("search", %{"value" => query}, socket) do
    assign(socket, :search_query, query)
    |> noreply()
  end

  @impl true
  def handle_event("save", _params, socket) do
    user = socket.assigns.user
    current_user = socket.assigns.current_user
    permission_ids = MapSet.to_list(socket.assigns.selected_additional)

    case Roles.update_user_permissions(user.id, permission_ids, current_user.id) do
      :ok ->
        {:noreply,
         socket
         |> put_flash(:info, "User permissions updated successfully")
         |> push_navigate(to: ~p"/users/system_admin")}

      _ ->
        {:noreply, put_flash(socket, :error, "Error updating permissions")}
    end
  end

  defp group_permissions_by_source(
         role_permissions,
         user_specific_permissions,
         available_permissions
       ) do
    # Extract just the permissions from user_specific_permissions
    user_specific_perms = Enum.map(user_specific_permissions, & &1.permission)

    %{
      role: group_by_service(role_permissions),
      user_specific: group_by_service(user_specific_perms),
      available: group_by_service(available_permissions)
    }
  end

  defp filter_permissions(permissions, search_query) do
    query = String.downcase(search_query)

    permissions
    |> Enum.map(fn {service, perms} ->
      filtered_perms =
        Enum.filter(perms, fn perm ->
          String.contains?(String.downcase(perm.description), query) ||
            String.contains?(String.downcase(perm.service), query) ||
            String.contains?(String.downcase(perm.tab || ""), query)
        end)

      {service, filtered_perms}
    end)
    |> Enum.reject(fn {_, perms} -> Enum.empty?(perms) end)
  end

  defp group_by_service(permissions) do
    permissions
    |> Enum.group_by(& &1.service)
    |> Enum.sort_by(fn {service, _} -> service end)
  end
end
