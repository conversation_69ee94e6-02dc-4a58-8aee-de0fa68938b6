<%= if !@data_loader do %>
  <div class="bg-gray-50 min-h-screen py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <nav class="mb-6">
        <ol class="flex items-center space-x-2 text-sm text-gray-500">
          <li class="flex items-center">
            <span class="font-medium text-xl text-gray-900">Application Details</span>
          </li>
        </ol>
      </nav>
      <!-- Main content area - side by side layout -->
      <div class="flex flex-col lg:flex-row gap-6">
        <!-- Left column - Application details -->
        <div class="lg:flex-1">
          <div class="bg-white shadow-md rounded-xl overflow-hidden border border-gray-200">
            <div class="bg-gradient-to-r from-brand-10 to-brand-10 px-6 py-4 flex justify-between items-center">
              <div>
                <h1 class="text-xl font-bold text-white">
                  Submitted Details
                </h1>

                <p class="text-indigo-100 text-sm mt-1">
                  Review application information
                </p>
              </div>

              <span class="text-xs px-3 py-1 rounded-full font-medium bg-white text-brand-10">
                <button
                  phx-click="view_licence"
                  phx-value-id={@record.license_id}
                  class="text-brand-10 font-medium hover:text-brand-10"
                >
                  View Licence Form
                </button>
              </span>
            </div>

            <.simple_form for={@form} id="preview-form" class="p-6">
              <div class="bg-white rounded-xl mb-8">
                <div class="flex items-center justify-between mb-6 pb-3 border-b border-gray-100">
                  <h2 class="text-xl font-semibold text-gray-900">
                    Application Details
                  </h2>

                  <span class="text-xs px-3 py-1 rounded-full font-medium">
                    <Table.table_approval_status status={@record.status} name={@current_level} />
                  </span>
                </div>
                <!-- Two-column grid for Application Summary and Documents -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <!-- Left column: Application Summary -->
                  <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Application Summary</h3>

                    <div class="space-y-4">
                      <%= for field <- @license_data do %>
                        <%= if field.field_type != "upload" &&
               field.field_name in ["company_name", "applicant_name"] do %>
                          <div class="group">
                            <div class="text-sm font-medium text-gray-500 mb-2 group-hover:text-brand-10 transition-colors">
                              <%= String.capitalize(to_string(field.field_label)) %>
                            </div>

                            <div class="text-base text-gray-900 bg-gray-50 p-3 rounded-md border border-gray-100">
                              <%= @field_data |> Map.get(field.field_name, "Not provided") %>
                            </div>
                          </div>
                        <% end %>
                      <% end %>
                    </div>
                  </div>
                  <!-- Right column: Uploaded Documents -->
                  <%= if Enum.any?(@license_data, &(&1.field_type == "upload")) do %>
                    <div>
                      <div class="flex items-center mb-4">
                        <svg
                          class="h-5 w-5 text-brand-10 mr-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>

                        <h3 class="text-lg font-medium text-gray-900">Uploaded Documents</h3>
                      </div>

                      <div class="space-y-4">
                        <%= for field <- @license_data do %>
                          <%= if field.field_type == "upload" &&
               field.field_name == "pop_upload" do %>
                            <%= if is_nil(field.field_dependents) ||
                  field.dependent_selection == (if !Enum.empty?(field.field_dependents),
                    do: @record.data[List.first(field.field_dependents)]) do %>
                              <div class="flex items-center justify-between bg-gray-50 p-4 rounded-lg border border-gray-200 hover:border-indigo-300 hover:shadow-sm transition-all">
                                <div class="flex items-center">
                                  <svg
                                    class="h-8 w-8 text-brand-10 mr-3"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      stroke-linecap="round"
                                      stroke-linejoin="round"
                                      stroke-width="2"
                                      d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                                    />
                                  </svg>

                                  <div>
                                    <div class="text-sm font-medium text-gray-700">
                                      <%= field.field_label %>
                                    </div>

                                    <div class="text-xs text-gray-500 mt-1">
                                      Document
                                    </div>
                                  </div>
                                </div>

                                <.button
                                  type="button"
                                  phx-click="view_document"
                                  phx-value-doc_name={field.field_label}
                                  phx-value-path={@field_data |> Map.get(field.field_name)}
                                  class="px-4 py-2 rounded-md text-sm flex items-center gap-1"
                                >
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      stroke-linecap="round"
                                      stroke-linejoin="round"
                                      stroke-width="2"
                                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                    />
                                    <path
                                      stroke-linecap="round"
                                      stroke-linejoin="round"
                                      stroke-width="2"
                                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                    />
                                  </svg>
                                  View Document
                                </.button>
                              </div>
                            <% end %>
                          <% end %>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>

              <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                <.button
                  :if={is_nil(@record.associated_license_id)}
                  type="button"
                  onclick="history.back()"
                  class="flex items-center gap-2 px-4 py-2 rounded-md transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M10 19l-7-7m0 0l7-7m-7 7h18"
                    />
                  </svg>
                  Back to Applications
                </.button>

                <%= if @approval_level && is_nil(@record.associated_license_id) do %>
                  <div class="flex gap-2">
                    <.button
                      type="button"
                      phx-click="check_amount"
                      phx-value-activity="confirmation"
                      class="flex items-center bg-brand-1 gap-2 px-4 py-2 rounded-md transition-colors"
                    >
                      Enter Amount
                    </.button>
                  </div>
                <% end %>
              </div>
            </.simple_form>
          </div>
        </div>
        <!-- Right column - Price Breakdown and Associated Licenses -->
        <div class="lg:w-1/3">
          <!-- Price Breakdown section -->
          <div class="bg-white shadow-md rounded-xl overflow-hidden border border-gray-200 mb-6">
            <div class="bg-gradient-to-r from-brand-10 to-brand-10 px-6 py-4">
              <div class="flex items-center">
                <svg
                  class="h-5 w-5 text-white mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>

                <h3 class="text-xl font-bold text-white">License Price Breakdown</h3>
              </div>

              <p class="text-indigo-100 text-sm mt-1 ml-7">
                Review the breakdown of the license price
              </p>
            </div>

            <div class="p-6 bg-white rounded-lg shadow-sm border border-gray-100">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Price Summary</h3>

              <div class="space-y-3">
                <!-- Item details with dotted line spacer -->
                <div class="flex items-center justify-between">
                  <div class="font-medium text-gray-700"><%= @record.license.name %></div>

                  <div class="flex items-center">
                    <div class="w-24 text-right font-medium">
                      <%= NumberF.currency(@record.license.amount, "K") %>
                    </div>
                  </div>
                </div>

                <div class="flex items-center justify-between">
                  <div class="font-medium text-gray-700">Compensation Levy</div>

                  <div class="flex items-center">
                    <div class="w-24 text-right font-medium">
                      <%= NumberF.currency(@record.license.other_fees, "K") %>
                    </div>
                  </div>
                </div>

                <%= if @associates != [] do %>
                  <div class="flex items-center justify-between">
                    <div class="font-medium text-gray-700">
                      <%= List.first(@associates).license.name %> (Associated Licenses)
                    </div>

                    <div class="flex items-center">
                      <div class="text-gray-500 px-2">
                        x<%= length(@associates) %>
                      </div>

                      <div class="w-24 text-right font-medium">
                        <%= NumberF.currency(List.first(@associates).license.amount, "K") %>
                      </div>
                    </div>
                  </div>
                <% end %>

                <%= if @associates != [] do %>
                  <!-- Separator line -->
                  <div class="border-t border-gray-200 my-2"></div>
                  <!-- Total amount -->
                  <div class="flex items-center justify-between">
                    <div class="font-semibold text-gray-900">Expected Total Amount</div>

                    <div class="w-24 text-right font-semibold text-gray-900">
                      <%= NumberF.currency(
                        @account_total.license_amount,
                        "K"
                      ) %>
                    </div>
                  </div>

                  <%= if @account_total.amount_paid do %>
                    <!-- Separator line -->
                    <div class="border-t border-gray-200 my-2"></div>
                    <!-- Total paid and pending -->
                    <div class="flex items-center justify-between">
                      <div class="font-semibold text-gray-900">Amount Paid:</div>

                      <div class="w-24 text-right font-semibold text-gray-900">
                        <%= NumberF.currency(
                          @account_total.amount_paid,
                          "K"
                        ) %>
                      </div>
                    </div>

                    <div class="flex items-center justify-between">
                      <div class="font-semibold text-gray-900">Pending Amount:</div>

                      <div class="w-24 text-right font-semibold text-gray-900">
                        <%= NumberF.currency(
                          @account_total.balance,
                          "K"
                        ) %>
                      </div>
                    </div>
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>

          <%= if @associates != [] do %>
            <!-- Associated Licenses section -->
            <div class="bg-white shadow-md rounded-xl overflow-hidden border border-gray-200 sticky top-8">
              <div class="bg-gradient-to-r from-brand-10 to-brand-10 px-6 py-4">
                <h2 class="text-xl font-bold text-white">Associated Licenses</h2>

                <p class="text-indigo-100 text-sm mt-1">
                  Licences associated with <%= @record.record_name %>
                </p>
              </div>

              <div class="p-4">
                <ul class="divide-y divide-gray-100">
                  <%= for associate <- @associates do %>
                    <.link phx-click="view_associate" phx-value-id={associate.id}>
                      <li class="py-3">
                        <div class="flex items-center justify-between hover:bg-gray-50 p-2 rounded-lg hover:text-brand-10 transition-colors">
                          <div>
                            <p class="font-medium">
                              <%= associate.record_name %>
                            </p>

                            <div class="flex items-center mt-2">
                              <span class="text-s ">
                                <%= associate.license.name %>
                              </span>
                            </div>
                          </div>
                        </div>
                      </li>
                    </.link>
                  <% end %>
                </ul>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
<% end %>

<Model.fullscreen
  :if={@live_action in [:view_associate]}
  id="view_associate-modal"
  title="Associated Licence"
  show
  return_to="close_model"
>
  <iframe
    src={@associate}
    id="associate_iframe"
    title="associate"
    style="width: 100%;"
    height="700"
    name="ASSOCIATE"
  >
  </iframe>
</Model.fullscreen>

<Model.fullscreen
  :if={@live_action in [:view_document]}
  id="view_document-modal"
  title={@doc_name}
  show
  return_to="close_model"
>
  <iframe
    src={@document}
    id="document_iframe"
    title="document"
    style="width: 100%;"
    height="700"
    name="DOCUMENT"
  >
  </iframe>
</Model.fullscreen>

<Model.fullscreen
  :if={@live_action in [:view_licence_form]}
  id="view_licence-modal"
  title="Licence Form"
  show
  return_to="close_model"
>
  <iframe
    src={@licence_form}
    id="licence_iframe"
    title="Licence"
    style="width: 100%;"
    height="700"
    name="Licence"
  >
  </iframe>
</Model.fullscreen>

<Model.confirmation_amount
  :if={@live_action == :confirmation_amount}
  show
  id="confirmation_amount-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>
