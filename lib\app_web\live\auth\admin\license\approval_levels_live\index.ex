defmodule AppWeb.Maintenance.ApprovalLevelsLive.Index do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})
  on_mount({AppWeb.UserAuth, :ensure_authenticated})

  alias App.Licenses

  @impl true
  def render(assigns) do
    ~H"""
    <div
      class="p-6"
      x-data="{ show: false }"
      x-init="setTimeout(() => show = true, 100)"
      x-show="show"
      x-transition:enter="transition ease-out duration-500"
      x-transition:enter-start="opacity-0 transform -translate-y-4"
      x-transition:enter-end="opacity-100 transform translate-y-0"
    >
      <div
        class="sm:flex sm:items-center mb-10"
        x-data="{ show: false }"
        x-init="setTimeout(() => show = true, 200)"
        x-show="show"
        x-transition:enter="transition ease-out duration-500"
        x-transition:enter-start="opacity-0 transform -translate-x-4"
        x-transition:enter-end="opacity-100 transform translate-x-0"
      >
        <div class="sm:flex-auto">
          <h1 class="text-2xl font-bold text-gray-800 mb-6">Application Categories</h1>
        </div>
      </div>

      <%= if Enum.empty?(@categories) do %>
        <.empty_state />
      <% else %>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
          <%= for {category, idx} <- Enum.with_index(@categories) do %>
            <div
              x-data="{ show: false, hover: false }"
              x-init={"setTimeout(() => show = true, #{150 * idx})"}
              x-show="show"
              x-transition:enter="transition ease-out duration-500"
              x-transition:enter-start="opacity-0 transform translate-y-8"
              x-transition:enter-end="opacity-100 transform translate-y-0"
              @mouseenter="hover = true"
              @mouseleave="hover = false"
              class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform"
              style={"border-top: 4px solid #{category["color"]}"}
            >
              <div class="relative p-6">
                <!-- Decorative Element -->
                <div
                  class="absolute top-0 right-0 w-24 h-24 opacity-10"
                  style={"background: radial-gradient(circle at top right, #{category["color"]}, transparent 70%)"}
                >
                </div>

                <div class="flex items-start justify-between">
                  <div class="mr-4 flex-1">
                    <!-- Title -->
                    <h3 class="text-xl font-bold text-gray-900 mb-2"><%= category["name"] %></h3>
                    <!-- Description  -->
                    <%= if Map.has_key?(category, "description") do %>
                      <p class="text-gray-600 text-sm line-clamp-2"><%= category["description"] %></p>
                    <% else %>
                      <p class="text-gray-500 text-sm">Explore this category for more information.</p>
                    <% end %>
                  </div>
                  <!-- Icon Circle -->
                  <%!-- <div class="flex-shrink-0">
                    <!-- Badge -->
                    <span
                      class="inline-block px-3 py-1 text-xs font-semibold rounded-full text-white mb-3"
                      style={"background-color: #{category["color"]}"}
                    >
                      Category <%= category["id"] %>
                    </span>
                  </div> --%>
                </div>
                <!-- Action Buttons -->
                <div class="mt-5 flex justify-between items-center">
                  <button
                    phx-value-id={category["id"]}
                    phx-click="view"
                    class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-300 flex items-center gap-2 shadow-sm"
                  >
                    View
                  </button>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    categories = Licenses.list_active_license_categories()

    {:ok, assign(socket, categories: categories)}
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, socket) do
    case target do
      "view" ->
        view(value, socket)

      _ ->
        {:noreply, socket}
    end
  end

  defp view(%{"id" => id}, socket) do
    {:noreply, push_navigate(socket, to: ~p"/maintenance/approval_levels/#{id}")}
  end
end
