defmodule AppWeb.UserLoginLive do
  use AppWeb, :live_view
  alias App.Users, as: Accounts
  alias App.{CustomContext, Settings}

  @impl true
  def render(assigns) do
    ~H"""
    <%= if !connected?(@socket) do %>
      <.live_component module={AppWeb.Login.LoaderComponent} id="FLD:Loader" />
    <% end %>

    <%= if connected?(@socket) do %>
      <div class="min-h-screen flex">
        <!-- Left Side - Blue Background with Logo -->
        <div
          class="hidden lg:flex lg:w-2/5 items-center justify-center relative overflow-hidden"
          style="background: linear-gradient(135deg, #4464AD, #2D4A8A);"
        >
          <div
            class="absolute inset-0"
            style="background: linear-gradient(135deg, rgba(68, 100, 173, 0.2), rgba(45, 74, 138, 0.2));"
          >
          </div>
          <div class="relative z-10 text-center">
            <img src="/images/pbs-logo.png" alt="Logo" class="mx-auto mb-8 h-24 w-auto" />
            <h1 class="text-4xl font-bold text-white mb-4">Welcome to Our Platform</h1>
            <p class="text-white/80 text-lg">Secure access to your account</p>
          </div>
        </div>
        <!-- Right Side - Background Image with Login Form -->
        <div
          class="w-full lg:w-3/5 bg-cover bg-center bg-no-repeat relative flex items-center justify-center p-8"
          style="background-image: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), url('/images/sec-dg.png');"
        >
          <div class={@form_container_animation <> " w-full max-w-md bg-white/90 backdrop-blur-xl shadow-2xl border border-white/20 rounded-3xl overflow-hidden"}>
            <div
              class={@header_animation <> " p-6 text-center backdrop-blur-sm"}
              style="background: linear-gradient(90deg, #292459, #4464AD, #81CCDE);"
            >
              <h1 class="text-2xl font-bold text-white mb-2">Sign In</h1>
              <p class="text-white/80">Access your account</p>
            </div>

            <%= if @process == 1 do %>
              <.simple_form_login
                for={@form}
                phx-change="validate_login"
                phx-submit="login_submit"
                form_class="my-auto"
                class={@form_animation <> " p-6 space-y-5"}
              >
                <.ns_input_form
                  field={@form[:device]}
                  type="hidden"
                  phx-hook="DeviceUuid"
                  label_class="text-white"
                  required
                />
                <.input_form_with_clickable_icon
                  field={@form[:email]}
                  type="text"
                  icon_start="hero-at-symbol"
                  label="Email"
                  required
                  placeholder="Enter your email"
                  class="pl-12 w-full border-0 bg-gray-50/80 backdrop-blur-sm rounded-xl py-4 px-4 focus:ring-2 focus:ring-indigo-500/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder-gray-400"
                />
                <.input_form_with_clickable_icon
                  field={@form[:password]}
                  type={if @show_password, do: "text", else: "password"}
                  icon_start="hero-lock-closed"
                  icon_end={if @show_password, do: "hero-eye-slash", else: "hero-eye"}
                  label="Password"
                  required
                  placeholder="Enter your password"
                  class="pl-12 pr-12 w-full border-0 bg-gray-50/80 backdrop-blur-sm rounded-xl py-4 px-4 focus:ring-2 focus:ring-indigo-500/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder-gray-400"
                  phx-click-away={JS.push("reset_show_password")}
                />
                <div class="flex items-center justify-between">
                  <.button_no_style
                    type="submit"
                    phx-disable-with="Signing in..."
                    class={@button_animation <> " w-full text-white py-4 rounded-xl hover:shadow-xl transition-all duration-300 ease-in-out transform hover:scale-[1.02] font-semibold text-lg"}
                    style="background: linear-gradient(90deg, #292459, #4464AD, #81CCDE);"
                  >
                    Sign In <span aria-hidden="true" class="ml-2">→</span>
                  </.button_no_style>
                </div>

                <div class="mt-4 text-center">
                  <.link
                    phx-click="forget_password"
                    class="inline-block align-baseline font-semibold text-sm text-indigo-600 hover:text-purple-600 transition duration-300"
                  >
                    Forgot your password?
                  </.link>
                </div>
              </.simple_form_login>

              <div class={@footer_animation <> " px-6 pb-6 text-center bg-gray-50/50 backdrop-blur-sm"}>
                <p class="text-sm text-gray-600">
                  Don't have an account?
                  <.link
                    navigate={~p"/users/signup"}
                    class="text-indigo-600 font-semibold hover:text-purple-600 transition duration-300 ml-1"
                  >
                    Sign Up
                  </.link>
                </p>
              </div>
            <% end %>

            <%= if @process == 2 do %>
              <.simple_form_login
                for={@forget}
                id="login_form"
                phx-change="validate_forget"
                phx-submit="forget_submit"
                form_class="md:my-auto"
                class={@form_animation <> " p-4 md:p-6 flex flex-col items-stretch w-full max-w-md gap-4"}
              >
                <h1 class="text-2xl my-4 font-bold self-center animate-fadeIn">Enter New Password</h1>

                <.ns_input_form
                  field={@form[:device]}
                  type="hidden"
                  phx-hook="DeviceUuid"
                  label_class="text-white"
                  required
                />
                <.input_form_with_clickable_icon
                  field={@forget[:password]}
                  type={if @show_password, do: "text", else: "password"}
                  icon_start="hero-lock-closed"
                  icon_end={if @show_password, do: "hero-eye-slash", else: "hero-eye"}
                  label="New Password"
                  required
                  placeholder="Enter your new password"
                  class="pl-12 pr-12 w-full border-0 bg-gray-50/80 backdrop-blur-sm rounded-xl py-4 px-4 focus:ring-2 focus:ring-indigo-500/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder-gray-400"
                  phx-click-away={JS.push("reset_show_password")}
                />
                <.input_form_with_clickable_icon
                  field={@forget[:confirm_password]}
                  type={if @show_password, do: "text", else: "password"}
                  icon_start="hero-lock-closed"
                  icon_end={if @show_password, do: "hero-eye-slash", else: "hero-eye"}
                  label="Confirm Password"
                  placeholder="Confirm your password"
                  required
                  class="pl-12 pr-12 w-full border-0 bg-gray-50/80 backdrop-blur-sm rounded-xl py-4 px-4 focus:ring-2 focus:ring-indigo-500/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder-gray-400"
                  phx-click-away={JS.push("reset_show_password")}
                />
                <.button_no_style
                  type="submit"
                  phx-disable-with="Updating..."
                  class={@button_animation <> " w-full text-white py-4 rounded-xl hover:shadow-xl transition-all duration-300 ease-in-out transform hover:scale-[1.02] font-semibold text-lg"}
                  style="background: linear-gradient(90deg, #292459, #4464AD, #81CCDE);"
                >
                  Update Password <span aria-hidden="true" class="ml-2">→</span>
                </.button_no_style>
              </.simple_form_login>
            <% end %>

            <%= if @process == 3 do %>
              <.simple_form_login
                for={@reset}
                id="reset_form"
                phx-change="validate_forget"
                phx-submit="email_submit"
                form_class="md:my-auto"
                class={@form_animation <> " p-4 md:p-6 flex flex-col items-stretch w-full max-w-md gap-4"}
              >
                <h1 class="text-xl my-4 font-bold self-center ">Enter Email</h1>

                <.input_form_with_clickable_icon
                  field={@form[:email]}
                  type="text"
                  icon_start="hero-at-symbol"
                  label="Email"
                  required
                  placeholder="Enter your email"
                  class="pl-12 w-full border-0 bg-gray-50/80 backdrop-blur-sm rounded-xl py-4 px-4 focus:ring-2 focus:ring-indigo-500/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder-gray-400"
                />
                <div class="">
                  <div class="text-end justify-between mb-2">
                    <.link
                      phx-click="remember_password"
                      class="inline-block align-baseline font-semibold text-sm text-indigo-600 hover:text-purple-600 transition duration-300"
                    >
                      Remembered your password?
                    </.link>
                  </div>
                </div>

                <.button_no_style
                  type="submit"
                  phx-disable-with="Sending..."
                  class={@button_animation <> " w-full text-white py-4 rounded-xl hover:shadow-xl transition-all duration-300 ease-in-out transform hover:scale-[1.02] font-semibold text-lg"}
                  style="background: linear-gradient(90deg, #292459, #4464AD, #81CCDE);"
                >
                  Send Reset Link <span aria-hidden="true" class="ml-2">→</span>
                </.button_no_style>
              </.simple_form_login>
            <% end %>

            <%= if @process == 4 do %>
              <.simple_form
                for={@form}
                id="login_form"
                phx-submit="validate_otp"
                form_class="md:my-auto"
                class={@form_animation <> " p-4 md:p-6 flex flex-col items-stretch w-full max-w-md gap-4"}
              >
                <div class="flex flex-col space-y-6">
                  <h2 class="text-xl font-semibold text-center">
                    <%= if @login_verification,
                      do: "Login Verification",
                      else: "Password Reset Verification" %>
                  </h2>

                  <p class="text-gray-600 text-center mb-4">
                    Please enter the verification code sent to your email
                  </p>

                  <div
                    class="flex flex-row items-center justify-between mx-auto w-full max-w-xs"
                    phx-update="ignore"
                    phx-hook="OTPInput"
                    id="otp"
                  >
                    <%= for i <- 1..@otp_field_number do %>
                      <input
                        name={"#{i}"}
                        type="text"
                        id={"#{i}"}
                        maxlength="1"
                        class="m-2 w-full h-full flex flex-col items-center justify-center text-center px-5 outline-none rounded-xl border border-gray-200 text-lg bg-white focus:bg-gray-50 focus:ring-1 ring-blue-700"
                      />
                    <% end %>
                  </div>

                  <div class="flex flex-col space-y-5">
                    <div>
                      <.button
                        phx-disable-with="Validating..."
                        class={@button_animation <> " w-full text-white py-4 rounded-xl hover:shadow-xl transition-all duration-300 ease-in-out transform hover:scale-[1.02] font-semibold text-lg hover:text-white"}
                        style="background: linear-gradient(90deg, #292459, #4464AD, #81CCDE);"
                      >
                        Verify Code <span aria-hidden="true" class="ml-2">→</span>
                      </.button>
                    </div>

                    <div class="flex flex-row items-center justify-center text-center text-sm font-medium space-x-1 text-gray-500">
                      <p>Didn't receive the code?</p>

                      <a
                        class="flex flex-row items-center text-indigo-600 hover:text-purple-600 transition duration-300 cursor-pointer font-semibold"
                        phx-click="resend_otp"
                      >
                        Resend
                      </a>
                    </div>
                  </div>
                </div>
              </.simple_form>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    login_changeset = Accounts.change_user_login(%{})
    Process.send_after(self(), :info_restart_flash, 5000)
    Process.send_after(self(), :error_restart_flash, 5000)
    Process.send_after(self(), :animate_elements, 100)

    socket
    |> assign(:process, 1)
    |> assign_form(login_changeset, :form)
    |> assign(:menu_open, false)
    |> assign(:show_password, false)
    |> assign(:login_verification, false)
    |> assign(:otp_field_number, 4)
    |> assign(:otp_max_retries, 3)
    |> assign(:otp_retries, 0)
    # Initialize animation classes
    |> assign(:navbar_animation, "opacity-0 translate-y-[-20px]")
    |> assign(:form_container_animation, "opacity-0 scale-95")
    |> assign(:header_animation, "opacity-0")
    |> assign(:form_animation, "opacity-0 translate-y-4")
    |> assign(:button_animation, "opacity-0")
    |> assign(:footer_animation, "opacity-0 translate-y-4")
    |> ok()
  end

  @impl true
  def handle_info(:error_restart_flash, socket), do: noreply(put_flash(socket, :error, nil))
  def handle_info(:info_restart_flash, socket), do: noreply(put_flash(socket, :info, nil))

  def handle_info(:animate_elements, socket) do
    # Stagger animations for a smooth sequence
    Process.send_after(self(), :animate_navbar, 100)
    Process.send_after(self(), :animate_container, 200)
    Process.send_after(self(), :animate_header, 400)
    Process.send_after(self(), :animate_form, 600)
    Process.send_after(self(), :animate_button, 800)
    Process.send_after(self(), :animate_footer, 1000)
    {:noreply, socket}
  end

  def handle_info(:animate_navbar, socket),
    do:
      {:noreply,
       assign(socket, :navbar_animation, "opacity-100 translate-y-0 transition-all duration-500")}

  def handle_info(:animate_container, socket),
    do:
      {:noreply,
       assign(
         socket,
         :form_container_animation,
         "opacity-100 scale-100 transition-all duration-500"
       )}

  def handle_info(:animate_header, socket),
    do: {:noreply, assign(socket, :header_animation, "opacity-100 transition-all duration-500")}

  def handle_info(:animate_form, socket),
    do:
      {:noreply,
       assign(socket, :form_animation, "opacity-100 translate-y-0 transition-all duration-500")}

  def handle_info(:animate_button, socket),
    do: {:noreply, assign(socket, :button_animation, "opacity-100 transition-all duration-500")}

  def handle_info(:animate_footer, socket),
    do:
      {:noreply,
       assign(socket, :footer_animation, "opacity-100 translate-y-0 transition-all duration-500")}

  @impl true
  def handle_event(target, params, socket) do
    case target do
      "validate_login" ->
        validate_login(params, socket)
        |> noreply()

      "login_submit" ->
        login_submit(params, socket)
        |> noreply()

      "opt_save" ->
        opt_save(params, socket)
        |> noreply()

      "wrong_text" ->
        wrong_text(params, socket)
        |> noreply()

      "forget_submit" ->
        forget_submit(params, socket)
        |> noreply()

      "validate_forget" ->
        validate_forget(params, socket)
        |> noreply()

      "toggle-menu" ->
        {:noreply, assign(socket, menu_open: !socket.assigns.menu_open)}

      "toggle_show_password" ->
        assign(socket, :show_password, !socket.assigns.show_password)
        |> noreply()

      "forget_password" ->
        {:noreply, forget_password(params, socket)}

      "reset_show_password" ->
        if socket.assigns.show_password do
          assign(socket, :show_password, false)
          |> noreply()
        else
          noreply(socket)
        end

      "remember_password" ->
        {:noreply, remember_password(params, socket)}

      "email_submit" ->
        {:noreply, check_email(params, socket)}

      "validate_otp" ->
        {:noreply, validate_otp(params, socket)}

      "resend_otp" ->
        {:noreply, resend_otp(socket.assigns.user_found, socket)}
    end
  end

  defp validate_login(params, socket) do
    login_changeset =
      Accounts.change_user_login(params["user"])
      |> Map.put(:action, :validate)

    socket
    |> assign_form(login_changeset, :form)
  end

  defp validate_forget(params, socket) do
    login_changeset =
      Accounts.change_password(params["user"])
      |> Map.put(:action, :validate)

    socket
    |> assign_form(login_changeset, :forget)
  end

  defp forget_password(_params, socket) do
    login_changeset = Accounts.change_password(%{})

    socket
    |> assign(:process, 3)
    |> assign(:login_verification, false)
    |> assign_form(login_changeset, :reset)
  end

  defp remember_password(_params, socket) do
    socket
    |> assign(:process, 1)
  end

  def check_email(%{"user" => params}, socket) do
    Accounts.send_otp_to_user(params)
    |> case do
      {:ok, user} ->
        socket
        |> assign(:user_found, user)
        |> assign(:process, 4)
        |> assign(:login_verification, false)
        |> assign(:otp_field_number, 4)
        |> put_flash(:info, "Successfully sent confirmation!")

      {:error, _} ->
        socket
        |> put_flash(:info, "Successfully sent confirmation!")
    end
  end

  defp resend_otp(user, socket) do
    Accounts.resend_otp(user)
    |> case do
      {:ok, _user} ->
        socket
        |> put_flash(:info, "Successfully sent otp!")

      {:error, _} ->
        socket
        |> put_flash(:error, "Failed to send otp!")
    end
  end

  def validate_otp(params, socket) do
    Accounts.validate_otp(socket, socket.assigns.user_found, params)
    |> case do
      {:ok, user} ->
        if socket.assigns.login_verification do
          login_success(socket, user.id, socket.assigns.device_id)
        else
          login_changeset = Accounts.change_password(%{})

          socket
          |> assign(:user, user)
          |> assign(:process, 2)
          |> assign(:show_password, false)
          |> assign_form(login_changeset, :forget)
          |> put_flash(:info, "Change your Password!")
        end

      {:error, _} ->
        socket
        |> put_flash(:error, "Invalid OTP!")
    end
  end

  def login_submit(%{"user" => user_params}, socket) do
    %{"email" => email, "password" => password, "device" => device} = user_params
    user = Accounts.get_user_by_email_and_password(email, password)

    cond do
      is_nil(user) ->
        # In order to prevent user enumeration attacks, don't disclose whether the email is registered.
        login_changeset = Accounts.change_user_login(Map.put(user_params, "password", ""))
        Process.send_after(self(), :error_restart_flash, 5000)
        message = "Invalid email or password"

        socket
        |> assign_form(login_changeset, :form)
        |> put_flash(:error, message)
        |> LiveFunctions.sweet_alert(message, "error")

      user.status != "A" ->
        # In order to prevent user enumeration attacks, don't disclose whether the email is registered.
        login_changeset = Accounts.change_user_login(Map.put(user_params, "password", ""))
        Process.send_after(self(), :error_restart_flash, 5000)
        message = "User has insufficient privileges. Contact your supervisor"

        socket
        |> put_flash(:error, message)
        |> assign_form(login_changeset, :form)
        |> LiveFunctions.sweet_alert(message, "error")

      user.auto_password == "Y" ->
        # User needs to set a new password
        login_changeset = Accounts.change_password(%{})

        socket
        |> assign(:user, user)
        |> assign(:process, 2)
        |> assign(:show_password, false)
        |> assign_form(login_changeset, :forget)
        |> then(fn socket ->
          Process.send_after(self(), :animate_form, 100)
          Process.send_after(self(), :animate_button, 300)
          socket
        end)

      true ->
        if !Settings.check_setting_status("send_emails") do
          login_success(socket, user.id, device)
        else
          # Send OTP for MFA
          case send_login_otp(user) do
            {:ok, _} ->
              socket
              |> assign(:user_found, user)
              |> assign(:process, 4)
              |> assign(:login_verification, true)
              |> assign(:device_id, device)
              |> put_flash(:info, "An OTP has been sent to your email!")
              |> then(fn socket ->
                Process.send_after(self(), :animate_form, 100)
                Process.send_after(self(), :animate_button, 300)
                socket
              end)

            {:error, message} ->
              socket
              |> put_flash(:error, message)
              |> LiveFunctions.sweet_alert(message, "error")
          end
        end
    end
  end

  # OTP for login verification
  defp send_login_otp(user) do
    Accounts.send_login_otp(user)
  end

  defp opt_save(params, %{assigns: assigns} = socket) do
    otp =
      Enum.reduce(
        1..socket.assigns.otp_field_number,
        [],
        fn x, result ->
          Enum.concat(result, [params[to_string(x)]])
        end
      )
      |> Enum.join("")

    if Pbkdf2.verify_pass(otp, assigns.otp_session_code) do
      login_success(socket, assigns.user.id, assigns.device_id)
    else
      if assigns.otp_retries >= assigns.otp_max_retries do
        assign(socket, :process, 1)
        |> assign(:error_message, "Maximum attempts reached")
        |> put_flash(:error, "Maximum attempts reached")
        |> LiveFunctions.sweet_alert("Maximum attempts reached", "error")
      else
        assign(socket, :otp_retries, assigns.otp_retries + 1)
        |> assign(:opt_failed_message, "Invalid code, Please try again")
        |> LiveFunctions.sweet_alert("Invalid code, Please try again", "warning")
      end
    end
  end

  defp wrong_text(_params, socket) do
    socket
    |> assign(:process, 1)
    # Re-animate the login form when returning to it
    |> then(fn socket ->
      Process.send_after(self(), :animate_form, 100)
      Process.send_after(self(), :animate_button, 300)
      socket
    end)
  end

  defp login_success(socket, user_id, device_id) do
    token =
      Phoenix.Token.sign(
        %Phoenix.Socket{endpoint: socket.endpoint},
        "userSessionAuthentication",
        %{
          "user_id" => user_id,
          "device_id" => device_id
        }
      )

    redirect(socket, to: ~p"/session/#{token}")
  end

  def forget_submit(%{"user" => user_params}, socket) do
    Accounts.change_user_password_on_login(socket.assigns.user, user_params)
    |> case do
      {:ok, _} ->
        login_success(socket, socket.assigns.user.id, user_params["device"])

      {:error, _, changeset, _} ->
        message = CustomContext.traverse_errors(changeset.errors)

        socket
        |> assign_form(changeset, :forget)
        |> assign(:show_password, false)
        |> put_flash(:error, message)
        |> LiveFunctions.sweet_alert(message, "error")
    end
  end
end
