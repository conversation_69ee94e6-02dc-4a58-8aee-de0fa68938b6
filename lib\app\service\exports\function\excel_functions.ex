defmodule App.Service.Export.ExcelFunctions do
  @moduledoc false
  alias AppWeb.Endpoint
  # alias App.Service.Export.Functions
  @extension ".xlsx"
  alias App.Service.Export.{
    ExcelClientProfileService,
    ExcelUsersService,
    ExcelContactPersonService,
    ExcelSenderService,
    ExcelPurchasesService,
    ExcelRatesService,
    ExcelErrorService,
    ExcelApiLogService,
    ExcelSessionLogService,
    ExcelSystemLogService,
    ExcelMonthlyStaticsticsService,
    ExcelSelfRegistrationApplicationsService,
    ExcelPaymentPlansService,
    ExcelPrepaidClientStatementService,
    ExcelPostpaidClientStatementService,
    ExcelApiServicesService,
    ExcelSmppServicesService,
    ExcelDepartmentRolesManagementService,
    ExcelAccessRolesService,
    ExcelAdminRoleAccessService,
    ExcelAdminAccessRoleNoneRoleUserAccessService,
    ExcelServiceProviderReport,
    ExcelSmsLogService,
    ExcelClientStatisticsService,
    ExcelSmsLogArchieve,
    ExcelTransactionReportsService,
    ExcelAnnualStaticsticsService,
    Functions
  }

  def annual_report_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Annual Statistical Report in Excel",
      "annual_statistical_report",
      @extension,
      &ExcelAnnualStaticsticsService.index/2
    )
  end

  def monthly_report_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Periodic Statistical Report in Excel",
      "periodic_statistical_report",
      @extension,
      &ExcelMonthlyStaticsticsService.index/2
    )
  end

  def transaction_reports_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Transaction Reports in Excel",
      "transaction_reports",
      @extension,
      &ExcelTransactionReportsService.index/2
    )
  end

  def sms_logs_archieve(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Sms Logs Archieve in Excel",
      "sms_logs_archieve",
      @extension,
      &ExcelSmsLogArchieve.index/2
    )
  end

  def client_statistics_report(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Client Statistics in Excel",
      "client_statistics",
      @extension,
      &ExcelClientStatisticsService.index/2
    )
  end

  def sms_logs_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Sms Logs Service  in Excel",
      "Sms Logs Service",
      @extension,
      &ExcelSmsLogService.index/2
    )
  end

  def service_provider_report(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Service Provider Reports in Excel",
      "service_provider_reports",
      @extension,
      &ExcelServiceProviderReport.index/2
    )
  end

  def admin_access_role_none_role_user_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Admin Access Role for None Role User in Excel",
      "Admin Access Role None Role User",
      @extension,
      &ExcelAdminAccessRoleNoneRoleUserAccessService.index/1
    )
  end

  def admin_access_role_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for Admin Access Role Management in Excel",
      "Admin Access Role Management",
      @extension,
      &ExcelAdminRoleAccessService.index/1
    )
  end

  def department_roles_management_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for Department Roles Management in Excel",
      "Department Roles Management",
      @extension,
      &ExcelDepartmentRolesManagementService.index/1
    )
  end

  def access_roles_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for Access Roles Management in Excel",
      "Access Roles Management",
      @extension,
      &ExcelAccessRolesService.index/1
    )
  end

  def smpp_services_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for SMPP Services in Excel",
      "SMPP Services",
      @extension,
      &ExcelSmppServicesService.index/2
    )
  end

  def api_services_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for API Services in Excel",
      "API Services",
      @extension,
      &ExcelApiServicesService.index/2
    )
  end

  def postpaid_client_statement_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for Postpaid Client Statement in Excel",
      "postpaid_client_statement",
      @extension,
      &ExcelPostpaidClientStatementService.index/2
    )
  end

  def prepaid_client_statement_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading for Prepaid Client Statement in Excel",
      "prepaid_client_statement",
      @extension,
      &ExcelPrepaidClientStatementService.index/2
    )
  end

  def payment_plans_service(socket, payload) do
    Functions.run_export(
      socket,
      payload,
      "Downloading Payment Plans in Excel",
      "Payment Plans",
      @extension,
      &ExcelPaymentPlansService.index/2
    )
  end

  def self_registration_applications_service(socket, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Self Registration Applications in Excel",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = ExcelSelfRegistrationApplicationsService.index(socket, payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Self Registration Applications in Excel is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "self_registration_applications", date, ".xlsx")
  end

  def system_logs_service(_socket, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading System Logs Management in Excel",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = ExcelSystemLogService.index(payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for System Logs Management in Excel is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "system_audit_logs_service", date, ".xlsx")
  end

  def session_logs_service(_socket, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading API Logs Management in Excel",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = ExcelSessionLogService.index(payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for API Logs Management in Excel is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "session_logs_service", date, ".xlsx")
  end

  def api_logs_service(_socket, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading API Logs Management in Excel",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = ExcelApiLogService.index(payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for API Logs Management in Excel is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "api_logs_service", date, ".xlsx")
  end

  def error_service(assigns, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Messaging Errors in Excel",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = ExcelErrorService.index(assigns, payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Messaging Errors in Excel is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "messaging_error", date, ".xlsx")
  end

  def rates_service(assigns, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Messaging Rates in Excel",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = ExcelRatesService.index(assigns, payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Messaging Rates in Excel is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "rates_service", date, ".xlsx")
  end

  def purchase_service(assigns, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Senders table in Excel",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = ExcelPurchasesService.index(assigns, payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Senders table in Excel is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "purchases_service", date, ".xlsx")
  end

  def sender_service(assigns, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Senders table in Excel",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = ExcelSenderService.index(assigns, payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Senders table in Excel is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "sender_service", date, ".xlsx")
  end

  def client_profile_service(assigns, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Client Profiles table in Excel",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = ExcelClientProfileService.index(payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Client Profiles table in Excel is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "client_profile_service", date, ".xlsx")
  end

  def contact_person_service(_socket, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Contacat Person table in Excel",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = ExcelContactPersonService.index(payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Contacat Person table in Excel is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "contact_person", date, ".xlsx")
  end

  def user_service(_socket, payload, date \\ Timex.now()) do
    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Downloading Admin User table in Excel",
        expression: "toast",
        timer: 8000,
        icon: "info"
      }
    )

    file_content = ExcelUsersService.index(payload)

    Endpoint.broadcast(
      "sweet_alert:" <> to_string(payload["browser_id"]),
      "sweet_alert:" <> to_string(payload["browser_id"]),
      %{
        message: "Download for Admin Users table in Excel is Completed",
        expression: "toast",
        icon: "success"
      }
    )

    Functions.export_done(file_content, "users_service", date, ".xlsx")
  end
end
