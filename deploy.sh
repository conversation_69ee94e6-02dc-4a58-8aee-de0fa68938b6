#!/bin/bash

# Define the image name and container name
IMAGE_NAME="sms_gw_client_app"
CONTAINER_NAME="sms_gw_client_app"

# Ask the user for a port number or default to 8500 if no input is provided
read -p "Enter the port number (default is 8500): " PORT
HOST_PORT=${PORT:-8500}  # Default to 8500 if no input is given
CONTAINER_PORT=80

# Display the chosen port
echo "Using port $HOST_PORT for the application."

# Build the Docker image (if not already built)
echo "Building the Docker image..."
sudo docker build -t $CONTAINER_NAME .

# Run the Docker container with the chosen port
echo "Running the Docker container on port $HOST_PORT..."

# Step 5: Check if the container already exists
EXISTING_CONTAINER=$(docker ps -aq -f name=$CONTAINER_NAME)

if [ -n "$EXISTING_CONTAINER" ]; then
    # Check if the container is already running
    RUNNING_PORT=$(docker port $EXISTING_CONTAINER 8000 | cut -d ':' -f 2)

    if [ "$RUNNING_PORT" == "$HOST_PORT" ]; then
        # If the container is running on the desired port, skip restart
        echo "Container is already running on port $HOST_PORT. No changes required."
        exit 0
    else
        # If the container is running on a different port, stop and remove it
        echo "Container is running on port $RUNNING_PORT, stopping and removing existing container..."
        docker stop $CONTAINER_NAME
        docker rm $CONTAINER_NAME
    fi
fi

echo "Running the Docker container on external port $HOST_PORT (mapped to internal port $CONTAINER_PORT)..."

sudo docker run -d \
  --name $CONTAINER_NAME \
  -p $HOST_PORT:$CONTAINER_PORT -e \
  RELEASE_NAME=pbs_gw_report_rel \
  --restart always $IMAGE_NAME

# Step 7: Confirm the container is running
echo "Deployment complete. Verifying if the container is running..."
docker ps -f name=$CONTAINER_NAME