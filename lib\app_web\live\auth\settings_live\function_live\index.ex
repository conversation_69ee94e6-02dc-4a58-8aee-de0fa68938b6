defmodule AppWeb.Authenticated.SettingsLive.FunctionLive.Index do
  use AppWeb, :live_view

  on_mount({AppWeb.UserAuth, :mount_current_user})

  alias App.Settings

  @impl true
  def render(assigns) do
    ~H"""
    <h2 class="sm:text-2xl font-bold text-xl pt-4 md:pt-1"><%= @page_title %></h2>

    <div class="grid max-w-2xls mx-auto">
      <div class="items-center text-[#202142]">
        <div class="mt-4 flow-root">
          <div class=" sm:-mx-6 lg:-mx-8">
            <%= if @settings != [] do %>
              <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-4 py-4">
                  <%= for function <-@settings do %>
                    <div class="rounded-lg bg-white border px-4 py-4 gap-4">
                      <h2 class="block pb-2 text-md font-medium text-gray-800 ">
                        <%= Enum.map(String.split(function.name, "_"), &String.capitalize/1)
                        |> Enum.join(" ") %> <span><i>Function</i></span>
                      </h2>

                      <div class=" bg-gray-50 rounded-r-lg border-l-4 border-brand-10">
                        <h2 class="p-4 text-sm text-gray-500">
                          <%= function.description %>.
                        </h2>
                      </div>

                      <%= if function.status == false do %>
                        <div class="mt-4">
                          <label class="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              phx-click="settings_submit"
                              phx-value-status="true"
                              phx-value-id={function.id}
                              class="sr-only peer"
                            />
                            <div class="w-11 h-6 bg-gray-200 rounded-full peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-1">
                            </div>
                          </label>
                        </div>
                      <% end %>

                      <%= if function.status == true do %>
                        <div class="mt-4">
                          <label class="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              phx-click="settings_submit"
                              phx-value-status="false"
                              phx-value-id={function.id}
                              class="sr-only peer"
                              checked
                            />
                            <div class="w-11 h-6 bg-gray-200 rounded-full peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-1">
                            </div>
                          </label>
                        </div>
                      <% end %>
                    </div>
                  <% end %>
                </div>
              </div>
            <% else %>
              <div class="text-center mt-10 text-rose-500">No Function Settings</div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def mount(_params, session, %{assigns: _assigns} = socket) do
    socket =
      assign(socket, settings: [])
      |> assign(page_title: "Function Settings")
      |> assign(maker_checker: false)
      |> assign(live_socket_id: session["live_socket_id"])
      |> LivePageControl.order_by_composer()

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    if connected?(socket), do: send(self(), {:get_list, params})

    {:noreply,
     socket
     |> assign(:params, params)
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Function Settings")
  end

  @impl true
  def handle_info(data, socket), do: handle_info_switch(socket, data)

  defp handle_info_switch(socket, data) do
    case data do
      :get_list ->
        list(socket, %{"sort_direction" => "desc", "sort_field" => "id"})

      {:get_list, params} ->
        list(socket, params)

      {_record, _info} ->
        send(self(), {:get_list, socket.assigns.params})

        {
          :noreply,
          assign(socket, :live_action, :index)
          |> apply_action(:index, %{})
        }
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, socket) do
    case target do
      "settings_submit" ->
        settings_submit(socket, value)
    end
  end

  defp settings_submit(socket, value) do
    Settings.update_settings_status(socket, value["id"], value["status"], value)
    |> case do
      {:ok, _} ->
        send(self(), {:get_list, socket.assigns.params})

        {
          :noreply,
          socket
          |> assign(:live_action, :index)
          |> LiveFunctions.sweet_alert(
            "Successfully updated function settings.",
            "success"
          )
        }

      {:error, _} ->
        send(self(), {:get_list, socket.assigns.params})

        {
          :noreply,
          socket
          |> assign(:live_action, :index)
          |> LiveFunctions.sweet_alert(
            "Failed to update function settings.",
            "error"
          )
        }
    end

    {:noreply, socket}
  end

  defp list(socket, _params) do
    {
      :noreply,
      assign(socket, :settings, Settings.list_settings())
    }
  end
end
