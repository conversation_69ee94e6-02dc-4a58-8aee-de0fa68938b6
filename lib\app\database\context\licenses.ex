defmodule App.Licenses do
  @moduledoc """
  The Licenses context.
  """
  # App.Licenses.get_reprint("2")
  # App.Licenses.get_license_data("2")
  # App.Licenses.query_user_with_expired_conditions(6)
  import Ecto.Query, warn: false

  alias App.Registration
  alias App.{Repo, Send.Email, Notification.UserNotifications, Utilities}
  alias Logs.Audit

  alias App.Schema.LicenseAccounts
  alias App.LicenseReviews.Comments
  alias App.Files.UploadedFile
  alias App.Licensing.IssuedLicense

  alias App.Licenses.{
    License,
    LicenseFields,
    LicenseData,
    LicenseCategoriesData,
    LicenseMapping,
    LicenseApprovalLogs,
    LicenseCategories,
    ApprovalLevels
  }

  alias App.Accounts.User
  alias App.Licenses.LicenseConditionsMapping

  alias App.SummaryData.SummarisedData
  alias App.{Accounts, Accounts.User, CustomContext}

  @topic "licenses"

  def subscribe, do: CustomContext.subscribe(@topic)

  def get_license_approval_status_list(department_id, role_id) do
    ApprovalLevels
    |> where([a], a.department_id == ^department_id and a.role_id == ^role_id)
    |> select([a], a.approval_status)
    |> Repo.all()
  end

  def get_returned_to_applicant_query(user_id) do
    LicenseApprovalLogs
    |> where([a], a.user_id == ^user_id and a.is_approved == false)
    |> order_by([a], desc: a.inserted_at)
    |> select([a], a.license_mapping_id)
    |> distinct([a], a.license_mapping_id)
  end

  def get_approved_application_query(user_id) do
    LicenseApprovalLogs
    |> where([a], a.user_id == ^user_id and a.is_approved == true)
    |> order_by([a], desc: a.inserted_at)
    |> select([a], a.license_mapping_id)
    |> distinct([a], a.license_mapping_id)
  end

  def get_returned_to_applicant_mappings(user_id) do
    subquery = get_returned_to_applicant_query(user_id)

    LicenseMapping
    |> where([lm], lm.id in subquery(subquery) and lm.status == ^(-1))
    |> Repo.aggregate(:count, :id)
  end

  def get_submitted_to_supervisor(status_list, user_id) do
    subquery = get_approved_application_query(user_id)
    new_status = Enum.map(status_list, &(&1 + 1))

    LicenseMapping
    |> where([lm], lm.id in subquery(subquery) and lm.status in ^new_status)
    |> Repo.aggregate(:count, :id)
  end

  def get_license_approval_status(department_id, role_id) do
    ApprovalLevels
    |> where([a], a.department_id == ^department_id and a.role_id == ^role_id)
    |> select([a], a.approval_status)
    |> order_by([a], desc: a.id)
    |> limit(1)
    |> Repo.one()
  end

  def get_license_category_by_name(name) do
    LicenseCategories
    |> where([a], a.name == ^name)
    |> limit(1)
    |> Repo.one()
  end

  def get_new_applications_count(status) do
    LicenseMapping
    |> where(
      [a],
      a.approved == false and a.status in ^status and is_nil(a.associated_license_id)
    )
    |> Repo.aggregate(:count, :id)
  end

  def get_market_operations(status, _user_id) do
    LicenseMapping
    |> where(
      [a],
      a.approved == ^false and is_nil(a.associated_license_id) and a.status > ^(status || 0)
    )
    |> Repo.aggregate(:count, :id)
  end

  def get_categories!() do
    LicenseCategories
    |> where([a], a.status == 1)
    |> select([a], {a.name, a.id})
    |> Repo.all()
  end

  def get_replacemecent(id) do
    LicenseMapping
    |> where([a], a.is_replaced == ^true and a.license_id == ^id)
    |> select([a], %{
      id: a.id,
      replace_reason: a.replace_reason,
      replace_comments: a.replace_comments
    })
    |> limit(1)
    |> Repo.one()
  end

  def get_reprint(id) do
    LicenseMapping
    |> where([a], a.is_replaced == ^true and a.license_id == ^id)
    |> select([a], %{
      id: a.id,
      reprint_reason: a.reprint_reason,
      reprint_comments: a.reprint_comments
    })
    |> limit(1)
    |> Repo.one()
  end

  def get_categories_by_id!(id) do
    LicenseCategories
    |> where([a], a.id == ^id)
    |> select([a], {a.name, a.id})
    |> Repo.all()
  end

  def get_category_by_name!(name) do
    LicenseCategories
    |> where([a], a.name == ^name)
    |> limit(1)
    |> Repo.one()
  end

  def check_licenses(user_id, name) do
    LicenseMapping
    |> join(:left, [a], b in assoc(a, :categories))
    |> where([a, b], a.user_id == ^user_id and a.approved == false and b.name == ^name)
    |> limit(1)
    |> Repo.exists?()
  end

  def get_total_issued_licenses() do
    LicenseMapping
    |> where([a], a.approved == true)
    |> Repo.aggregate(:count, :id)
  end

  def get_total_declined_licenses() do
    LicenseMapping
    |> where([a], a.approved == false and a.status == ^(-1))
    |> Repo.aggregate(:count, :id)
  end

  def querying_10_recent_licenses() do
    LicenseMapping
    |> join(:left, [a], b in assoc(a, :user))
    |> where([a, b], a.approved == true or a.approved == false)
    |> where([a, b], is_nil(a.associated_license_id))
    |> order_by([a, b], desc: a.inserted_at)
    |> select([a, b], %{
      id: a.id,
      record_name: a.record_name,
      status: a.status,
      user_id: a.user_id,
      license_id: a.license_id,
      approved: a.approved,
      reason: a.reason,
      inserted_at: a.inserted_at,
      comments: a.comments,
      user_email: b.email,
      fname: fragment("concat(?, ' ', ?)", b.first_name, b.last_name)
    })
    |> limit(10)
    |> Repo.all()
  end

  def change_upload_file(%UploadedFile{} = file, attrs \\ %{}) do
    UploadedFile.changeset(file, attrs)
  end

  def get_uploaded_files_by_application!(id) do
    UploadedFile
    |> where([a], a.application_id == ^id and a.is_condition == false)
    |> order_by([a], asc: a.level)
    |> Repo.all()
  end

  def upload_level_file(socket, file) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "file",
      UploadedFile.changeset(%UploadedFile{}, %{
        "filename" => Path.basename(file),
        "path" => file,
        "level" => socket.assigns.record.status,
        "user_id" => socket.assigns.current_user.id,
        "application_id" => socket.assigns.record.id
      })
    )
    |> Repo.transaction()
  end

  def upload_level_file_condition(socket, file) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "file",
      UploadedFile.changeset(%UploadedFile{}, %{
        "filename" => Path.basename(file),
        "path" => file,
        "type" => "uploaded_condition",
        "user_id" => socket.assigns.current_user.id,
        "application_id" => socket.assigns.application_id
      })
    )
    |> Repo.transaction()
  end

  def upload_conditional_file(socket, file, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "file",
      UploadedFile.changeset(%UploadedFile{}, %{
        "filename" => Path.basename(file),
        "path" => file,
        "user_id" => socket.assigns.current_user.id,
        "application_id" => socket.assigns.params["license_id"],
        "is_condition" => true,
        "condition_map_id" => attrs["condition_id"]
      })
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} uploaded a Condition for: #{Path.basename(file)}",
      "CONDITIONAL APPROVALS",
      attrs,
      "Conditional Approvals"
    )
    |> Repo.transaction()
  end

  def upload_level_file_condition(socket, file) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "file",
      UploadedFile.changeset(%UploadedFile{}, %{
        "filename" => Path.basename(file),
        "path" => file,
        "type" => "uploaded_condition",
        "user_id" => socket.assigns.current_user.id,
        "application_id" => socket.assigns.application_id
      })
    )
    |> Repo.transaction()
  end

  def update_data_key(socket, attrs) do
    socket.assigns.record
    |> LicenseMapping.data_update_changeset(%{"data" => attrs})
    |> Repo.update()
  end

  def update_current_step(record, attrs) do
    record
    |> LicenseMapping.changeset_draft(%{"current_step" => attrs})
    |> Repo.update()
  end

  def save_license_mapping_field(%{assigns: assigns} = _socket, attrs \\ %{}) do
    record = get_application_by_user_id(assigns.current_user.id, assigns.license_id)

    Ecto.Multi.new()
    |> Ecto.Multi.insert_or_update(
      "field_registration",
      LicenseMapping.insert_changeset(
        record || %LicenseMapping{},
        %{
          "data" => attrs,
          "record_name" =>
            String.trim(Map.get(attrs, assigns.chosen_licence.primary_key) || "NOT PROVIDED"),
          "license_id" => assigns.license_id,
          "categories_id" => assigns.chosen_licence.categories_id,
          "status" => 0,
          "user_id" => assigns.current_user.id
        }
      )
    )
    |> Repo.transaction()
  end

  @doc """
  Returns the list of licenses.

  ## Examples

      iex> list_licenses()
      [%License{}, ...]

  """
  def list_licenses do
    Repo.all(License)
  end

  def get_license_name(id) do
    License
    |> where([a], a.id == ^id)
    |> select([a], %{
      name: a.name
    })
    |> limit(1)
    |> Repo.one()
  end

  def get_license_by_id(id) do
    License
    |> where([a], a.id == ^id)
    |> limit(1)
    |> Repo.one()
  end

  def get_license_category_by_id(id) do
    License
    |> where([a], a.id == ^id)
    |> select([a], a.categories_id)
    |> limit(1)
    |> Repo.one()
  end

  def checking_license_data(socket, param) do
    IO.inspect(param, label: "param!!!!!!!!!!!!!!!!!!!!!!!!")
  end

  def total_licenses() do
    License
    |> Repo.aggregate(:count, :id)
  end

  def select_license_not_in(list \\ []) when is_list(list) do
    License
    |> where([a], a.id not in ^list and a.status == ^"A" and a.require_license == false)
    |> select([a], {a.name, a.id})
    |> Repo.all()
  end

  def select_categories do
    LicenseCategories
    |> where([a], a.status == ^1)
    |> select([a], {a.name, a.id})
    |> Repo.all()
  end

  def query_by_license() do
    LicenseFields
    |> select([a], %{
      id: a.id,
      field_name: a.field_name,
      field_label: a.field_label
    })
    |> Repo.all()
  end

  def get_licence_data_by_license_id_summary(license) do
    LicenseData
    |> join(:left, [a], b in LicenseFields, on: a.license_field_id == b.id)
    # |> join(:left, [a], b in LicenseFields, on: a.license_field_id == b.id)
    |> where([a, _b], a.license_id == ^license and a.status == ^1)
    |> select([a, b], %{
      id: a.id,
      license_id: a.license_id,
      field: a.license_field_id,
      field_name: b.field_name,
      field_label: b.field_label
    })
    # |> select([_, a], a.license_id)
    |> Repo.all()
  end

  def get_licence_data_by_license_id(license_id) do
    LicenseData
    |> join(:left, [a], b in LicenseFields, on: a.license_field_id == b.id)
    |> where([a], a.license_id == ^license_id and a.status == ^1)
    |> select([_, b], b.id)
    |> Repo.all()
  end

  def query_from_licence_field() do
    LicenseFields
    |> select([a], %{
      id: a.id,
      field_name: a.field_name,
      field_label: a.field_label
    })
    |> Repo.all()
  end

  def submit_for_review(attrs, socket) do
    IO.inspect(attrs, label: "Reason!!!!!!!!!!!!!!!!!!!")
    field_id = query_by_license_field_id!(attrs["id"])

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :submit_review,
      LicenseFields.changeset(field_id, %{
        attention_field: attrs["field"],
        attention_status: "submitted",
        reason: attrs["reason"]
      })
    )
    |> Repo.transaction()
  end

  def remove_review(attrs, socket) do
    field_id = query_by_license_field_id!(attrs["id"])

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :submit_review,
      LicenseFields.changeset(field_id, %{
        attention_field: attrs["field"],
        attention_status: "removed"
      })
    )
    |> Repo.transaction()
  end

  def query_field_label do
    LicenseFields
    |> select([a], %{
      field_label: a.field_label,
      id: a.id
      # license_id: a.license_id
    })
    |> Repo.all()
  end

  def query_field_label_for_review() do
    LicenseFields
    |> where([a], a.attention_status == ^"submitted")
    |> select([a], %{
      field_label: a.field_label,
      id: a.id,
      attention_status: a.attention_status
    })
    |> Repo.all()
  end

  def checking_field_label_for_review() do
    LicenseFields
    |> where([a], a.attention_status == ^"submitted")
    |> select([a], %{
      field_label: a.field_label,
      id: a.id
    })
    |> Repo.all()
    |> Enum.map(fn x -> x.id end)
  end

  def query_by_license_field_id!(id), do: Repo.get!(LicenseFields, id)

  def list_active_licenses_by_user_id(user_id) do
    LicenseMapping
    |> join(:left, [a], b in assoc(a, :license))
    |> where([a, b], b.status == ^"A" and a.user_id == ^user_id)
    |> select([a, b], %{
      "id" => a.id,
      "data" => a.data,
      "icon" => b.icon,
      "form_id" => b.id,
      "title" => b.name,
      "color" => b.color,
      "status" => a.status,
      "form_number" => b.form_number,
      "parent_id" => a.associated_license_id,
      "approval_status" => a.approval_status,
      "count_down_days" => a.count_down_days,
      "associated_license_id" => b.associated_license_id,
      "count_down_start_date" => a.count_down_start_date
    })
    |> distinct(true)
    |> Repo.all()

    # |> Enum.flat_map(fn record ->
    #   associate =
    #     if record["associated_license_id"] && record["status"] == 1 do
    #       case get_license_mapping_by_license_id_and_user_id(
    #              record["associated_license_id"],
    #              user_id
    #            ) do
    #         nil ->
    #           [
    #             get_associate_license!(record["associated_license_id"])
    #             |> Map.put("parent_id", record["id"])
    #           ]

    #         _ ->
    #           []
    #       end
    #     else
    #       []
    #     end

    #   [record | associate]
    # end)
  end

  def get_associate_license!(id) do
    License
    |> where([a], a.id == ^id and a.status == ^"A")
    |> select([a], %{
      "id" => a.id,
      "form_id" => a.id,
      "form_number" => a.form_number,
      "title" => a.name,
      "color" => a.color,
      "icon" => a.icon,
      "associated_license_id" => a.associated_license_id,
      "status" => a.status
    })
    |> limit(1)
    |> Repo.one()
  end

  def list_active_licenses_on_dashboard(category, type) do
    LicenseData
    |> join(:left, [a], b in assoc(a, :license))
    |> where(
      [a, b],
      b.status == ^"A" and b.require_license == false and b.categories_id == ^category and
        b.type in ^type
    )
    |> order_by([_, b], asc: b.form_number)
    |> select([_, b], %{
      "id" => b.id,
      "form_number" => b.form_number,
      "title" => b.name,
      "color" => b.color,
      "icon" => b.icon
    })
    |> distinct(true)
    |> Repo.all()
  end

  def list_active_update_licenses(type) do
    LicenseData
    |> join(:left, [a], b in assoc(a, :license))
    |> where([a, b], b.status == ^"A" and b.require_license == true and b.type in ^type)
    |> order_by([_, b], asc: b.form_number)
    |> select([_, b], %{
      "id" => b.id,
      "form_number" => b.form_number,
      "title" => b.name,
      "color" => b.color,
      "icon" => b.icon
    })
    |> distinct(true)
    |> Repo.all()
  end

  def list_active_license_categories_on_dashboard(type) do
    LicenseCategories
    |> where([a], a.status == ^1 and a.type in ^type)
    |> order_by([a], asc: a.id)
    |> select([a], %{
      "id" => a.id,
      "name" => a.name,
      "color" => a.color,
      "description" => a.description
      # "icon" => b.icon
    })
    |> distinct(true)
    |> Repo.all()
  end

  def list_active_license_categories do
    LicenseCategories
    |> where([a], a.status == ^1)
    |> order_by([a], asc: a.id)
    |> select([a], %{
      "id" => a.id,
      "name" => a.name,
      "color" => a.color,
      "description" => a.description
      # "icon" => b.icon
    })
    |> distinct(true)
    |> Repo.all()
  end

  @doc """
  Gets a single license.

  Raises `Ecto.NoResultsError` if the License does not exist.

  ## Examples

      iex> get_license!(123)
      %License{}

      iex> get_license!(456)
      ** (Ecto.NoResultsError)

  """
  def get_license!(id) do
    License
    |> where([a], a.id == ^id)
    |> limit(1)
    |> Repo.one()
  end

  def get_license_w_preload!(id) do
    License
    |> where([a], a.id == ^id)
    |> preload([:associated_license])
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Creates a license.

  ## Examples

      iex> create_license(%{field: value})
      {:ok, %License{}}

      iex> create_license(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """

  def query_user_with_expired_conditions(id) do
    # IO.inspect(LicenseConditionsMapping, label: "LicenseConditionsMapping in query_user_with
    LicenseConditionsMapping
    |> join(:left, [a], b in LicenseMapping, on: a.application_id == b.id)
    # |> join(:left, [a], b in assoc(a, :user))
    |> where(
      [a, b],
      a.expiring_status in ["EXPIRED", "SET", "EXPIRED_CONDITION"] and b.user_id == ^id
    )
    |> select([a, b], %{
      condition_mapping: a,
      user: b
    })
    |> limit(1)
    |> Repo.one()

    # |> Repo.all()
  end

  def query_license_mapping_by_id(id) do
    LicenseConditionsMapping
    |> join(:left, [a], b in User, on: a.added_by_id == b.id)
    |> join(:left, [a, b], c in LicenseMapping, on: b.id == c.license_id)
    |> where([a], a.application_id == ^id)
    |> select([a, b, c], %{
      condition: a,
      license: b,
      usermapping: c
    })
    |> limit(1)
    |> Repo.one()
  end

  def upate_license_expired_condition(params) do
    license_id = query_license_mapping_by_id(params["license_id"])

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :upate_expired_condition,
      LicenseConditionsMapping.changeset(
        license_id.condition,
        %{comments: params["condition_comments"]}
      )
    )
    |> Repo.transaction()
    |> case do
      {:ok, _result} ->
        {:ok, "Reason sent  successfully"}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, "Failed to update license condition: #{inspect(failed_value)}"}
    end
  end

  def create_license(socket, attrs \\ %{}) do
    params = attrs["license"]

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:create, License.changeset(%License{}, params))
    |> IO.inspect()
    |> Logs.Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.first_name} created license name: #{params["name"]}",
      "LICENSE MAINTENANCE CREATION",
      attrs,
      "Lincense Maintenance"
    )
    |> Repo.transaction()
  end

  def update_license(socket, license, attrs \\ %{}) do
    params = attrs["license"]

    Ecto.Multi.new()
    |> Ecto.Multi.update(:update, License.changeset(license, params))
    |> IO.inspect()
    |> Logs.Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.first_name} updated license details for the license name: #{license.name}",
      "LICENSE MAINTENANCE UPDATE",
      attrs,
      "Lincence maintenance"
    )
    |> Repo.transaction()
  end

  def change_license_status(socket, attrs, user) do
    new_status = if attrs["status"] == "D", do: "Disable", else: "Enable"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update_status",
      License.changeset(user, %{status: attrs["status"], reason: attrs["reason"]})
    )
    |> Logs.Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.first_name} #{new_status}d status for license name: #{user.name}",
      "LICENSE MAINTENANCE STATUS",
      attrs,
      "Lincence maintenance"
    )
    |> Repo.transaction()
  end

  def change_approved_license_status(socket, attrs, record) do
    new_status = if attrs["status"] == "false", do: "Revoke", else: "Activate"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update_status",
      LicenseMapping.revoke_update_changeset(record, %{
        revoked: attrs["status"],
        reason: attrs["reason"]
      })
    )
    |> Ecto.Multi.insert(
      "create_notification",
      fn %{"update_status" => license} ->
        UserNotifications.changeset(
          %UserNotifications{},
          %{
            "message" => "A Licence has been been #{new_status}d because: #{license.reason}",
            "page_url" => "/license/applications?id=#{license.id}",
            "type" => "LICENCES",
            "reason" => license.reason,
            "user_id" => license.user_id,
            "role_id" => 8
          }
        )
      end
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.first_name} #{new_status}d status for license name: #{record.record_name}",
      "LICENSE APPLICATION STATUS",
      attrs,
      "Lincence Applications"
    )
    |> Repo.transaction()
  end

  @doc """
  Updates a license.

  ## Examples

      iex> update_license(license, %{field: new_value})
      {:ok, %License{}}

      iex> update_license(license, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """

  # def update_license(%License{} = license, attrs) do
  #   license
  #   |> License.changeset(attrs)
  #   |> Repo.update()
  # end

  def delete_license(%License{} = license) do
    Repo.delete(license)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking license changes.

  ## Examples

      iex> change_license(license)
      %Ecto.Changeset{data: %License{}}

  """
  def change_license(%License{} = license, attrs \\ %{}) do
    License.changeset(license, attrs)
  end

  def change_license_fields(%LicenseFields{} = license_fields, attrs \\ %{}) do
    LicenseFields.changeset(license_fields, attrs)
  end

  def change_license_data(%LicenseData{} = license_data, attrs \\ %{}) do
    LicenseData.changeset(license_data, attrs)
  end

  def get_license_fields() do
    LicenseFields
    |> Repo.all()
  end

  def get_fields_by_name(field_name) do
    LicenseFields
    |> where([a], a.field_name == ^field_name)
    |> limit(1)
    |> Repo.one()
  end

  def create_license_fields(attrs \\ %{}) do
    %LicenseFields{}
    |> LicenseFields.changeset(attrs)
    |> Repo.insert!()
  end

  def checking_by_license_field_id(id, license_id) do
    LicenseData
    |> where([a], a.license_field_id in ^id and a.license_id == ^license_id)
    |> Repo.all()
  end

  def get_by_license_field_id_and_license_id(id, license_id) do
    LicenseData
    |> where([a], a.license_field_id == ^id and a.license_id == ^license_id)
    |> limit(1)
    |> Repo.one()
  end

  def get_by_license_field_id(id) do
    LicenseData
    |> where([a], a.license_field_id == ^id)
    |> limit(1)
    |> Repo.one()
  end

  def create_license_data(socket, attrs \\ %{}) do
    attrs["license_field_id"]
    |> Enum.reduce(Ecto.Multi.new(), fn license_field_id, multi ->
      Ecto.Multi.run(multi, "record#{license_field_id}", fn _repo, _ ->
        {
          :ok,
          get_by_license_field_id_and_license_id(license_field_id, attrs["license_id"]) ||
            %LicenseData{}
        }
      end)
      |> Ecto.Multi.insert_or_update("insert#{license_field_id}", fn changes ->
        LicenseData.changeset(changes["record#{license_field_id}"], %{
          "status" => 1,
          "license_id" => attrs["license_id"],
          "license_field_id" => license_field_id
        })
      end)
    end)
    |> Ecto.Multi.update_all(
      "remove",
      fn _changes ->
        from(
          a in LicenseData,
          where:
            a.license_id == ^attrs["license_id"] and
              a.license_field_id not in ^attrs["license_field_id"],
          update: [set: [status: 0]]
        )
      end,
      []
    )
    |> Logs.Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.first_name} created license",
      "LICENSE MAINTENANCE ",
      attrs,
      "license Maintenance"
    )
    |> Repo.transaction()
  end

  def remove_form(socket, attrs, field \\ %{}) do
    Ecto.Multi.new()
    |> Ecto.Multi.update_all(
      "change_status_summary",
      fn _changes ->
        from(
          a in SummarisedData,
          where: a.license_field_id == ^field,
          update: [set: [status: 0]]
        )
      end,
      []
    )
    |> Logs.Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.first_name} created license",
      "LICENSE MAINTENANCE ",
      attrs,
      "license Maintenance"
    )
    |> Repo.transaction()
  end

  def update_license_fields(%LicenseFields{} = license, attrs) do
    license
    |> LicenseFields.changeset(attrs)
    |> Repo.update!()
  end

  def get_licence_field_id() do
    # IO.inspect id, label: "idhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh"
    SummarisedData
    |> where([a], a.status == ^1)
    |> select([a], %{license_field_id: a.license_field_id})
    |> Repo.all()
    |> Enum.map(fn %{license_field_id: license_field_id} -> license_field_id end)
  end

  def get_licence_by_number(form_number) do
    License
    |> where([a], a.form_number == ^form_number)
    |> limit(1)
    |> Repo.one()
  end

  def seeds_create_licence(attrs \\ %{}) do
    %License{}
    |> License.changeset(attrs)
    |> Repo.insert()
  end

  def seeds_update_licence(%License{} = license, attrs) do
    license
    |> License.changeset(attrs)
    |> Repo.update()
  end

  def total_pending_applications() do
    LicenseMapping
    |> where(
      [a],
      a.approved == false and a.condition_tracking == false and a.approval_status == true and
        is_nil(a.associated_license_id)
    )
    |> Repo.aggregate(:count, :id)
  end

  def get_license_data(license_id) do
    LicenseData
    |> where([a], a.license_id == ^license_id)
    |> preload([:license_field, :license])
    |> Repo.all()
    |> Enum.map(& &1.license_field)
  end

  def get_incomplete_application(application_id) do
    LicenseMapping
    |> where([a], a.status == 0 and a.id == ^application_id)
    |> Repo.one()
  end

  def cancel_application(application_id) do
    case get_incomplete_application(application_id) do
      nil ->
        {:error, "No incomplete application found for the given user."}

      record ->
        Repo.delete(record)
    end
  end

  defp next_step(%{assigns: assigns} = socket, record) do
    Registration.get_license_steps!(record.license_id)
    |> Enum.drop_while(&(&1 <= assigns.current_position))
    |> List.first()
    |> case do
      nil -> assigns.current_position
      step -> step
    end
  end

  def create_registration(%{assigns: assigns} = socket, record, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert_or_update(
      "license_registration",
      LicenseMapping.insert_changeset(
        record || %LicenseMapping{},
        %{
          "data" => attrs,
          "current_step" => next_step(socket, record),
          "record_name" =>
            String.trim(Map.get(attrs, assigns.chosen_licence.primary_key) || "NOT PROVIDED"),
          "status" => 0,
          "license_id" => assigns.license_id,
          "user_id" => assigns.current_user.id
        }
      )
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{assigns.current_user.email} registered for a licence: #{assigns.chosen_licence.name}",
      "LICENSE REGISTRATION",
      attrs,
      "Lincence Registration"
    )
    |> Repo.transaction()
    |> case do
      {:ok, records} ->
        {:ok, records}

      {:error, error} ->
        {:error, error}

      {:error, _, error, _} ->
        {:error, error}
    end
  end

  def insert_or_update_registration(%{assigns: assigns} = socket, record, attrs) do
    # get_associate =
    #   if attrs["parent_id"] do
    #     get_application!(attrs["parent_id"])
    #   else
    #     get_application_by_name(String.trim(attrs[:principal_name] || "0"))
    #   end

    check_association = get_licenses_w_association!()

    multi =
      Ecto.Multi.new()
      |> Ecto.Multi.run("get_level", fn _, _ ->
        Utilities.get_current_approval_level!(1)
        |> case do
          nil -> {:error, "Something went wrong, please contact system Administrator"}
          data -> {:ok, data}
        end
      end)
      |> Ecto.Multi.insert_or_update(
        "license_registration",
        LicenseMapping.insert_changeset(
          record || %LicenseMapping{},
          %{
            "status" => 1,
            "current_step" => next_step(socket, record),
            "categories_id" => assigns.chosen_licence.categories_id,
            "count_down_days" => assigns.chosen_licence.count_down_days
            # "associated_license_id" => get_associate && get_associate.id
          }
        )
      )
      |> Audit.create_system_log_session_live_multi(
        socket,
        "User: #{assigns.current_user.email} registered for a licence: #{assigns.chosen_licence.name}",
        "LICENSE REGISTRATION",
        attrs,
        "License Registration"
      )

    if record.license_id not in check_association do
      account = Accounts.get_license_account_by_application_id(record.id)

      Ecto.Multi.insert_or_update(
        multi,
        "account",
        LicenseAccounts.changeset(account || %LicenseAccounts{}, %{
          "license_amount" =>
            Decimal.add(assigns.chosen_licence.amount, assigns.chosen_licence.other_fees),
          "other_fees" => assigns.chosen_licence.other_fees,
          "user_id" => assigns.current_user.id,
          "application_id" => record.id
        })
      )
    else
      multi
      # account = Accounts.get_license_account_by_application_id(get_associate.id)

      # total =
      #   Decimal.add(
      #     Decimal.add(account.license_amount, assigns.chosen_licence.amount),
      #     assigns.chosen_licence.other_fees
      #   )

      # Ecto.Multi.update(
      #   multi,
      #   "account",
      #   LicenseAccounts.changeset(account, %{
      #     "license_amount" => total,
      #     "other_fees" => assigns.chosen_licence.other_fees
      #   })
      # )
      # |> IO.inspect(label: :account)
    end
    |> Repo.transaction()
    |> IO.inspect(label: :repo)
    |> case do
      {:ok, records} ->
        {:ok, records}

      {:error, error} ->
        {:error, error}

      {:error, _, error, _} ->
        {:error, error}
    end
  end

  def update_license_correction(%{assigns: assigns} = socket, record, attrs) do
    rec = get_application!(record)

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "license_registration_correction",
      LicenseMapping.insert_changeset(rec, %{
        "status" => 1,
        "data" => attrs
        # "associated_license_id" => get_associate && get_associate.id
      })
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{assigns.current_user.email} made correction for a licence: #{assigns.chosen_licence.name}",
      "LICENSE REGISTRATION CORRECTION",
      attrs,
      "License Registration Correction"
    )
    |> Repo.transaction()
    |> Ecto.Multi.update(
      "license_registration_correction",
      LicenseMapping.insert_changeset(rec, %{"status" => 2})
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{assigns.current_user.email} made correction for a licence: #{assigns.chosen_licence.name}",
      "LICENSE REGISTRATION CORRECTION",
      attrs,
      "License Registration Correction"
    )
    |> Repo.transaction()
  end

  def get_summary_draft_user_mapping!(id) do
    LicenseMapping
    |> where([a], a.license_id == ^id)
    |> select([a], %{
      summary_user_draft: a.summary_user_draft,
      record_name: a.record_name
    })
    |> limit(1)
    |> Repo.one()
  end

  def process_license_update(application) do
    get_temp = App.SummaryDrafts.get_summary_draft!(application.license_id)

    temp = if !is_nil(get_temp), do: get_temp.template, else: nil

    multi =
      Ecto.Multi.new()
      |> Ecto.Multi.run("get_level", fn _, _ ->
        Utilities.get_current_approval_level!(application.status)
        |> case do
          nil -> {:error, "Something went wrong, please contact system Administrator"}
          data -> {:ok, data}
        end
      end)
      |> Ecto.Multi.update(
        "license",
        LicenseMapping.insert_changeset(
          application,
          %{
            "approval_status" => true,
            "summary_user_draft" => temp
          }
        )
      )
      |> Ecto.Multi.insert("create_logs", fn %{"license" => license} ->
        LicenseApprovalLogs.changeset(
          %LicenseApprovalLogs{},
          %{
            "status" => license.status,
            "is_approved" => false,
            "license_mapping_id" => license.id,
            "user_id" => license.user_id
          }
        )
      end)
      |> Ecto.Multi.insert("notification", fn
        %{
          "get_level" => get_level
        } ->
          UserNotifications.changeset(
            %UserNotifications{},
            %{
              "message" =>
                "A new Licence application has been created for #{application.license.name}",
              "page_url" => "/license/applications?id=#{application.id}",
              "type" => "LICENCE REGISTRATION",
              "role_id" => get_level.department_id
            }
          )
      end)

    if !is_nil(application.associated_license_id) do
      account = Accounts.get_license_account_by_application_id(application.associated_license_id)

      total =
        Decimal.add(
          Decimal.add(account.license_amount, application.license.amount),
          application.license.other_fees
        )

      Ecto.Multi.update(
        multi,
        "account",
        LicenseAccounts.changeset(account, %{
          "license_amount" => total,
          "other_fees" => Decimal.add(account.other_fees, application.license.other_fees)
        })
      )
      |> IO.inspect(label: :account)
    else
      multi
    end
    |> Repo.transaction()
    |> IO.inspect(label: :running)
    |> case do
      {:ok, record} ->
        Email.send_reg_notification(
          application.user.email,
          record["license"].record_name
        )

        {:ok, record}

      {:error, error} ->
        {:error, error}

      {:error, _, error, _} ->
        {:error, error}
    end
  end

  def get_licence_details!(id) do
    get_data = get_license_data(id)

    selected_fields =
      get_data
      |> Enum.map(& &1.field_name)
      |> Enum.map(&String.to_existing_atom/1)

    get_dyncamic_fields = get_dynamic_fields(LicenseMapping, selected_fields)

    {get_data, get_dyncamic_fields}
  end

  def get_dynamic_fields(schema, fields) do
    schema
    |> select([a], ^fields)
    |> limit(1)
    |> Repo.one()
  end

  def get_application!(id), do: Repo.get!(LicenseMapping, id)

  def get_application_w_preload(id) do
    LicenseMapping
    |> where([a], a.id == ^id)
    |> preload([:license, :user])
    |> limit(1)
    |> Repo.one()
  end

  def get_licence_mapping_by_id(id) do
    LicenseMapping
    |> where([a], a.id == ^id)
    |> preload([:license, :user])
    |> limit(1)
    |> Repo.one()
  end

  def get_application_by_user_id(id, license_id) do
    LicenseMapping
    |> where([a], a.user_id == ^id and a.license_id == ^license_id)
    |> limit(1)
    |> Repo.one()
  end

  def get_application_w_assoc(id) do
    record = get_application_w_preload(id)

    associated =
      LicenseMapping
      |> where([a], a.associated_license_id == ^record.id)
      |> preload([:license])
      |> Repo.all()

    {record, associated}
  end

  def get_application_w_assoc_representative(id) do
    record = get_application_w_preload(id)

    LicenseMapping
    |> where([a], a.associated_license_id == ^record.id)
    |> preload([:license])
    |> limit(1)
    |> Repo.one()
  end

  def license_id(id) do
    LicenseMapping
    |> where([a], a.license_id == ^id and a.status == ^3)
    |> preload([:license, :user])
    |> limit(1)
    |> Repo.one()
  end

  def licenses_id(id) do
    LicenseMapping
    |> where([a], a.license_id == ^id and a.status == ^9 and a.approved == true)
    |> preload([:license, :user])
    |> limit(1)
    |> Repo.one()
  end

  def update_application(record, socket, template) do
    # license_id = license_id(record)

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :updates,
      LicenseMapping.changeset_draft(socket.assigns.record, %{
        summary_user_draft: template,
        status: 3
      })
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} edited licence summary for : #{socket.assigns.record.record_name}",
      "LICENSE SUMMARY DRAFT",
      "license_id#{record}",
      "License summary draft"
    )
    |> Repo.transaction()
  end

  def update_application_replace(record, socket, value) do
    license_id = licenses_id(record)

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :updates,
      LicenseMapping.changeset_draft(license_id, %{
        is_replaced: true,
        replace_reason: value["replace_reason"],
        replace_comments: value["replace_comments"]
      })
    )
    # |> Audit.create_system_log_session_live_multi(
    #   socket,
    #   "User: #{socket.assigns.current_user.email} edited licence summary for : #{socket.assigns.record.record_name}",
    #   "LICENSE SUMMARY DRAFT",
    #   "license_id#{record}",
    #   "License summary draft"
    # )

    |> Repo.transaction()
  end

  def update_application_reprint(record, socket, value) do
    license_id = licenses_id(record)

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :updates,
      LicenseMapping.changeset_draft(license_id, %{
        to_reprint: true,
        reprint_reason: value["reprint_reason"],
        reprint_comments: value["reprint_comments"]
      })
    )
    # |> Audit.create_system_log_session_live_multi(
    #   socket,
    #   "User: #{socket.assigns.current_user.email} edited licence summary for : #{socket.assigns.record.record_name}",
    #   "LICENSE SUMMARY DRAFT",
    #   "license_id#{record}",
    #   "License summary draft"
    # )

    |> Repo.transaction()
  end

  def get_application_by_licence!(id) do
    LicenseMapping
    |> where([a], a.license_id == ^id)
    |> preload([:license])
    |> limit(1)
    |> Repo.one()
  end

  def get_application_by_name(name) do
    LicenseMapping
    |> where([a], a.record_name == ^name)
    |> limit(1)
    |> Repo.one()
  end

  def count_associated_applications(id, status) do
    LicenseMapping
    |> where(
      [a],
      a.associated_license_id == ^id and a.status == ^status
    )
    |> Repo.aggregate(:count, :id)
    |> IO.inspect(label: :assoc_count)
  end

  def count_associates_at_reg(id) do
    LicenseMapping
    |> where(
      [a],
      a.associated_license_id == ^id and a.status == ^1
    )
    |> Repo.aggregate(:count, :id)
  end

  def check_dependency(id, status) do
    LicenseMapping
    |> where(
      [a],
      a.id == ^id and a.status == ^status and a.approval_status == true
    )
    |> Repo.exists?()
  end

  def get_pending_licences!() do
    LicenseMapping
    |> where([a], a.status == 1 and a.approval_status == false)
    |> preload([:license, :user])
    |> Repo.all()
  end

  def get_licenses_w_association!() do
    License
    |> where([a], a.status == "A" and not is_nil(a.associated_license_id))
    |> select([a], a.associated_license_id)
    |> Repo.all()
  end

  def get_pending_licenses_without_assoc!() do
    LicenseMapping
    |> where(
      [a],
      a.status == 1 and a.approval_status == false and is_nil(a.associated_license_id)
    )
    |> select([a], a.id)
    |> Repo.all()
  end

  #  def change_user_mapping(input_fields, record, attrs \\ %{}) do
  #    (record || %LicenseMapping{})
  #    |> LicenseMapping.changeset(attrs, input_fields)
  #  end
  #  def change_user_mapping(input_fields, record, attrs \\ %{}) do
  #    (record || %LicenseMapping{})
  #    |> LicenseMapping.changeset(attrs, input_fields)
  #  end

  def get_license_mapping_by_license_id_and_user_id(license_id, user_id) do
    LicenseMapping
    |> where([a], a.license_id == ^license_id and a.user_id == ^user_id)
    |> preload([:license])
    |> limit(1)
    |> Repo.one()
  end

  def get_associated_licenses!(id) do
    LicenseMapping
    |> where([a], a.associated_license_id == ^id)
    |> preload([:license, :user])
    |> Repo.all()
  end

  def approve_application(%{assigns: assigns} = socket, attrs, record) do
    get_level = Utilities.get_current_approval_level!(attrs["status"])
    current_approval_level = Utilities.get_current_approval_level!(record.status)

    has_conditions = App.LicenseConditions.license_has_conditions(record.id)

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "approval",
      LicenseMapping.insert_changeset(record, %{
        "status" => attrs["status"],
        "count_down_start_date" =>
          if(current_approval_level.count_down == 1,
            do: Date.utc_today(),
            else: record.count_down_start_date
          ),
        "condition_tracking" =>
          if(current_approval_level.condition_tracking == 1 && has_conditions,
            do: true,
            else: record.condition_tracking
          ),
        "condition_status" =>
          if(current_approval_level.condition_tracking == 1,
            do: 1,
            else: record.condition_status
          ),
        "show_summary" =>
          if(current_approval_level.genarate_summary == 1, do: true, else: record.show_summary)
      })
    )
    |> Ecto.Multi.update_all(
      "associated",
      fn %{"approval" => license} ->
        from(
          a in LicenseMapping,
          where: a.associated_license_id == ^license.id,
          update: [
            set:
              ^[
                status: attrs["status"],
                condition_status:
                  if(current_approval_level.condition_tracking == 1,
                    do: 1,
                    else: record.condition_status
                  ),
                condition_tracking:
                  if(current_approval_level.condition_tracking == 1 && has_conditions,
                    do: true,
                    else: record.condition_tracking
                  ),
                show_summary:
                  if(current_approval_level.genarate_summary == 1,
                    do: true,
                    else: record.show_summary
                  ),
                count_down_start_date:
                  if(current_approval_level.count_down == 1,
                    do: Date.utc_today(),
                    else: record.count_down_start_date
                  )
              ]
          ]
        )
      end,
      []
    )
    |> Ecto.Multi.insert(
      "comments",
      Comments.changeset(
        %Comments{},
        %{
          "comment" => attrs["comments"],
          "level" => record.status,
          "user_id" => assigns.current_user.id,
          "application_id" => record.id
        }
      )
    )
    |> Ecto.Multi.insert(
      "create_logs",
      fn %{"approval" => license} ->
        LicenseApprovalLogs.changeset(
          %LicenseApprovalLogs{},
          %{
            "status" => record.status,
            "is_approved" => true,
            "license_mapping_id" => license.id,
            "user_id" => assigns.current_user.id
          }
        )
      end
    )
    |> Ecto.Multi.insert(
      "create_notification",
      fn %{"approval" => license} ->
        UserNotifications.changeset(
          %UserNotifications{},
          %{
            "message" => "A Licence Application has been sent for approval",
            "page_url" => "/license/applications?id=#{license.id}",
            "type" => "LICENCE APPROVAL",
            "role_id" => get_level.department_id
          }
        )
      end
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} approved an Application for: #{record.record_name}",
      "APPROVALS",
      attrs,
      "Licence Applications"
    )
    |> Repo.transaction()
    |> case do
      {:ok, records} ->
        Task.start(fn ->
          nil
          # send email notification to the user
          # Email.send_email_notification(record.user.email, "Your License for: #{record.record_name} has been approved at #{NumberF.ordinal(get_level.approval_status)} level")

          # send department email
          Accounts.get_department_users(get_level.department_id)
          |> Enum.reduce(fn user ->
            nil

            # Email.send_email_notification(user.email, "A Licence Application has been sent for approval")
          end)
        end)

        {:ok, records}

      {:error, error} ->
        {:error, error}

      {:error, _, error, _} ->
        {:error, error}
    end
  end

  def update_conditional_status(socket, attrs, record) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update_condition",
      LicenseMapping.insert_changeset(record, %{
        "condition_status" => attrs["condition_status"]
      })
    )
    |> Ecto.Multi.update_all(
      "updated_associated",
      fn %{"update_condition" => license} ->
        from(
          a in LicenseMapping,
          where: a.associated_license_id == ^license.id,
          update: [
            set:
              ^[
                condition_status: license.condition_status
              ]
          ]
        )
      end,
      []
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} approved an Application for: #{record.record_name}",
      "CONDITIONAL APPROVALS",
      attrs,
      "Conditional Approvals"
    )
    |> Repo.transaction()
  end

  def final_approval(%{assigns: assigns} = socket, attrs, record, associates) do
    user = Accounts.get_user!(record.user_id)

    multi =
      Ecto.Multi.new()
      |> Ecto.Multi.update(
        "approval",
        LicenseMapping.insert_changeset(record, %{
          "status" => attrs["status"],
          "approved" => true
        })
      )
      |> issued_insert(record)
      |> run_user_updates(record, user)
      |> Ecto.Multi.insert(
        "create_logs",
        fn %{"approval" => license} ->
          LicenseApprovalLogs.changeset(
            %LicenseApprovalLogs{},
            %{
              "status" => record.status,
              "is_approved" => true,
              "license_mapping_id" => license.id,
              "user_id" => assigns.current_user.id
            }
          )
        end
      )
      |> Ecto.Multi.insert(
        "comments",
        Comments.changeset(
          %Comments{},
          %{
            "comment" => attrs["comments"],
            "level" => record.status,
            "user_id" => assigns.current_user.id,
            "application_id" => record.id
          }
        )
      )
      |> Audit.create_system_log_session_live_multi(
        socket,
        "User: #{socket.assigns.current_user.email} approved an Application for: #{record.record_name}",
        "APPROVALS",
        attrs,
        "Licence Applications"
      )

    associates
    |> Enum.reduce(multi, fn associated, multi ->
      associate_user = Accounts.get_user!(associated.user_id)

      multi
      |> Ecto.Multi.update(
        "update_associations#{associated.id}",
        LicenseMapping.insert_changeset(associated, %{
          "status" => attrs["status"],
          "meeting_number" => attrs["meeting_number"],
          "approved" => true
        })
      )
      |> issued_insert(associated)
      |> run_user_updates(associated, associate_user)
    end)
    |> Repo.transaction()
    |> case do
      {:ok, records} ->
        Task.start(fn ->
          nil
          # send email notification to the user
          # Email.send_email_notification(record.user.email, "Your License for: #{record.record_name} has been approved at #{NumberF.ordinal(get_level.approval_status)} level")
        end)

        {:ok, records}

      {:error, error} ->
        {:error, error}

      {:error, _, error, _} ->
        {:error, error}
    end
  end

  defp issued_insert(multi, record) do
    multi
    |> Ecto.Multi.insert(
      "issued_license#{record.id}",
      IssuedLicense.changeset(
        %IssuedLicense{},
        %{
          application_id: record.id,
          holder_id: record.user_id,
          certificate_id: record.license.certificate_id,
          license_id: record.license_id,
          expiring_date: Date.add(Date.utc_today(), 365)
        }
      )
    )
  end

  defp run_user_updates(multi, record, user) do
    multi
    |> Ecto.Multi.update(
      "assign_license#{record.id}",
      User.assign_changeset(user, %{
        "role_id" => record.license.role_id || user.role_id
      })
    )
    |> Ecto.Multi.insert(
      "notify_user#{record.id}",
      fn %{"approval" => license} ->
        UserNotifications.changeset(
          %UserNotifications{},
          %{
            "message" => "Your Licence Application has been approved",
            "page_url" => "/client/applications/#{"completed"}",
            "type" => "LICENCE APPROVAL",
            "user_id" => record.user_id,
            "role_id" => 8
          }
        )
      end
    )
  end

  def decline_application(%{assigns: assigns} = socket, attrs, record, associates) do
    multi =
      Ecto.Multi.new()
      |> Ecto.Multi.update(
        "approval",
        LicenseMapping.insert_changeset(record, %{
          "status" => attrs["status"],
          "reason" => attrs["reason"]
        })
      )
      |> run_decline_updates(record, attrs)
      |> Ecto.Multi.insert(
        "create_logs",
        fn %{"approval" => license} ->
          LicenseApprovalLogs.changeset(
            %LicenseApprovalLogs{},
            %{
              "status" => record.status,
              "is_approved" => false,
              "license_mapping_id" => license.id,
              "user_id" => assigns.current_user.id
            }
          )
        end
      )
      |> Ecto.Multi.insert(
        "comments",
        Comments.changeset(
          %Comments{},
          %{
            "comment" => attrs["reason"],
            "level" => record.status,
            "user_id" => assigns.current_user.id,
            "application_id" => record.id
          }
        )
      )
      |> Audit.create_system_log_session_live_multi(
        socket,
        "User: #{socket.assigns.current_user.email} declined an Application for: #{record.record_name}",
        "APPROVALS",
        attrs,
        "Licence Applications"
      )

    associates
    |> Enum.reduce(multi, fn associated, multi ->
      multi
      |> Ecto.Multi.update(
        "update_associations#{associated.id}",
        LicenseMapping.insert_changeset(associated, %{
          "status" => attrs["status"],
          "reason" => attrs["reason"]
        })
      )
      |> run_decline_updates(associated, attrs)
    end)
    |> Repo.transaction()
    |> case do
      {:ok, records} ->
        # Decline Email Here

        Task.start(fn ->
          nil
          # send email notification to the user
          # Email.send_email_notification(record.user.email, "Your License for: #{record.record_name} has been approved at #{NumberF.ordinal(get_level.approval_status)} level")
        end)

        {:ok, records}

      {:error, error} ->
        {:error, error}

      {:error, _, error, _} ->
        {:error, error}
    end
  end

  defp run_decline_updates(multi, record, attrs) do
    message = App.LicenseReviews.query_field_label_for_review(record.id)

    new_message =
      if message != [] do
        ", please review fields: " <>
          Enum.map_join(message, ", ", fn x -> "#{x.attention_field}: #{x.reason}" end)
      else
        ""
      end

    if attrs["status"] == "-1" do
      multi
      |> Ecto.Multi.insert(
        "create_notification#{record.id}",
        fn %{"approval" => license} ->
          UserNotifications.changeset(
            %UserNotifications{},
            %{
              "message" =>
                "A Licence Application has been declined because: #{license.reason}#{new_message}",
              "page_url" => "/registration/#{record.license_id}",
              "type" => "LICENCE DECLINED",
              "reason" => license.reason,
              "user_id" => record.user_id,
              "role_id" => 8
            }
          )
        end
      )
    else
      get_level = Utilities.get_current_approval_level!(attrs["status"])

      multi
      |> Ecto.Multi.insert(
        "create_notification#{record.id}",
        fn %{"approval" => license} ->
          UserNotifications.changeset(
            %UserNotifications{},
            %{
              "message" => "A Licence Application has been declined because: #{license.reason}",
              "page_url" => "/license/applications?id=#{license.id}",
              "type" => "LICENCE DECLINED",
              "reason" => license.reason,
              "role_id" => get_level.department_id
            }
          )
        end
      )
    end
  end

  def perfom_debit(%{assigns: assigns} = socket, attrs, record, associates) do
    account = Accounts.get_license_account_by_application_id(record.id)
    amount_paid = Decimal.add(Decimal.new(attrs["amount_paid"]), account.amount_paid)

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "debit_account",
      account
      |> LicenseAccounts.debit_account_changeset(%{new_balance: amount_paid})
    )
    |> Ecto.Multi.update(
      "update_account",
      LicenseAccounts.changeset(account, %{
        "amount_paid" => amount_paid,
        "verified" => false,
        "declined" => false
      })
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{assigns.current_user.email} validated amounts for: #{record.record_name}'s application",
      "FINANCE VALIDATION",
      attrs,
      "Licence Applications"
    )
    |> Repo.transaction()
  end

  def validate_amount(account, amount) do
    case Decimal.compare(Decimal.new(amount), account.license_amount) do
      :eq -> {:ok, "Amount Validated"}
      :lt -> {:error, "Amount is less than the required License amount"}
      :gt -> {:error, "Amount is greater than the required License amount"}
    end
  end

  @doc """
  Creates a changeset for user license mapping.
  """
  def change_user_mapping(fields, record, params \\ %{}) do
    # Create schema with atom keys for the fields
    schema =
      fields
      |> Enum.map(&{&1, :string})
      |> Enum.into(%{})

    {record || %{}, schema}
    |> Ecto.Changeset.cast(params, fields)
    |> Ecto.Changeset.validate_required(fields)
  end

  @doc """
  Creates a changeset for upload fields.
  """
  def change_upload(fields, params \\ %{}) do
    # Create schema with atom keys for the fields
    schema =
      fields
      |> Enum.map(&{&1, :string})
      |> Enum.into(%{})

    {%{}, schema}
    |> Ecto.Changeset.cast(params, fields)
    |> Ecto.Changeset.validate_required(fields)
  end

  ## License Categories

  def get_license_category!(id), do: Repo.get!(LicenseCategories, id)

  def create_license_category(%{assigns: assigns} = socket, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      "create",
      LicenseCategories.changeset(
        %LicenseCategories{},
        attrs
      )
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{assigns.current_user.email} created a license category: #{attrs["name"]}",
      "APPLICATION CATEGORY STATUS",
      attrs,
      "Application Category"
    )
    |> Repo.transaction()
  end

  def update_license_category(socket, %LicenseCategories{} = record, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update_record",
      LicenseCategories.changeset(record, attrs)
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} updated an error code: #{attrs["code"]}",
      "APPLICATION CATEGORY STATUS",
      attrs,
      "Application Category"
    )
    |> Repo.transaction()
  end

  def change_category_status(socket, attrs, record) do
    new_status = if attrs["status"] == "0", do: "Disable", else: "Enable"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      "update_status",
      LicenseCategories.changeset(record, %{status: attrs["status"]})
    )
    |> Audit.create_system_log_session_live_multi(
      socket,
      "User: #{socket.assigns.current_user.email} #{new_status}d status for: #{record.name}",
      "APPLICATION CATEGORY STATUS",
      attrs,
      "Application Category"
    )
    |> Repo.transaction()
  end

  def change_license_category(%LicenseCategories{} = category, attrs \\ %{}) do
    LicenseCategories.changeset(category, attrs)
  end

  def count_user_applications_by_status(user_id, status) do
    case status do
      "draft" ->
        LicenseMapping
        |> where([a], a.user_id == ^user_id and a.status == 0)
        |> Repo.aggregate(:count, :id)

      "submitted" ->
        LicenseMapping
        |> where([a], a.user_id == ^user_id and a.status > 0 and a.approved == false)
        |> Repo.aggregate(:count, :id)

      "approved" ->
        LicenseMapping
        |> where([a], a.user_id == ^user_id and a.approved == true)
        |> Repo.aggregate(:count, :id)

      "declined" ->
        LicenseMapping
        |> where([a], a.user_id == ^user_id and a.status == -1)
        |> Repo.aggregate(:count, :id)

      _ ->
        0
    end
  end

  def count_applications_by_status(status) do
    case status do
      "all" ->
        LicenseMapping
        |> where([a], a.approval_status == true)
        |> Repo.aggregate(:count, :id)

      "new" ->
        LicenseMapping
        |> where([a], a.status == 1 and a.approval_status == true)
        |> Repo.aggregate(:count, :id)

      "returned" ->
        LicenseMapping
        |> where([a], a.status == -1 and a.approval_status == true)
        |> Repo.aggregate(:count, :id)

      "supervisor" ->
        LicenseMapping
        |> where(
          [a],
          a.status in [2, 3, 4, 5, 6, 7] and a.approval_status == true and a.approved == false
        )
        |> Repo.aggregate(:count, :id)

      _ ->
        0
    end
  end
end
