defmodule App.Service.Export.CsvMonthlyStatisticalService do
  @moduledoc false
  alias App.Service.Table.PeriodicReports
  alias App.Service.Export.Functions

  @headers [
    "DATE",
    "SENT",
    "DELIVERED",
    "INVALID",
    "FAILED",
    "TOTAL"
  ]

  def index(socket, payload) do
    PeriodicReports.export(socket, payload)
    |> Stream.map(
      &[
        &1.date,
        &1.sent,
        &1.delivered,
        &1.failed,
        &1.invalid,
        &1.total
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["Periodic Reports"],
              ["", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
