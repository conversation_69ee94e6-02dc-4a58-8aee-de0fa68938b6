<div class="p-6">
  <div class="sm:flex sm:items-center mb-10">
    <div class="sm:flex-auto">
      <h1 class="text-2xl font-bold text-gray-800 mb-6">Application Categories</h1>
    </div>

    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none" phx-update="ignore" id="time2">
      <span phx-hook="LocalTime" id="time" class="text-lg"></span>
    </div>
  </div>

  <%= if Enum.empty?(@categories) do %>
    <div class="text-center py-12">
      <p class="text-gray-500">No application categories available.</p>
    </div>
  <% else %>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
      <%= for category <- @categories do %>
        <div
          class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform"
          style={"border-top: 4px solid #{category["color"]}"}
        >
          <div class="relative p-6">
            <div
              class="absolute top-0 right-0 w-24 h-24 opacity-10"
              style={"background: radial-gradient(circle at top right, #{category["color"]}, transparent 70%)"}
            >
            </div>

            <div class="flex items-start justify-between">
              <div class="mr-4 flex-1">
                <h3 class="text-xl font-bold text-gray-900 mb-2"><%= category["name"] %></h3>

                <%= if Map.has_key?(category, "description") do %>
                  <p class="text-gray-600 text-sm line-clamp-2"><%= category["description"] %></p>
                <% else %>
                  <p class="text-gray-500 text-sm">Explore this category for more information.</p>
                <% end %>
              </div>

              <%!-- <div class="flex-shrink-0">
                <span
                  class="inline-block px-3 py-1 text-xs font-semibold rounded-full text-white mb-3"
                  style={"background-color: #{category["color"]}"}
                >
                  Category <%= category["id"] %>
                </span>
              </div> --%>
            </div>

            <div class="mt-5 flex justify-between items-center">
              <.link navigate={~p"/categories/#{category["name"]}"}>
                <button
                  phx-value-id={category["id"]}
                  class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-300 flex items-center gap-2 shadow-sm"
                >
                  Apply
                </button>
              </.link>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% end %>
</div>
