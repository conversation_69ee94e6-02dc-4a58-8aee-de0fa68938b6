defmodule App.Service.Export.DepartmentRolesManagementServicePdf do
  @moduledoc false

  alias App.Service.Table.DepartmentRoles

  alias App.Service.Export.{
    Functions
  }

  def index(payload) do
    results =
      DepartmentRoles.export(payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "inserted_at" => Calendar.strftime(data.inserted_at, "%d/%m/%Y"),
          "name" => data.name,
          "description" => data.description,
          "access_roles" => data.access_roles,
          "status" => Functions.department_roles_management_status(data.status)
        }
      end)
      |> Enum.map(fn data ->
        """
         <tr>
            <td style="text-align: center;">{{ inserted_at }}</td>
            <td style="text-align: center;">{{ name }}</td>
            <td style="text-align: center;">{{ description }}</td>
            <td style="text-align: center;">{{ access_roles }}</td>
            <td style="text-align: center;">{{ status }}</td>

         </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/department_roles_management_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Department Roles Management",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end
