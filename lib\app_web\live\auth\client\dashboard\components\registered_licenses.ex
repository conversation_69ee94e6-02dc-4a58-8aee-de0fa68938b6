defmodule AppWeb.Dashboard.RegisteredLicenses do
  use AppWeb, :live_view
  on_mount({AppWeb.UserAuth, :mount_current_user})
  on_mount({AppWeb.UserAuth, :ensure_authenticated})
  alias App.Licenses

  @impl true
  def render(assigns) do
    ~H"""
    <div
      class="p-6"
      x-data="{ show: false }"
      x-init="setTimeout(() => show = true, 100)"
      x-show="show"
      x-transition:enter="transition ease-out duration-500"
      x-transition:enter-start="opacity-0 transform -translate-y-4"
      x-transition:enter-end="opacity-100 transform translate-y-0"
    >
      <div
        class="sm:flex sm:items-center mb-10"
        x-data="{ show: false }"
        x-init="setTimeout(() => show = true, 200)"
        x-show="show"
        x-transition:enter="transition ease-out duration-500"
        x-transition:enter-start="opacity-0 transform -translate-x-4"
        x-transition:enter-end="opacity-100 transform translate-x-0"
      >
        <div class="sm:flex-auto">
          <h1 class="text-2xl font-bold text-gray-800 mb-6">Securities Licenses & Forms</h1>
        </div>

        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none" phx-update="ignore" id="time2">
          <span phx-hook="LocalTime" id="time" class="text-lg"></span>
        </div>
      </div>

      <%= if Enum.empty?(@licenses) do %>
        <.empty_state />
      <% else %>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <%= for {license, idx} <- Enum.with_index(@licenses) do %>
            <div
              x-data="{ show: false }"
              x-init={"setTimeout(() => show = true, #{100 * idx})"}
              x-show="show"
              x-transition:enter="transition ease-out duration-500"
              x-transition:enter-start="opacity-0 transform scale-95"
              x-transition:enter-end="opacity-100 transform scale-100"
              class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
              style={"border-top: 4px solid #{license["color"]}"}
            >
              <div class="p-6">
                <div class="flex items-start justify-between">
                  <div class="mr-4">
                    <span class="inline-block px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                      Form <%= license["form_number"] %>
                    </span>

                    <h3 class="mt-2 text-lg font-medium text-gray-900"><%= license["title"] %></h3>

                    <%= if license["count_down_start_date"] do %>
                      <% days_left =
                        App.Util.CustomTime.days_left(
                          license["count_down_start_date"],
                          license["count_down_days"]
                        ) %>
                      <p class="text-sm text-gray-500 mt-1">
                        <%= if days_left == 0 do %>
                          Application deadline has passed.
                        <% else %>
                          <%= days_left %> day<%= if days_left != 1, do: "s" %> left to complete application.
                        <% end %>
                      </p>
                    <% end %>
                  </div>

                  <div class="flex-shrink-0">
                    <div
                      class="w-10 h-10 rounded-full flex items-center justify-center"
                      style={"background-color: #{license["color"]}"}
                    >
                      <.licence_icon name={license["icon"]} class="h-5 w-5 text-white" />
                    </div>
                  </div>
                </div>

                <div class="mt-4 flex justify-between items-center">
                  <%= if license["status"] == 0 do %>
                    <button
                      type="button"
                      phx-click="apply"
                      phx-value-form={license["form_id"]}
                      phx-value-parent_id={license["parent_id"]}
                      class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors duration-300"
                    >
                      Continue Application
                    </button>
                  <% end %>

                  <%= if license["status"] == -1 do %>
                    <button
                      type="button"
                      phx-click="apply"
                      phx-value-form={license["form_id"]}
                      phx-value-parent_id={license["parent_id"]}
                      class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors duration-300"
                    >
                      Amend Details
                    </button>
                  <% end %>

                  <%= if license["status"] >= 1 do %>
                    <Table.client_default_status
                      status={license["status"]}
                      approval_status={license["approval_status"]}
                    />
                    <%= if !is_nil(license["data"]["representative_number"]) do %>
                      <span class="text-sm text-gray-500">
                        Number of Representatives: <%= license["data"]["representative_number"] %>
                      </span>
                    <% end %>
                  <% end %>

                  <%= if license["status"] == "A" do %>
                    <button
                      phx-click="apply"
                      phx-value-form={license["form_id"]}
                      phx-value-parent_id={license["parent_id"]}
                      class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors duration-300"
                    >
                      Apply
                    </button>
                  <% end %>

                  <%= if license["status"] == 0 do %>
                    <button
                      type="button"
                      phx-click="cancel_application"
                      phx-value-id={license["id"]}
                      phx-value-parent_id={license["associated_license_id"]}
                      class="px-4 py-2 bg-rose-600 text-white text-sm font-medium rounded-md hover:bg-rose-700 transition-colors duration-300"
                    >
                      Cancel Application
                    </button>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>

    <Model.confirmation_model
      :if={@live_action == :confirm}
      show
      id="confirmation_model-modal"
      model_title={@confirmation_model_title}
      body_text={@confirmation_model_text}
      agree_function={@confirmation_model_agree}
      reject_function={@confirmation_model_reject}
      params={Jason.encode!(@confirmation_model_params)}
      icon={@confirmation_model_icon}
    />
    """
  end

  @impl true
  def mount(_params, session, socket) do
    licenses = Licenses.list_active_licenses_by_user_id(socket.assigns.current_user.id)

    IO.inspect(Enum.map(licenses, &%{"p" => &1["parent_id"], "l" => &1["form_id"]}), label: :lic)

    {
      :ok,
      assign(socket, licenses: licenses)
      |> assign(confirmation_model: false)
      |> assign(confirmation_model_title: "Are you sure?")
      |> assign(confirmation_model_text: "")
      |> assign(confirmation_model_agree: "")
      |> assign(confirmation_model_reject: "close_confirmation_model")
      |> assign(confirmation_model_params: "")
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> assign(session_id: session["live_socket_id"])
      |> assign(live_socket_identifier: session["live_socket_id"])
    }
  end

  @impl true
  def handle_event("apply", %{"form" => form_number, "parent_id" => parent_id}, socket) do
    {:noreply, push_navigate(socket, to: ~p"/license/registration/#{form_number}/#{parent_id}")}
  end

  @impl true
  def handle_event("apply", %{"form" => form_number}, socket) do
    {:noreply, push_navigate(socket, to: ~p"/license/registration/#{form_number}")}
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  def handle_event_switch(target, value, %{assigns: _assigns} = socket) do
    case target do
      "cancel_application" ->
        {
          :noreply,
          assign(socket, :live_action, :confirm)
          |> assign(
            :confirmation_model_text,
            "Are you sure you want to cancel this application?"
          )
          |> assign(:confirmation_model_params, Map.merge(value, %{"action" => "cancel"}))
          |> assign(:confirmation_model_agree, "process_cancel")
        }

      "close_confirmation_model" ->
        {
          :noreply,
          assign(socket, :live_action, :index)
        }

      "process_cancel" ->
        cancel_application(Jason.decode!(value["params"]), socket)
    end
  end

  def cancel_application(value, socket) do
    Licenses.cancel_application(value["id"])
    |> case do
      {:ok, _message} ->
        success_message(socket, "Application Cancelled Successfully.")

      {:error, message} ->
        error_message(socket, message)
    end
  end

  defp success_message(%{assigns: _assigns} = socket, message) do
    {
      :noreply,
      redirect(socket, to: ~p"/categories/Licenses")
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "success"
      )
    }
  end

  defp error_message(%{assigns: _assigns} = socket, message) do
    {
      :noreply,
      socket
      |> assign(:live_action, :index)
      |> assign(confirmation_model_icon: "exclamation_circle")
      |> LiveFunctions.sweet_alert(
        message,
        "error"
      )
    }
  end
end
