defmodule App.SetUp.AdminContacts do
  @moduledoc false
  alias App.Users

  def init do
    [
      %{
        "name" => "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "email" => "chiyembe<PERSON><EMAIL>",
        "mobile" => "260978921730"
      }
    ]
    |> Enum.each(fn data ->
      if user = Users.get_admin_contact_by_name(data["name"]) do
        Users.update_admin_contacts(user, data)
      else
        Users.create_admin_contacts(data)
      end
    end)
  end
end
