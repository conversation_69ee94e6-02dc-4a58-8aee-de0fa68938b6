defmodule App.TransactionTest do
  use App.DataCase

  alias App.Transaction

  describe "transactions" do
    alias App.Transaction.Transactions

    import App.TransactionFixtures

    @invalid_attrs %{
      status: nil,
      timestamp: nil,
      reference: nil,
      provider: nil,
      txn_date: nil,
      value_date: nil,
      face_value: nil,
      merchant_value: nil,
      commission: nil,
      narration: nil,
      merchant_reference: nil,
      provider_reference: nil
    }

    test "list_transactions/0 returns all transactions" do
      transactions = transactions_fixture()
      assert Transaction.list_transactions() == [transactions]
    end

    test "get_transactions!/1 returns the transactions with given id" do
      transactions = transactions_fixture()
      assert Transaction.get_transactions!(transactions.id) == transactions
    end

    test "create_transactions/1 with valid data creates a transactions" do
      valid_attrs = %{
        status: 42,
        timestamp: "some timestamp",
        reference: "some reference",
        provider: "some provider",
        txn_date: ~D[2024-09-25],
        value_date: "some value_date",
        face_value: 42,
        merchant_value: "some merchant_value",
        commission: "some commission",
        narration: "some narration",
        merchant_reference: "some merchant_reference",
        provider_reference: "some provider_reference"
      }

      assert {:ok, %Transactions{} = transactions} = Transaction.create_transactions(valid_attrs)
      assert transactions.status == 42
      assert transactions.timestamp == "some timestamp"
      assert transactions.reference == "some reference"
      assert transactions.provider == "some provider"
      assert transactions.txn_date == ~D[2024-09-25]
      assert transactions.value_date == "some value_date"
      assert transactions.face_value == 42
      assert transactions.merchant_value == "some merchant_value"
      assert transactions.commission == "some commission"
      assert transactions.narration == "some narration"
      assert transactions.merchant_reference == "some merchant_reference"
      assert transactions.provider_reference == "some provider_reference"
    end

    test "create_transactions/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Transaction.create_transactions(@invalid_attrs)
    end

    test "update_transactions/2 with valid data updates the transactions" do
      transactions = transactions_fixture()

      update_attrs = %{
        status: 43,
        timestamp: "some updated timestamp",
        reference: "some updated reference",
        provider: "some updated provider",
        txn_date: ~D[2024-09-26],
        value_date: "some updated value_date",
        face_value: 43,
        merchant_value: "some updated merchant_value",
        commission: "some updated commission",
        narration: "some updated narration",
        merchant_reference: "some updated merchant_reference",
        provider_reference: "some updated provider_reference"
      }

      assert {:ok, %Transactions{} = transactions} =
               Transaction.update_transactions(transactions, update_attrs)

      assert transactions.status == 43
      assert transactions.timestamp == "some updated timestamp"
      assert transactions.reference == "some updated reference"
      assert transactions.provider == "some updated provider"
      assert transactions.txn_date == ~D[2024-09-26]
      assert transactions.value_date == "some updated value_date"
      assert transactions.face_value == 43
      assert transactions.merchant_value == "some updated merchant_value"
      assert transactions.commission == "some updated commission"
      assert transactions.narration == "some updated narration"
      assert transactions.merchant_reference == "some updated merchant_reference"
      assert transactions.provider_reference == "some updated provider_reference"
    end

    test "update_transactions/2 with invalid data returns error changeset" do
      transactions = transactions_fixture()

      assert {:error, %Ecto.Changeset{}} =
               Transaction.update_transactions(transactions, @invalid_attrs)

      assert transactions == Transaction.get_transactions!(transactions.id)
    end

    test "delete_transactions/1 deletes the transactions" do
      transactions = transactions_fixture()
      assert {:ok, %Transactions{}} = Transaction.delete_transactions(transactions)
      assert_raise Ecto.NoResultsError, fn -> Transaction.get_transactions!(transactions.id) end
    end

    test "change_transactions/1 returns a transactions changeset" do
      transactions = transactions_fixture()
      assert %Ecto.Changeset{} = Transaction.change_transactions(transactions)
    end
  end
end
