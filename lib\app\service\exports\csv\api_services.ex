defmodule App.Service.Export.CsvApiServicesService do
  @moduledoc false
  alias App.Service.Table.ApiServices
  alias App.Service.Export.Functions

  @headers [
    "NAME",
    "REGEX",
    "TYPE",
    "STATUS"
  ]

  def index(assigns, payload) do
    ApiServices.export(assigns, payload)
    |> Stream.map(
      &[
        &1.name,
        &1.regex,
        &1.type,
        Functions.table_numeric_status(&1.status)
      ]
    )
    |> (fn stream ->
          Stream.concat(
            [
              ["API Services"],
              ["", "", "", "", "", "", ""],
              @headers,
              ["", "", "", "", "", "", ""]
            ],
            stream
          )
        end).()
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string
  end
end
