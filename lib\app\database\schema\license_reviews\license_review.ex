defmodule App.LicenseReviews.LicenseReview do
  use Ecto.Schema
  import Ecto.Changeset

  schema "license_reviews" do
    field :attention_field, :string
    field :field_id, :integer
    field :license_id, :integer
    field :associated_id, :integer
    field :attention_status, :string
    field :reason, :string
    field :user_license_mapping_id, :integer

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(license_review, attrs) do
    license_review
    |> cast(attrs, [
      :reason,
      :associated_id,
      :field_id,
      :license_id,
      :attention_status,
      :attention_field,
      :user_license_mapping_id
    ])
    |> validate_required([:field_id, :license_id, :user_license_mapping_id])
  end
end
