defmodule App.Service.Export.PdfAnnualStaticsticsService do
  alias App.Service.Table.YearlyReports

  alias App.Service.Export.{
    Functions
  }

  def index(assigns, payload) do
    results =
      YearlyReports.export(assigns, payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "month" => data.month,
          "sent" => data.sent,
          "delivered" => data.delivered,
          "failed" => data.failed,
          "invalid" => data.invalid,
          "total" => data.total
        }
      end)
      |> Enum.map(fn data ->
        """
          <tr>
            <td style="text-align: center;">{{ month }}</td>
            <td style="text-align: center;">{{ sent }}</td>
            <td style="text-align: center;">{{ delivered }}</td>
            <td style="text-align: center;">{{ failed }}</td>
            <td style="text-align: center;">{{ invalid }}</td>
            <td style="text-align: center;">{{ total }}</td>

          </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/annual_statistical_report_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "Annual Reports",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end
