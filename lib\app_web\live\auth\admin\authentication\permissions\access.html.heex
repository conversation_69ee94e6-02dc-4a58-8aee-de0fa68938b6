<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  <.header>
    <%= @page_title %>
    <:subtitle>
      Department: <%= @access_role.department_role.name %> |
      Select permissions from available department permissions
    </:subtitle>
    <:actions>
      <.link
        navigate={~p"/roles/access_roles/#{@access_role.department_role_id}"}
        class="text-sm text-gray-600 hover:text-gray-900"
      >
        ← Back to Access Roles
      </.link>
    </:actions>
  </.header>

  <div class="mt-8">
    <!-- Info Banner -->
    <div class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
            <path
              fill-rule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm text-blue-700">
            You can only select permissions that have been assigned to the department role.
          </p>
        </div>
      </div>
    </div>

    <div class="mb-6">
      <div class="relative">
        <input
          type="text"
          phx-keyup="search"
          phx-debounce="300"
          value={@search_query}
          placeholder="Search permissions..."
          class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <svg
          class="absolute right-3 top-3 h-5 w-5 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>
    </div>
    <!-- Permissions List -->
    <div class="space-y-6">
      <div class="grid lg:grid-cols-4 md:grid-cols-3 sm:grid-cols-2 xs:grid-cols-1 gap-4">
        <%= if Enum.empty?(@available_permissions) do %>
          <div class="text-center py-12 bg-gray-50 rounded-lg">
            <svg
              class="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No permissions available</h3>
            <p class="mt-1 text-sm text-gray-500">
              The department role doesn't have any permissions assigned yet.
            </p>
          </div>
        <% else %>
          <%= for {service, permissions} <- filter_permissions(@available_permissions, @search_query) do %>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div class="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                  <%= service %>
                </h3>
              </div>

              <div class="divide-y divide-gray-200">
                <%= for permission <- permissions do %>
                  <div class="px-6 py-4 hover:bg-gray-50 transition-colors duration-150">
                    <label class="flex items-start cursor-pointer">
                      <input
                        type="checkbox"
                        phx-click="toggle_permission"
                        phx-value-permission_id={permission.id}
                        checked={MapSet.member?(@selected_permissions, permission.id)}
                        class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <div class="ml-3 flex-1">
                        <div class="text-sm font-medium text-gray-900">
                          <%= permission.description %>
                        </div>
                        <%= if permission.tab do %>
                          <div class="text-sm text-gray-500 mt-1">
                            Tab: <%= permission.tab %>
                          </div>
                        <% end %>
                      </div>
                    </label>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>
    <!-- Save Button -->
    <div class="mt-8 flex justify-end space-x-4">
      <.link navigate={~p"/roles/access_roles/#{@access_role.department_role_id}"}>
        <.button variant="secondary">Cancel</.button>
      </.link>
      <.button phx-click="save" disabled={Enum.empty?(@available_permissions)}>
        Save Permissions
      </.button>
    </div>
  </div>
</div>
