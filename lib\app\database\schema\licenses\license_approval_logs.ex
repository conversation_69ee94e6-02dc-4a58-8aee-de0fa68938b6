defmodule App.Licenses.LicenseApprovalLogs do
  use Ecto.Schema
  import Ecto.Changeset

  schema "license_approval_logs" do
    field :is_approved, :boolean, default: false
    field :status, :integer
    belongs_to :license_mapping, App.Licenses.LicenseMapping
    belongs_to :user, App.Accounts.User

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(approval_levels, attrs) do
    approval_levels
    |> cast(attrs, [:is_approved, :status, :license_mapping_id, :user_id])
    |> validate_required([:status, :user_id, :license_mapping_id, :is_approved])
  end
end
