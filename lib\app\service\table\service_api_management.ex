defmodule App.Service.Table.ApiManagement do
  @moduledoc false
  import AppWeb.Helps.DataTable, only: [sort: 1]
  import Ecto.Query, warn: false

  alias App.{
    Management.ApiManagement,
    Repo
  }

  @pagination [page_size: 10]

  def index(params) do
    ApiManagement
    |> compose_filter_query(params["filter"])
    |> order_by(^sort(params["order_by"]))
    |> compose_select()
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, params))
    |> (fn %{entries: entries, total_entries: _total_entries} = result ->
          formatted =
            Enum.map(entries, fn entry ->
              IO.inspect(entry.data, label: "API Management Entry")

              data =
                entry.data
                |> Map.merge(%{
                  "base_url" => entry.base_url,
                  "key" => entry.key,
                  "updated_by" => entry.updated_by
                })

              %{
                id: entry.id,
                name: entry.name,
                type: entry.type,
                status: entry.status,
                data: Jason.encode!(data)
              }
            end)

          Map.put(result, :entries, formatted)
        end).()
  end

  defp compose_filter_query(query, nil), do: query

  defp compose_filter_query(query, filter) do
    Enum.reduce(filter, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"start_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? >= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 00:00:00"
          )
        )

      {"end_date", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment(
            "? <= TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')",
            a.inserted_at,
            ^"#{value} 23:59:59"
          )
        )

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp compose_select(query) do
    select(query, [a], %{
      name: a.name,
      type: a.type,
      base_url: a.base_url,
      key: a.key,
      status: a.status,
      data: a.data,
      updated_by: a.updated_by,
      created_by: a.created_by,
      id: a.id
    })
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      fragment("lower(?) LIKE lower(?)", a.name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.base_url, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.type, ^search_term)
    )
  end

  defp sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
