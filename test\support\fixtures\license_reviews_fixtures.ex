defmodule App.LicenseReviewsFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `App.LicenseReviews` context.
  """

  @doc """
  Generate a license_review.
  """
  def license_review_fixture(attrs \\ %{}) do
    {:ok, license_review} =
      attrs
      |> Enum.into(%{
        attention_field: "some attention_field",
        attention_status: "some attention_status",
        reason: "some reason"
      })
      |> App.LicenseReviews.create_license_review()

    license_review
  end
end
