<%= if !@data_loader do %>
  <div class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Application Summary View -->
      <div class="mb-6 flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">Application Summary</h1>

        <button
          phx-click="view_details"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
        >
          View Details
        </button>
      </div>

      <div id="toBePrinted">
        <div class="bg-white shadow rounded-xl overflow-hidden border border-gray-200 mb-6">
          <div class="flex flex-col lg:flex-row">
            <div class="lg:w-full">
              <!-- Controls Bar -->
              <div class="flex justify-center items-center gap-5 p-4 bg-gray-50 border-b border-gray-200 no-print">
                <!-- Print button -->
                <button
                  type="button"
                  phx-click="export_pdf"
                  phx-value-file_name="MainLicence"
                  class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Print"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <polyline points="6 9 6 2 18 2 18 9" />
                    <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2" />
                    <rect width="12" height="8" x="6" y="14" />
                  </svg>
                </button>
                <!-- View the Approvals link -->
                <.link navigate={~p"/license/approval_report/#{@record.id}"}>
                  <button
                    type="button"
                    phx-value-file_name="MainLicence"
                    class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                    title="View the Approvals"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>

                      <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                  </button>
                </.link>
                <!-- View License Form button -->
                <button
                  type="button"
                  phx-click="view_licence_summary"
                  phx-value-id={@record.license_id}
                  class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                  title="View License Form"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                    <polyline points="14 2 14 8 20 8" /> <line x1="16" y1="13" x2="8" y2="13" />
                    <line x1="16" y1="17" x2="8" y2="17" /> <polyline points="10 9 9 9 8 9" />
                  </svg>
                </button>
                <!-- View Details button -->
                <button
                  type="button"
                  phx-click="view_details"
                  class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                  title="View Details"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <circle cx="12" cy="12" r="10" /> <line x1="12" y1="16" x2="12" y2="12" />
                    <line x1="12" y1="8" x2="12.01" y2="8" />
                  </svg>
                </button>
                <!-- Add Condition button -->
                <button
                  type="button"
                  phx-click="view_condition"
                  class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                  title="License Condition"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <circle cx="12" cy="12" r="10" /> <line x1="12" y1="8" x2="12" y2="16" />
                    <line x1="8" y1="12" x2="16" y2="12" />
                  </svg>
                </button>
              </div>
              <!-- Editor Container -->
              <div class="flex-1 p-6 bg-white" id="editor-container" phx-update="ignore">
                <div
                  id={@editors["id"]}
                  phx-hook="RichTextEditor"
                  phx-update="ignore"
                  class="relative w-full h-full border rounded-lg overflow-hidden border-gray-300 min-h-[500px]"
                >
                </div>
              </div>
              <!-- Action buttons -->
              <div class="flex items-center justify-between p-6 border-t border-brand-10 no-print">
                <.button
                  type="button"
                  onclick="history.back()"
                  class="flex items-center gap-2 px-4 py-2 rounded-md bg-brand-10 text-gray-700 hover:bg-brand-2 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M10 19l-7-7m0 0l7-7m-7 7h18"
                    />
                  </svg>
                  Back to Applications
                </.button>

                <%= if @approval_level && @record.status > 1 && !@record.condition_tracking && !@record.approved do %>
                  <div class="flex items-center gap-3">
                    <%= if @edited == "hide" do %>
                      <.button
                        type="button"
                        phx-click="edit_form"
                        phx-value-id={@editors["id"]}
                        class="flex items-center gap-2 px-5 py-2 rounded-md bg-brand-2 text-white hover:bg-brand-1 transition-colors"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                          />
                        </svg>
                        Edit
                      </.button>

                      <.button
                        type="button"
                        phx-click="approve_application"
                        phx-value-activity="decline"
                        class="flex items-center gap-2 px-5 py-2 rounded-md bg-red-500 text-white hover:bg-red-600 transition-colors"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                        Decline
                      </.button>

                      <.button
                        type="button"
                        phx-click="approve_application"
                        phx-value-activity="approve"
                        class="flex items-center gap-2 px-5 py-2 rounded-md bg-green-500 text-white hover:bg-green-600 transition-colors"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                        Approve
                      </.button>
                    <% else %>
                      <.button
                        type="button"
                        phx-click="save_edits"
                        phx-value-license_id={@record.license_id}
                        class="flex items-center gap-2 px-5 py-2 rounded-md bg-brand-10 text-white hover:bg-indigo-700 transition-colors"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                          />
                        </svg>
                        Save Changes
                      </.button>
                    <% end %>
                  </div>
                <% end %>
              </div>
              <!-- Associated editors -->
              <div
                class="flex-1 p-6 bg-white border-t border-gray-200"
                id="editor-container"
                phx-update="ignore"
              >
                <%= if !Enum.empty?(@editors["associates"]) do %>
                  <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">
                      Associated Licenses
                    </h3>

                    <%= for assoc <- @editors["associates"] do %>
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                        <div class="flex  rounded-lg p-4 bg-white border border-gray-200 cursor-pointer items-center hover:bg-gray-50  transition-all">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-5 w-5 text-brand-2"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            >
                            </path>
                          </svg>

                          <button
                            phx-value-name={assoc["name"]}
                            phx-value-assoc_id={assoc["id"]}
                            phx-click="view_assoc_id"
                            class="text-sm font-medium text-gray-700 hover:text-brand-10 transition-colors flex-1 text-left"
                          >
                            <%= assoc["name"] %>
                          </button>
                        </div>
                      </div>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>
            <!-- Right sidebar in summary view -->
            <div class="lg:w-80 p-6 border-l border-gray-200 bg-gray-50 no-print">
              <!-- Attachments Section -->
              <div class="bg-white shadow rounded-xl overflow-hidden border border-gray-200 mb-6">
                <div class="bg-gradient-to-r from-brand-10 to-brand-2 px-6 py-4">
                  <h2 class="text-lg font-bold text-white">Attachments</h2>
                </div>

                <div class="p-4 space-y-2">
                  <%= for field <- @license_data do %>
                    <%= if field.field_type == "upload" &&
                        (@role_department not in [4] || field.field_name == "pop_upload") do %>
                      <%= if is_nil(field.field_dependents) ||
                          field.dependent_selection == (if !Enum.empty?(field.field_dependents),
                            do: @record.data[List.first(field.field_dependents)]) do %>
                        <div class="flex items-center space-x-2 py-3 px-3 border-b border-gray-100 last:border-0 hover:bg-gray-50 rounded-md transition-all">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-5 w-5 text-brand-2"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>

                          <button
                            phx-value-doc_name={field.field_label}
                            phx-value-path={@field_data |> Map.get(field.field_name)}
                            phx-click="view_document"
                            class="text-sm font-medium text-gray-700 hover:text-brand-10 transition-colors flex-1 text-left"
                          >
                            <%= field.field_label %>
                          </button>
                        </div>
                      <% end %>
                    <% end %>
                  <% end %>

                  <%= if Enum.empty?(@license_data) || !Enum.any?(@license_data, fn field ->
                      field.field_type == "upload"
                    end) do %>
                    <div class="p-4 text-center text-gray-500 italic">
                      No attachments available
                    </div>
                  <% end %>
                </div>
              </div>
              <!-- Uploaded Files Section -->
              <div class="bg-white shadow rounded-xl overflow-hidden border border-gray-200">
                <div class="bg-gradient-to-r from-brand-10 to-brand-2 px-6 py-4">
                  <h2 class="text-lg font-bold text-white">Uploaded Files</h2>
                </div>

                <.simple_form
                  for={@upload_form}
                  id="card-form"
                  phx-change="validate_file"
                  phx-submit="upload"
                  class="p-4"
                >
                  <AppWeb.DocumentComponent.document_upload
                    uploads={@uploads}
                    db_files={@db_files}
                  />
                  <%= if @uploads.file.entries != [] do %>
                    <div class="flex items-center justify-center mt-4">
                      <.button
                        type="submit"
                        class="px-4 py-2 bg-brand-10 hover:bg-brand-2 text-white rounded-lg transition"
                      >
                        Upload Files
                      </.button>
                    </div>
                  <% end %>
                </.simple_form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>
<!-- Modals -->
<Model.fullscreen
  :if={@live_action in [:view_associate]}
  id="view_associate-modal"
  title="Associated License"
  show
  return_to="close_model"
>
  <iframe
    src={@associate}
    id="associate_iframe"
    title="associate"
    style="width: 100%;"
    height="700"
    name="ASSOCIATE"
  >
  </iframe>
</Model.fullscreen>

<Model.fullscreen
  :if={@live_action in [:view_document]}
  id="view_document-modal"
  title={@doc_name}
  show
  return_to="close_model"
>
  <iframe
    src={@document}
    id="document_iframe"
    title="document"
    style="width: 100%;"
    height="700"
    name="DOCUMENT"
  >
  </iframe>
</Model.fullscreen>

<Model.fullscreen
  :if={@live_action in [:view_licence_form]}
  id="view_licence-modal"
  title="License Form"
  show
  return_to="close_model"
>
  <iframe
    src={@licence_form}
    id="licence_iframe"
    title="License"
    style="width: 100%;"
    height="700"
    name="License"
  >
  </iframe>
</Model.fullscreen>

<Model.confirmation_model
  :if={@live_action == :confirm}
  show
  id="confirmation_model-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>

<Model.confirmation_with_des
  :if={@live_action == :confirm_with_des}
  show
  id="confirmation_with_des-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>

<Model.confirmation_with_comments
  :if={@live_action == :confirmation_with_comments}
  show
  id="confirmation_with_comments-modal"
  model_title={@confirmation_model_title}
  body_text={@confirmation_model_text}
  agree_function={@confirmation_model_agree}
  reject_function={@confirmation_model_reject}
  params={Jason.encode!(@confirmation_model_params)}
  icon={@confirmation_model_icon}
/>

<Model.small
  :if={@live_action == :view_conditions}
  id="user-modal"
  show
  return_to="close_model"
  title={@page_title}
>
  <.live_component
    module={AppWeb.LicenceDetailsLive.ConditionComponent}
    id={@current_user.id}
    title={@page_title}
    action={@live_action}
    record={@record}
    browser_info={@browser_info}
    current_user={@current_user}
  />
</Model.small>

<Model.fullscreen
  :if={@live_action == :assoc}
  id="user-modal"
  show
  return_to="close_model"
  title={@page_title}
>
  <.live_component
    module={AppWeb.Auth.ViewApplicationsLive.Representative}
    id="assoc{@assoc_id}"
    title={@page_title}
    assoc_id={@assoc_id}
    action={@live_action}
    current_user={@current_user}
    browser_info={@browser_info}
    live_socket_identifier={@live_socket_identifier}
  />
</Model.fullscreen>
