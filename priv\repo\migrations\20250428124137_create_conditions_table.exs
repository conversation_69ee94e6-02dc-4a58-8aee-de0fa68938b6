defmodule App.Repo.Migrations.CreateConditionsTable do
  use Ecto.Migration

  def change do
    create table(:conditions) do
      add :name, :string
      add :description, :string
      add :type, :string
      add :status, :integer, default: 1
      add :user_id, references(:tbl_users, on_delete: :nothing), null: false

      timestamps()
    end

    create table(:license_conditions_mapping) do
      add :condition_id, references(:conditions, on_delete: :nothing), null: false
      add :status, :integer, default: 0
      add :responsibility, :string
      add :comments, :string
      add :reason, :string
      add :expiring_date, :string
      add :expiring_status, :string
      add :application_id, references(:user_license_mapping, on_delete: :nothing)
      add :condition_met, :boolean, default: false
      add :added_by_id, references(:tbl_users, on_delete: :nothing)
      add :confirmed_by_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    alter table(:uploaded_files) do
      add :is_condition, :boolean, default: false
      add :condition_map_id, references(:license_conditions_mapping, on_delete: :nothing)
    end

    create index(:license_conditions_mapping, [:condition_id, :application_id])
  end
end
