defmodule App.Service.Export.PdfSmppServicesService do
  @moduledoc false

  alias App.Service.Table.ServiceSmpp

  alias App.Service.Export.Functions

  def index(assigns, payload) do
    results =
      ServiceSmpp.export(assigns, payload)
      |> List.flatten()
      |> Enum.map(fn data ->
        %{
          "service_name" => data.service_name,
          "host" => data.host,
          "system_id" => data.system_id,
          "mobile_regex" => data.mobile_regex,
          "status" => Functions.table_numeric_status(data.status)
        }
      end)
      |> Enum.map(fn data ->
        """
        <tr>
            <td style="text-align: center;">{{ service_name }}</td>
            <td style="text-align: center;">{{ host }}</td>
            <td style="text-align: center;">{{ system_id }}</td>
            <td style="text-align: center;">{{ mobile_regex }}</td>
            <td style="text-align: center;">{{ status }}</td>
        </tr>
        """
        |> :bbmustache.render(data, key_type: :binary)
      end)

    body = "<tbody>" <> Enum.join(results, "") <> "</tbody>"

    Application.app_dir(
      :app,
      "priv/static/exports/smpp_services_service.html.eex"
    )
    |> File.read!()
    |> String.replace("<tbody></tbody>", body)
    |> :bbmustache.render(
      %{
        "title" => "SMPP Services",
        "today_date" => to_string(App.Util.CustomTime.local_datetime())
      },
      key_type: :binary
    )
    |> PdfGenerator.generate_binary!()
  end
end
